import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON><PERSON>, IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class SendEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address to send the email to',
  })
  @IsEmail()
  @IsNotEmpty()
  to: string[];

  @ApiProperty({
    example: 'Hello',
    description: 'The subject of the email',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiProperty({
    example: 'Hello, this is a test email',
    description: 'The body of the email',
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address to send the email from',
    required: false,
  })
  @IsOptional()
  @IsEmail()
  from?: string;

  @ApiProperty({
    example: [],
    description: 'The email addresses to send the email to as CC',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { each: true })
  cc?: string[];

  @ApiProperty({
    example: [],
    description: 'The email addresses to send the email to as BCC',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { each: true })
  bcc?: string[];

  @ApiProperty({
    example: [],
    description: 'The email addresses to send the email to as replyTo',
    required: false,
  })
  
  @ApiProperty({ required: false, description: 'Inline or additional attachments' })
  @IsOptional()
  @IsEmail({}, { each: true })
  replyTo?: string[];
  attachments?: {
    filename: string;
    content: Buffer;
    contentType: string;
  }[];

  @ApiProperty({ required: false, description: 'S3 file URLs to be zipped and attached' })
  @IsOptional()
  @IsArray()
  fileUrls?: string[];
}
