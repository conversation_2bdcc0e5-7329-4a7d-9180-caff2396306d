import { Body, Controller, Post } from '@nestjs/common';
import { ContactUsService } from './contact-us.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ContactUsDto } from './dto/contact_us.dto';
import { ContactUs } from './contact-us.entity';
@ApiTags('Contact Us')
@Controller('contact-us')
export class ContactUsController {
  constructor(private readonly contactUsService: ContactUsService) {}

  @Post('contact')
  @ApiOperation({ summary: 'Create a new contact us query' })
  async createContactUs(
    @Body() contactUsDto: ContactUsDto,
  ): Promise<ContactUs> {
    return this.contactUsService.createContactUs(contactUsDto);
  }
}
