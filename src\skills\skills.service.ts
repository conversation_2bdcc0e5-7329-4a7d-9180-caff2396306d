import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PersonSkill } from './skills.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PersonSkillDto } from './dto/person-skill.dto';
import { UpdatepersonSkillsDto } from './dto/updatePersonSkills.dto';

@Injectable()
export class SkillsService {
  constructor(
    @InjectRepository(PersonSkill)
    private readonly skillsRepository: Repository<PersonSkill>,
  ) {}

  async createSkill(skill: PersonSkillDto): Promise<PersonSkill> {
    try {
      const newSkill = this.skillsRepository.create(skill);
      return await this.skillsRepository.save(newSkill);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error creating skill',
        error: error.message,
      });
    }
  }

  async updateSkill(skill: UpdatepersonSkillsDto): Promise<PersonSkill> {
    try {
      const existingSkill = await this.skillsRepository.findOne({
        where: { id: skill.id },
      });
      if (!existingSkill) {
        throw new NotFoundException('Skill not found');
      }

      Object.assign(existingSkill, skill);
      return await this.skillsRepository.save(existingSkill);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating skill',
        error: error.message,
      });
    }
  }

  async deleteSkill(id: number): Promise<void> {
    try {
      const skill = await this.skillsRepository.findOne({ where: { id } });
      if (!skill) {
        throw new NotFoundException('Skill not found');
      }

      await this.skillsRepository.remove(skill);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error deleting skill',
        error: error.message,
      });
    }
  }

  async getAllSkills(): Promise<PersonSkill[]> {
    try {
      return await this.skillsRepository.find();
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching skills',
        error: error.message,
      });
    }
  }

  async getSkillById(id: number): Promise<PersonSkill> {
    try {
      const skill = await this.skillsRepository.findOne({ where: { id } });
      if (!skill) {
        throw new NotFoundException('Skill not found');
      }
      return skill;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching skill',
        error: error.message,
      });
    }
  }
  async getSkillsByPersonId(personId: number): Promise<PersonSkill[]> {
    try {
      return await this.skillsRepository.find({
        where: { person: { id: personId } },
      });
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching skills by person ID',
        error: error.message,
      });
    }
  }
}
