const axios = require('axios');

const API_BASE_URL = 'https://19cxs75g-5001.inc1.devtunnels.ms';

async function testFileManagerEndpoints() {
  console.log('🧪 Testing File Manager Endpoints...\n');

  try {
    // Test 1: Create a folder
    console.log('1. Testing folder creation...');
    const createFolderResponse = await axios.post(`${API_BASE_URL}/file-manager/folders`, {
      name: 'Test Folder',
      description: 'A test folder for file manager'
    });
    console.log('✅ Folder created:', createFolderResponse.data);
    const folderId = createFolderResponse.data.id;

    // Test 2: List files (should show the created folder)
    console.log('\n2. Testing file listing...');
    const listFilesResponse = await axios.get(`${API_BASE_URL}/file-manager/files`);
    console.log('✅ Files listed:', listFilesResponse.data);

    // Test 3: Get upload URL
    console.log('\n3. Testing upload URL generation...');
    const uploadUrlResponse = await axios.post(`${API_BASE_URL}/file-manager/files/upload-url`, {
      fileName: 'test-document.pdf',
      mimeType: 'application/pdf',
      fileSize: 1024000,
      parentId: folderId
    });
    console.log('✅ Upload URL generated:', uploadUrlResponse.data);

    // Test 4: Rename folder
    console.log('\n4. Testing folder rename...');
    const renameResponse = await axios.patch(`${API_BASE_URL}/file-manager/files/${folderId}/rename`, {
      newName: 'Renamed Test Folder'
    });
    console.log('✅ Folder renamed:', renameResponse.data);

    // Test 5: Move to trash
    console.log('\n5. Testing move to trash...');
    const trashResponse = await axios.post(`${API_BASE_URL}/file-manager/files/trash`, {
      fileIds: [folderId]
    });
    console.log('✅ Moved to trash:', trashResponse.data);

    // Test 6: List files with trash
    console.log('\n6. Testing file listing with trash...');
    const listTrashResponse = await axios.get(`${API_BASE_URL}/file-manager/files?includeTrash=true`);
    console.log('✅ Files with trash listed:', listTrashResponse.data);

    console.log('\n🎉 All tests passed! File Manager is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the backend server is running on port 5000');
      console.log('   Run: npm run start:dev');
    }
  }
}

// Run the tests
testFileManagerEndpoints();
