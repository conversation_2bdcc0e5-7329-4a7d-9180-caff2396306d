import { IsString, IsNotEmpty, IsOptional, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class WhatsAppMessageDto {
  @ApiProperty({
    description: 'Recipient phone number in E.164 format, e.g., +1234567890',
    example: '+1234567890',
  })
  @IsString()
  @IsNotEmpty()
  to: string;

  @ApiProperty({
    description: 'Text message to send',
    example: 'Hello from WhatsApp!',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Optional media URL to send with the message',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsUrl()
  mediaUrl?: string;

  @ApiProperty({
    description: 'Optional media type (e.g., image, video, audio)',
    example: 'image',
    required: false,
  })
  @IsString()
  @IsOptional()
  mediaType?: string;

  @ApiProperty({
    description: 'Region for WhatsApp sender (UK or US)',
    example: 'UK',
    required: false,
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({
    description: 'Indicates if the message is a template message',
    example: true,
    required: false,
  })
  @IsOptional()
  isTemplate?: boolean;

  @ApiProperty({
    description: 'Optional template data for template messages',
    example: { name: 'John Doe', title: 'Welcome', location: 'New York' },
    required: false,
  })
  @IsOptional()
  templateData?: Record<string, any>;
}

export class WhatsAppStatusDto {
  @ApiProperty({
    description: 'Twilio Message SID',
    example: 'SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  })
  @IsString()
  @IsNotEmpty()
  messageSid: string;

  @ApiProperty({
    description: 'Status of the message',
    example: 'delivered',
  })
  @IsString()
  @IsNotEmpty()
  messageStatus: string;

  @ApiProperty({
    description: 'Optional error message if the status is failed',
    example: 'Message failed due to insufficient funds',
    required: false,
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({
    description: 'Optional template data for template messages',
    example: { name: 'John Doe', title: 'Welcome', location: 'New York' },
    required: false,
  })
  @IsOptional()
  templateData?: Record<string, any>;
  @ApiProperty({
    description: 'Indicates if the message is a template message',
    example: true,
    required: false,
  })
  @IsOptional()
  isTemplate?: boolean;

  @ApiProperty({
    description: 'Optional media URL to send with the message',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  errorMessage?: string;
}

export class SmsMessageDto {
  @ApiProperty({
    description: 'Recipient phone number in E.164 format, e.g., +1234567890',
    example: '+1234567890',
  })
  @IsString()
  @IsNotEmpty()
  to: string;

  @ApiProperty({
    description: 'Text message to send',
    example: 'Hello from SMS!',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Region for SMS sender (UK or US)',
    example: 'UK',
    required: false,
  })
  @IsString()
  @IsOptional()
  region?: string;
}
