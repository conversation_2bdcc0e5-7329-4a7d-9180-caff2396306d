import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';

import { UsersModule } from './users.module';
import { Users } from './users.entity';
import { EmailService } from '../email/email.service';

describe('Users (e2e)', () => {
  let app: INestApplication;
  let moduleRef: TestingModule;

  const mockEmailService = {
    sendEmail: jest.fn().mockResolvedValue(true),
  };

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Users],
          synchronize: true,
          logging: false,
        }),
        UsersModule,
      ],
    })
      .overrideProvider(EmailService)
      .useValue(mockEmailService)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('/users (GET)', () => {
    it('should return welcome message', () => {
      return request(app.getHttpServer())
        .get('/users')
        .expect(200)
        .expect('<h1>Welcome to the user module. Its working fine</h1>');
    });
  });

  describe('/users/register (POST)', () => {
    const validUserData = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'John',
      last_name: 'Doe',
      username: 'johndoe',
      source: 'CRM',
      role: 'USER',
      designation: 'RECRUITER',
      profile_picture: 'https://example.com/profile.jpg',
      status: 'ACTIVE',
    };

    it('should register a new user successfully', () => {
      return request(app.getHttpServer())
        .post('/users/register')
        .send(validUserData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('user');
          expect(res.body.message).toContain('User created successfully');
        });
    });

    it('should fail with invalid email', () => {
      const invalidData = { ...validUserData, email: 'invalid-email' };

      return request(app.getHttpServer())
        .post('/users/register')
        .send(invalidData)
        .expect(400);
    });

    it('should fail with missing required fields', () => {
      const incompleteData = { email: '<EMAIL>' };

      return request(app.getHttpServer())
        .post('/users/register')
        .send(incompleteData)
        .expect(400);
    });

    it('should fail when registering duplicate email', async () => {
      // First registration should succeed
      await request(app.getHttpServer())
        .post('/users/register')
        .send({ ...validUserData, email: '<EMAIL>' })
        .expect(201);

      // Second registration with same email should fail
      return request(app.getHttpServer())
        .post('/users/register')
        .send({ ...validUserData, email: '<EMAIL>' })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('already exists');
        });
    });
  });

  describe('/users/verify-email (POST)', () => {
    it('should fail with missing token', () => {
      return request(app.getHttpServer())
        .post('/users/verify-email')
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('Verification token is required');
        });
    });

    it('should fail with invalid token', () => {
      return request(app.getHttpServer())
        .post('/users/verify-email?token=invalid-token')
        .expect(400);
    });
  });

  describe('/users/resend-verification-email (POST)', () => {
    it('should fail with missing email', () => {
      return request(app.getHttpServer())
        .post('/users/resend-verification-email')
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('Email is required');
        });
    });

    it('should fail with invalid email', () => {
      return request(app.getHttpServer())
        .post('/users/resend-verification-email?email=invalid-email&source=CRM')
        .expect(400);
    });
  });

  describe('/users/login (POST)', () => {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123',
      source: 'crm',
      designation: 'RECRUITER',
    };

    beforeEach(async () => {
      // Create a user for login tests
      await request(app.getHttpServer())
        .post('/users/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          first_name: 'Login',
          last_name: 'User',
          username: 'loginuser',
          source: 'CRM',
          role: 'USER',
          designation: 'RECRUITER',
          profile_picture: 'https://example.com/profile.jpg',
          status: 'ACTIVE',
        });
    });

    it('should fail with non-existent user', () => {
      return request(app.getHttpServer())
        .post('/users/login')
        .send({ ...loginData, email: '<EMAIL>' })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('User not found');
        });
    });

    it('should fail with invalid password', () => {
      return request(app.getHttpServer())
        .post('/users/login')
        .send({ ...loginData, password: 'wrongpassword' })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('Invalid credentials');
        });
    });

    it('should send verification code on successful login', () => {
      return request(app.getHttpServer())
        .post('/users/login')
        .send(loginData)
        .expect(201)
        .expect((res) => {
          expect(res.body.message).toContain('Verification code sent');
        });
    });
  });

  describe('/users/verify (POST)', () => {
    const verifyData = {
      email: '<EMAIL>',
      verification_code: '123456',
    };

    beforeEach(async () => {
      // Create a user for verification tests
      await request(app.getHttpServer())
        .post('/users/register')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          first_name: 'Verify',
          last_name: 'User',
          username: 'verifyuser',
          source: 'CRM',
          role: 'USER',
          designation: 'RECRUITER',
          profile_picture: 'https://example.com/profile.jpg',
          status: 'ACTIVE',
        });
    });

    it('should return JWT token on successful verification', () => {
      return request(app.getHttpServer())
        .post('/users/verify')
        .send(verifyData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('token');
          expect(typeof res.body.token).toBe('string');
        });
    });

    it('should fail with missing verification code', () => {
      return request(app.getHttpServer())
        .post('/users/verify')
        .send({ email: verifyData.email })
        .expect(400);
    });
  });

  describe('/users/getAllUsers (GET)', () => {
    beforeEach(async () => {
      // Create test users
      const users = [
        {
          email: '<EMAIL>',
          password: 'password123',
          first_name: 'User',
          last_name: 'One',
          username: 'user1',
          source: 'CRM',
          role: 'USER',
          designation: 'RECRUITER',
          profile_picture: 'https://example.com/profile.jpg',
          status: 'ACTIVE',
        },
        {
          email: '<EMAIL>',
          password: 'password123',
          first_name: 'User',
          last_name: 'Two',
          username: 'user2',
          source: 'CRM',
          role: 'ADMIN',
          designation: 'PROJECT_MANAGER',
          profile_picture: 'https://example.com/profile.jpg',
          status: 'ACTIVE',
        },
      ];

      for (const user of users) {
        await request(app.getHttpServer())
          .post('/users/register')
          .send(user);
      }
    });

    it('should return all users', () => {
      return request(app.getHttpServer())
        .get('/users/getAllUsers')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });

    it('should filter users by role', () => {
      return request(app.getHttpServer())
        .get('/users/getAllUsers?role=USER')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should filter users by designation', () => {
      return request(app.getHttpServer())
        .get('/users/getAllUsers?designation=RECRUITER')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should support pagination', () => {
      return request(app.getHttpServer())
        .get('/users/getAllUsers?page=0&pageSize=1')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should support search', () => {
      return request(app.getHttpServer())
        .get('/users/getAllUsers?searchString=User')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });
  });

  describe('/users/designationBasedUsers (GET)', () => {
    it('should return users by designation', () => {
      return request(app.getHttpServer())
        .get('/users/designationBasedUsers?designation=RECRUITER')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should fail without designation parameter', () => {
      return request(app.getHttpServer())
        .get('/users/designationBasedUsers')
        .expect(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON', () => {
      return request(app.getHttpServer())
        .post('/users/register')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);
    });

    it('should handle missing Content-Type header', () => {
      return request(app.getHttpServer())
        .post('/users/register')
        .send({ email: '<EMAIL>' })
        .expect(400);
    });
  });
});
