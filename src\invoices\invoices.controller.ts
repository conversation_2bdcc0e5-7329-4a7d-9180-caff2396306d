import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { InvoicesService } from './invoices.service';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { InvoiceDto, UpdateInvoiceDto } from './dto/invoices.dto';

@Controller('invoices')
@ApiTags('Invoices')
export class InvoicesController {
  constructor(
    private readonly invoicesService: InvoicesService, // Inject the InvoicesService to handle business logic
  ) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new invoice' })
  async createInvoice(@Body() invoiceDto: InvoiceDto) {
    return await this.invoicesService.createInvoice(invoiceDto); // Call the service to create an invoice
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all invoices' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiQuery({ name: 'searchString', required: false, type: String })
  @ApiQuery({ name: 'start_date', required: false, type: String })
  @ApiQuery({ name: 'end_date', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  async getAllInvoices(
    @Query('page') page: number = 0, // Default to page 1 if not provided
    @Query('limit') pageSize: number = 10, // Default to 10 items per page if not provided
    @Query('searchString') searchString: string = '', // Default to empty string if not provided
    @Query('start_date') start_date: Date = null, // Default to empty string if not provided
    @Query('end_date') end_date: Date = null, // Default to empty string if not provided
    @Query('status') status: string, // Default to empty string if not
  ) {
    return await this.invoicesService.getAllInvoices(
      page,
      pageSize,
      searchString,
      start_date,
      end_date,
      status,
    ); // Call the service to get all invoices with pagination
  }

  @Get('invoiceStatsGroupByStatus')
  @ApiOperation({ summary: 'Get invoices statistics' })
  async getInvoicesStats() {
    return await this.invoicesService.getAllInvoicesStatGroupByStatus(); // Call the service to get invoices statistics
  }
  @Get(':id')
  @ApiOperation({ summary: 'Get an invoice by ID' })
  async getInvoiceById(@Param('id') id: number) {
    return await this.invoicesService.getInvoiceById(id); // Call the service to get an invoice by ID
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an invoice by ID' })
  async updateInvoice(
    @Param('id') id: number, // Get the ID from the URL parameter
    @Body() invoiceDto: UpdateInvoiceDto, // Get the invoice data from the request body
  ) {
    return await this.invoicesService.updateInvoice(id, invoiceDto); // Call the service to update the invoice
  }
}
