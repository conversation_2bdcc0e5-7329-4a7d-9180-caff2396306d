import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  MaxLength,
  IsArray,
} from 'class-validator';

export class RoleDto {
  @ApiPropertyOptional({
    enum: ['REGULAR', 'TRIAL', 'FIX'],
    description: 'Category of the role',
  })
  @IsOptional()
  @IsEnum(['REGULAR', 'TRIAL', 'FIX'])
  category?: string;

  @ApiPropertyOptional({
    type: Date,
    description: 'Start date of the role (if FIX category)',
  })
  @IsOptional()
  @IsDate()
  start_date?: Date;

  @ApiPropertyOptional({
    type: Date,
    description: 'End date of the role (if FIX category)',
  })
  @IsOptional()
  @IsDate()
  end_date?: Date;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of candidates required',
  })
  @IsOptional()
  @IsInt()
  candidates_required?: number;

  @ApiPropertyOptional({
    type: Boolean,
    description: 'Indicates if it is a credit-based role',
  })
  @IsOptional()
  @IsBoolean()
  is_credit?: boolean;

  @ApiPropertyOptional({ type: Number, description: 'Role number' })
  @IsOptional()
  @IsInt()
  role_number?: number;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Title of the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Postal code for the role location',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  postal_code?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Client number associated with the role',
  })
  @IsOptional()
  client_number?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Country for the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  country?: string;

  @ApiPropertyOptional({
    enum: ['LOW', 'MEDIUM', 'HIGH'],
    description: 'Priority level of the role',
  })
  @IsOptional()
  @IsEnum(['LOW', 'MEDIUM', 'HIGH'])
  priority?: string;

  @ApiPropertyOptional({
    enum: ['FIXED', 'VARIABLE'],
    description: 'Salary payment type',
  })
  @IsOptional()
  @IsEnum(['FIXED', 'VARIABLE'])
  salary_payment_type?: string;

  @ApiPropertyOptional({ type: String, description: 'Minimum salary offered' })
  @IsOptional()
  @IsString()
  salary_min?: string;

  @ApiPropertyOptional({ type: String, description: 'Maximum salary offered' })
  @IsOptional()
  @IsString()
  salary_max?: string;

  @ApiPropertyOptional({ type: String, description: 'Fixed salary amount' })
  @IsOptional()
  @IsString()
  salary_fixed?: string;

  @ApiPropertyOptional({
    enum: ['USD', 'GBP'],
    description: 'Currency for salary',
  })
  @IsOptional()
  @IsEnum(['USD', 'GBP'])
  salary_currency?: string;

  @ApiPropertyOptional({
    enum: [
      'HOURLY',
      'DAILY',
      'WEEKLY',
      'FORTNIGHT',
      'MONTHLY',
      'BIANNUALLY',
      'ANNUALY',
    ],
    description: 'Salary type based on time period',
  })
  @IsOptional()
  @IsEnum([
    'HOURLY',
    'DAILY',
    'WEEKLY',
    'FORTNIGHT',
    'MONTHLY',
    'BIANNUALLY',
    'ANNUALY',
  ])
  salary_type?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Current status of the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  current_status?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Locations for the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  locations?: string;

  @ApiPropertyOptional({
    type: 'enum',
    enum: [
      'LESS_THAN_MONTH',
      '1MONTH',
      '3MONTHS',
      '6MONTHS',
      '9MONTHS',
      '12MONTHS',
      '15MONTHS',
      '18MONTHS',
      '24MONTHS',
      '36MONTHS',
      '48MONTHS',
      '60MONTHS',
      'MORE_THAN_60MONTHS',
    ],
    description: 'Months Back',
  })
  @IsOptional()
  @IsEnum([
    'LESS_THAN_MONTH',
    '1MONTH',
    '3MONTHS',
    '6MONTHS',
    '9MONTHS',
    '12MONTHS',
    '15MONTHS',
    '18MONTHS',
    '24MONTHS',
    '36MONTHS',
    '48MONTHS',
    '60MONTHS',
    'MORE_THAN_60MONTHS',
  ])
  months_back?: string;

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Industry for the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  industry?: string;

  @ApiPropertyOptional({
    type: [String],
    description: 'Relevant titles for the role',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  relevant_titles?: string[];

  @ApiPropertyOptional({
    type: String,
    maxLength: 255,
    description: 'Radius in miles for the role',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  radius_miles?: string;

  @ApiPropertyOptional({
    type: [String],
    description: 'Attachments for the role',
  })
  @IsOptional()
  @IsString({ each: true })
  attachments?: string;
  
  @ApiPropertyOptional({
    type: String,
    description: 'User ID who created the role',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'Person ID associated with the role',
  })
  @IsOptional()
  @IsString()
  personId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'ACM User ID who added the role',
  })
  @IsOptional()
  @IsString()
  acmUserId?: string;

  @ApiPropertyOptional({
    type: String,
    description: 'BD User ID who added the role',
  })
  @IsOptional()
  @IsString()
  bdUserId?: string;

  @ApiPropertyOptional({
    type: Number,
    description: 'Service ID associated with the role',
  })
  @IsOptional()
  @IsInt()
  serviceId?: number;
}
