import { Test, TestingModule } from '@nestjs/testing';
import { FocusPointService } from './focus-point.service';

describe('FocusPointService', () => {
  let service: FocusPointService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FocusPointService],
    }).compile();

    service = module.get<FocusPointService>(FocusPointService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
