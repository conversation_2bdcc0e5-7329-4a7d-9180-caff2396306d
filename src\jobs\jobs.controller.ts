import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JobsService } from './jobs.service';
import {
  CreateJobDto,
  GetAllJobsDto,
  searchJobPostsOfCompanyDto,
} from './dto/createJob.dto';
import { UpdateJobDto } from './dto/updateJob.dto';

@Controller('jobs')
@ApiTags('jobs')
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new job' })
  async createJob(@Body() body: CreateJobDto) {
    return this.jobsService.createJob(body);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a job' })
  async updateJob(@Param('id') id: number, @Body() body: UpdateJobDto) {
    return this.jobsService.updateJob(id, body);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all jobs' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'searchString',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortingOrder',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: String,
  })
  async findAll(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('searchString') searchString?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortingOrder') sortingOrder?: string,
    @Query('userId') userId?: string,
  ) {
    return this.jobsService.findAll(
      page,
      pageSize,
      searchString,
      sortingOrder,
      sortBy,
      userId,
    );
  }
  @Get('all-candidate-jobs')
  @ApiOperation({ summary: 'Get all jobs' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'searchString',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sortingOrder',
    required: false,
    type: String,
  })
  async findAllGeenral(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('searchString') searchString?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortingOrder') sortingOrder?: string,
  ) {
    return this.jobsService.findAllGeneral(
      page,
      pageSize,
      searchString,
      sortingOrder,
      sortBy,
    );
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Get a job by id' })
  async findOne(@Param('id') id: number) {
    return this.jobsService.findOne(id);
  }

  @Get('find/title')
  @ApiOperation({ summary: 'Get a job by title' })
  async findJobByTitle(@Query('title') title: string) {
    return this.jobsService.findJobByTitle(title);
  }

  @Get('find/personId')
  @ApiOperation({ summary: 'Get a job by personId' })
  async findJobByPersonId(@Query('personId') personId: number) {
    return this.jobsService.findJobByPersonId(personId);
  }

  @Get('find/location')
  @ApiOperation({ summary: 'Get a job by location' })
  async findJobByLocation(@Query('job_location') job_location: string) {
    return this.jobsService.findJobByLocation(job_location);
  }

  @Get('find/type')
  @ApiOperation({ summary: 'Get a job by type' })
  async findJobByType(@Query('job_type') job_type: string) {
    return this.jobsService.findJobByType(job_type);
  }

  @Get('find/companyId')
  @ApiOperation({ summary: 'Get a job by companyId' })
  async findJobByCompanyId(@Query('companyId') companyId: number) {
    return this.jobsService.findJobByCompanyId(companyId);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a job' })
  async deleteJob(@Param('id') id: number) {
    return this.jobsService.deleteJob(id);
  }

  @Get('findJobWithApllicants/:userId')
  @ApiOperation({ summary: 'Get a job by jobId' })
  async findJobByJobId(@Query('userId') userId: string) {
    return this.jobsService.findAllJobApplicantsByUserId(userId);
  }

  @Get('getJobPostsByCompanyId')
  @ApiOperation({ summary: 'Get a job by jobId' })
  async getJobPostsByCompanyId(@Query('companyId') companyId: string) {
    return this.jobsService.getJobPostsByCompanyId(companyId);
  }

  @Get('getAllJobs')
  @ApiOperation({ summary: 'Get all jobs' })
  async getAllJobs(@Query() queryParams: GetAllJobsDto) {
    return this.jobsService.getAllJobs(queryParams);
  }

  @Get('searchJobPostsOfCompany')
  @ApiOperation({ summary: 'search job posts of company' })
  async searchJobPostsOfCompany(
    @Query() queryParams: searchJobPostsOfCompanyDto,
  ) {
    return this.jobsService.searchJobPostsOfCompany(queryParams);
  }
  @Get('searchJobPosts')
  @ApiOperation({ summary: 'search job posts' })
  async searchJobPosts(@Query('searchTerm') searchTerm: string) {
    return this.jobsService.searchJobPosts(searchTerm);
  }
}
