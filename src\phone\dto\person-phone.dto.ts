import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsPhoneNumber } from 'class-validator';

export enum PersonPhoneType {
    PERSONAL = 'PERSONAL',
    BUSINESS = 'BUSINESS',
  }
  
export class PersonPhoneDto {
  @ApiProperty({
    example: '+1234567890',
    description: 'The phone number of the person',
  })
  @IsPhoneNumber(null)
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty({
    example: 'PERSONAL',
    description: 'Type of phone number (PERSONAL or BUSINESS)',
    enum: PersonPhoneType,
  })
  @IsEnum(PersonPhoneType)
  phone_type: PersonPhoneType;
}
