import { <PERSON>tity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsEmail, IsOptional, IsString, IsBoolean, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

@Entity('job_alerts')
export class JobAlert {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  email: string;

  @Column({ type: 'text', nullable: true })
  keywords: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  country: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  city: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  jobType: string; // e.g., Full-time, Part-time, Remote

  @Column({ type: 'boolean', default: true })
  is_active: boolean; // Whether the job alert is active

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

export class CreateJobAlertDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address for receiving job alerts',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'Software Engineer, Remote',
    description: 'Keywords for job matching',
    required: false,
  })
  @IsOptional()
  @IsString()
  keywords?: string;

  @ApiProperty({
    example: 'USA',
    description: 'Country of job preference',
    required: false,
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    example: 'New York',
    description: 'City of job preference',
    required: false,
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({
    example: ['REMOTE', 'ONSITE', 'HYBRID'],
    description: 'Preferred job type',
    required: false,
    type: 'enum',
    enum: ['REMOTE', 'ONSITE', 'HYBRID'],
    enumName: 'JobTypeEnum',
  })
  @IsOptional()
  @IsString()
  @IsEnum(['REMOTE', 'ONSITE', 'HYBRID'])
  job_type?: string;

  @ApiProperty({
    example: true,
    description: 'Status of the job alert subscription',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}