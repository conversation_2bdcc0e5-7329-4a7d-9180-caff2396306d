import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('company_scrapper_control')
export class CompanyScrapperControl {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  countryId: number;

  @Column({ nullable: true })
  sectorId: number;

  @Column({
    nullable: true,
  })
  company_source: string;

  @Column({ nullable: true })
  is_default: string;
}
