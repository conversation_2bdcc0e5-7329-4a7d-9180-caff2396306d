import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsNumber, IsString } from 'class-validator';

export class RenewClientDto {
  @ApiProperty()
  @IsNumber()
  client_number: number;

  @ApiProperty({
    type: 'enum',
    enum: ['FREE', 'MONTHLY', 'ANNU<PERSON>LY', 'ADHOC'],
    default: 'FREE',
  })
  @IsEnum(['FREE', 'MONTHLY', 'ANNUALLY', 'ADHOC'])
  subscription_type: string;

  @ApiProperty({
    type: 'date',
    description: 'Date when the subscription starts',
  })
  @IsDate()
  subscription_start_date: Date;

  @ApiProperty({
    type: 'date',
    description: 'Date when the subscription ends',
  })
  @IsDate()
  subscription_end_date: Date;

  @ApiProperty({
    type: 'date',
    description: 'Date when the reminder is set',
  })
  @IsDate()
  reminder_date: Date;

  @ApiProperty({
    type: 'number',
    description: 'Amount paid for the subscription',
  })
  @IsNumber()
  amount_paid: number;

  @IsDate()
  @ApiProperty({
    type: 'date',
    description: 'Date when the payment was made',
  })
  payment_date: Date;

  @ApiProperty({
    type: 'number',
    description: 'Number of credits available',
  })
  @IsNumber()
  credits: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of credits per day',
  })
  @IsNumber()
  credits_per_day: number;

  @ApiProperty({
    type: 'number',
    description: 'Number of credits used',
  })
  @IsNumber()
  credits_used: number;
}
