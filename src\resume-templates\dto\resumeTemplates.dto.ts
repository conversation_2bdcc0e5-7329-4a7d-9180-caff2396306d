import { IsNotEmpty, <PERSON>Optional, IsString, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ResumeTemplateDto {
  @ApiProperty({ 
    description: 'Name of the resume template',
    examples: ['Modern Resume', 'Creative Resume', 'Simple Resume']
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ 
    description: 'HTML structure of the template with placeholders',
    examples: [
      "<h1>{{name}}</h1><p>Email: {{email}}</p><p>Phone: {{phone}}</p>",
      "<div style='background-color: #f8f9fa;'><h1>{{name}}</h1><p>{{email}}</p></div>",
      "<header style='background: #ff6f61;'><h1>{{name}}</h1></header><section><p>{{summary}}</p></section>"
    ]
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiPropertyOptional({ 
    description: 'Optional style information as a JSON object',
    examples: [
      { "fontFamily": "Arial, sans-serif", "color": "#333" },
      { "backgroundColor": "#f8f9fa", "fontSize": "16px" },
      { "headerBackground": "#ff6f61", "textColor": "#000" }
    ]
  })
  @IsOptional()
  @IsObject()
  styles?: Record<string, any>;
}
