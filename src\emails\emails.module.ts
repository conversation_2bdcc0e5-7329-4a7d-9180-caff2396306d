import { Modu<PERSON> } from '@nestjs/common';
import { PersonEmailService } from './emails.service';
import { PersonEmailController } from './emails.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PersonEmail } from './emails.entity';
import { People } from 'src/people/people.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PersonEmail, People])],
  providers: [PersonEmailService],
  controllers: [PersonEmailController],
})
export class EmailsModule {}
