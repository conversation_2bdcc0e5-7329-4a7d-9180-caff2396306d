import { Body, Controller, Delete, Get, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ServiceService } from './service.service';
import { ServiceDto } from './dto/service.dto';
import { UpdateServiceDTO } from './dto/updateService.dto';

@ApiTags('Services we offer')
@Controller('service')
export class ServiceController {
  constructor(private readonly serviceService: ServiceService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create Service' })
  async createService(@Body() createServiceDTO: ServiceDto) {
    return this.serviceService.createService(createServiceDTO);
  }

  @Put('update')
  @ApiOperation({ summary: 'Update Service' })
  async updateService(@Body() updateServiceDTO: UpdateServiceDTO) {
    const {
      id,
      name,
      description,
      identity_number_start,
      identity_number_end,
      trial_number_fixed,
      status,
    } = updateServiceDTO;
    return this.serviceService.updateService(
      id,
      name,
      description,
      identity_number_start,
      identity_number_end,
      trial_number_fixed,
      status,
    );
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete Service' })
  async deleteService(id: number) {
    return this.serviceService.deleteService(id);
  }

  @Get('getAll')
  @ApiOperation({ summary: 'Get All Services' })
  async getAllServices() {
    return this.serviceService.getAllServices();
  }

  @Get('get/:id')
  @ApiOperation({ summary: 'Get Service by ID' })
  async getServiceById(id: number) {
    return this.serviceService.getServiceById(id);
  }

  @Get('getByName/:name')
  @ApiOperation({ summary: 'Get Service by Name' })
  async getServiceByName(name: string) {
    return this.serviceService.getServiceByName(name);
  }

  @Get('getByStatus/:status')
  @ApiOperation({ summary: 'Get Service by Status' })
  async getServiceByStatus(status: string) {
    return this.serviceService.getServiceByStatus(status);
  }

  @Get('getByRange/:start/:end')
  @ApiOperation({ summary: 'Get Service by Range' })
  async getServiceByRange(start: number, end: number) {
    return this.serviceService.getServiceByRange(start, end);
  }
}
