import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MailBox } from './mailBox.entity';
import { DeepPartial, In, Repository } from 'typeorm';
import { MailBoxDto } from './dto/mailBox.dto';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';

@Injectable()
export class MailBoxService {
  constructor(
    @InjectRepository(MailBox)
    private readonly mailBoxRepository: Repository<MailBox>,
    @InjectRepository(Users)
    private readonly userRepository: Repository<Users>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
  ) {}

  async create(mailBox: MailBoxDto): Promise<MailBox> {
    try {
      const newMailBox = this.mailBoxRepository.create({
        ...mailBox,
        type: mailBox.type as DeepPartial<MailBox['type']>,
      });
      return await this.mailBoxRepository.save(newMailBox);
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error creating mailbox');
    }
  }

  async findAll(
    page: number,
    pageSize: number,
    searchString: string,
  ): Promise<MailBox[]> {
    const query = this.mailBoxRepository.createQueryBuilder('mailbox');
    if (searchString) {
      query.where('mailbox.name LIKE :name', { name: `%${searchString}%` });
    }
    query.skip((page - 1) * pageSize).take(pageSize);
    return await query.getMany();
  }

  async findById(id: number): Promise<MailBox> {
    const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
    if (!mailBox) {
      throw new NotFoundException('Mailbox not found');
    }
    return mailBox;
  }

  async update(id: number, mailBox: MailBoxDto): Promise<MailBox> {
    const existingMailBox = await this.findById(id);
    if (!existingMailBox) {
      throw new NotFoundException('Mailbox not found');
    }
    const updatedMailBox = {
      ...existingMailBox,
      ...mailBox,
      type: mailBox.type as DeepPartial<MailBox['type']>,
    };
    return await this.mailBoxRepository.save(updatedMailBox);
  }

  async delete(id: number): Promise<void> {
    const mailBox = await this.findById(id);
    if (!mailBox) {
      throw new NotFoundException('Mailbox not found');
    }
    await this.mailBoxRepository.remove(mailBox);
  }

  async deleteAll(): Promise<void> {
    const mailBoxes = await this.mailBoxRepository.find();
    if (mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found');
    }
    await this.mailBoxRepository.remove(mailBoxes);
  }
  async findByType(type: string): Promise<MailBox[]> {
    const mailBoxes = await this.mailBoxRepository.find({
      where: { type: type as MailBox['type'] },
    });
    if (!mailBoxes || mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found for this type');
    }
    return mailBoxes;
  }

  async findByEmail(
    email?: string,
    type?: string,
    category?: string,
    pageSize?: string,
  ): Promise<MailBox[]> {
    const whereCondition: any = {};

    if (type) {
      whereCondition.type = type as MailBox['type'];
    }

    if (email) {
      const searchField =
        type?.toUpperCase() === 'INBOX'
          ? 'recipient'
          : type?.toUpperCase() === 'SENT'
            ? 'sender'
            : 'email';
      whereCondition[searchField] = email;
    }

    if (category) {
      whereCondition.category = category;
    }

    const findOptions: any = {
      where: whereCondition,
      order: { created_at: 'DESC' },
    };

    if (pageSize) {
      findOptions.take = parseInt(pageSize);
      findOptions.skip = 0;
    }

    const mailBoxes = await this.mailBoxRepository.find(findOptions);

    if (!mailBoxes || mailBoxes.length === 0) {
      return [];
    }

    return mailBoxes;
  }

  async findByDate(date: string): Promise<MailBox[]> {
    const mailBoxes = await this.mailBoxRepository.find({ where: { date } });
    if (!mailBoxes || mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found for this date');
    }
    return mailBoxes;
  }

  async findBySender(sender: string): Promise<MailBox[]> {
    const mailBoxes = await this.mailBoxRepository.find({ where: { sender } });
    if (!mailBoxes || mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found for this sender');
    }
    return mailBoxes;
  }

  async findByRecipient(recipient: string): Promise<MailBox[]> {
    const mailBoxes = await this.mailBoxRepository.find({
      where: { recipient },
    });
    if (!mailBoxes || mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found for this recipient');
    }
    return mailBoxes;
  }

  async findBySenders(senders: string[]): Promise<MailBox[]> {
    const mailBoxes = await this.mailBoxRepository.find({
      where: { sender: In(senders) },
    });
    if (!mailBoxes || mailBoxes.length === 0) {
      throw new NotFoundException('No mailboxes found for these senders');
    }
    return mailBoxes;
  }

  async updateReadStatusById(id: number): Promise<MailBox> {
    const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
    if (!mailBox) {
      throw new NotFoundException('Mailbox not found');
    }
    mailBox.read_by_user = true;
    return await this.mailBoxRepository.save(mailBox);
  }

  async updateAssignEmailToBd(
    id: number,
    assigneeId: string,
  ): Promise<MailBox> {
    try {
      // find if this user exists in the database
      const user = await this.userRepository.findOne({
        where: { id: assigneeId },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const mailBox = await this.mailBoxRepository.findOne({ where: { id } });
      if (!mailBox) {
        throw new NotFoundException('Mailbox not found');
      }
      mailBox.assigneeId = assigneeId;
      mailBox.assignee = user;

      const updatedMailBox = await this.mailBoxRepository.save(mailBox);
      return updatedMailBox;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error assigning email to user');
    }
  }

  async getMyTrialLeads(userId: string, pageSize?: string): Promise<MailBox[]> {
    try {
      const findOptions: any = {
        where: { assigneeId: userId },
        order: { created_at: 'DESC' },
      };

      if (pageSize) {
        findOptions.take = parseInt(pageSize);
        findOptions.skip = 0;
      }

      const mailBoxes = await this.mailBoxRepository.find(findOptions);
      if (!mailBoxes || mailBoxes.length === 0) {
        return [];
      }
      return mailBoxes;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error fetching trial leads');
    }
  }

  async updateProspectStatus(
    email: string,
    trialStatus: string,
  ): Promise<People> {
    try {
      // find if this email exists in the database
      const personEmail = await this.personEmailRepository.findOne({
        where: { email },
      });
      if (!personEmail) {
        throw new NotFoundException('Email not found');
      }
      const person = await this.peopleRepository.findOne({
        where: { id: personEmail.personId },
      });
      if (!person) {
        throw new NotFoundException('Person not found');
      }
      // update person prospect status
      person.prospect_status = trialStatus as People['prospect_status'];

      const updatedPerson = await this.peopleRepository.save(person);
      return updatedPerson;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error updating trial status');
    }
  }

  async saveAsDraft(
    email: string,
    type: string,
    sender: string,
    recipient: string,
    subject?: string,
    body?: string,
    attachments?: string[],
    cc?: string[],
    bcc?: string[],
    reply_to?: string,
  ): Promise<MailBox> {
    try {
      const mailBox = this.mailBoxRepository.create({
        email,
        type: type as MailBox['type'],
        sender,
        recipient,
        subject,
        message: body,
        attachment: attachments ? attachments.join(',') : null,
        cc: cc ? cc.join(',') : null,
        bcc: bcc ? bcc.join(',') : null,
        reply_to,
      });
      return await this.mailBoxRepository.save(mailBox);
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error saving draft email');
    }
  }

  async findDraftsByEmail(email: string): Promise<MailBox[]> {
    try {
      const mailBoxes = await this.mailBoxRepository.find({
        where: { email, type: 'DRAFT' },
      });
      if (!mailBoxes || mailBoxes.length === 0) {
        return []; // Return an empty array if no mailboxes are found
      }
      return mailBoxes;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error fetching draft emails');
    }
  }

  async scheduleEmail(
    email: string,
    type: string,
    sender: string,
    recipient: string,
    subject?: string,
    body?: string,
    attachments?: string[],
    cc?: string[],
    bcc?: string[],
    reply_to?: string,
    schedule_date?: Date,
  ): Promise<MailBox> {
    try {
      const mailBox = this.mailBoxRepository.create({
        email,
        type: type as MailBox['type'],
        sender,
        recipient,
        subject,
        message: body,
        attachment: attachments ? attachments.join(',') : null,
        cc: cc ? cc.join(',') : null,
        bcc: bcc ? bcc.join(',') : null,
        reply_to,
        schedule_date,
      });
      return await this.mailBoxRepository.save(mailBox);
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error scheduling email');
    }
  }

  async findScheduledEmailsByEmail(email: string): Promise<MailBox[]> {
    try {
      const mailBoxes = await this.mailBoxRepository.find({
        where: { email, type: 'SCHEDULED' },
      });
      if (!mailBoxes || mailBoxes.length === 0) {
        return []; // Return an empty array if no mailboxes are found
      }
      return mailBoxes;
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException('Error fetching scheduled emails');
    }
  }
}
