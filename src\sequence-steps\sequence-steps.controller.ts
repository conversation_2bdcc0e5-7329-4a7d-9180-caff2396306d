import { Body, Controller, Delete, Get, Post, Put } from '@nestjs/common';
import { SequenceStepsService } from './sequence-steps.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { SequenceStepsDto } from './dto/sequenceSteps.dto';
import { UpdateSequenceStepDto } from './dto/updateSequenceStep.dto';

@ApiTags('SequenceSteps')
@Controller('sequence-steps')
export class SequenceStepsController {
  constructor(
    private readonly sequenceStepsService: SequenceStepsService, // Corrected spelling of 'private'
  ) {}

  @ApiOperation({ summary: 'Create a new sequence step' })
  @Post('create')
  async createSequenceStep(
    @Body() sequenceStepsDto: SequenceStepsDto, // Replace 'any' with the actual DTO type
  ): Promise<any> {
    // Replace 'any' with the actual return type
    return this.sequenceStepsService.createSequenceSteps(sequenceStepsDto);
  }

  @ApiOperation({ summary: 'Get all sequence steps' })
  @Get('get-all')
  async getAllSequenceSteps(): Promise<any[]> {
    // Replace 'any' with the actual return type
    return this.sequenceStepsService.getSequenceSteps();
  }

  @ApiOperation({ summary: 'Get a sequence step by ID' })
  @Get(':id')
  async getSequenceStepById(id: number): Promise<any> {
    // Replace 'any' with the actual return type
    return this.sequenceStepsService.getSequenceStepsById(id);
  }

  @ApiOperation({ summary: 'Update a sequence step by ID' })
  @Put(':id')
  async updateSequenceStep(
    id: number,
    sequenceStepsDto: UpdateSequenceStepDto, // Replace 'any' with the actual DTO type
  ): Promise<any> {
    // Replace 'any' with the actual return type
    return this.sequenceStepsService.updateSequenceSteps(id, sequenceStepsDto);
  }

  @ApiOperation({ summary: 'Delete a sequence step by ID' })
  @Delete(':id')
  async deleteSequenceStep(id: number): Promise<void> {
    // Replace 'void' with the actual return type if needed
    return this.sequenceStepsService.deleteSequenceSteps(id);
  }
}
