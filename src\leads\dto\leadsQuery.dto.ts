import { IsOptional } from 'class-validator';

export class GetMarketingEmailsDto {
  @IsOptional()
  startDate?: string;

  @IsOptional()
  endDate?: string;

  @IsOptional()
  searchString?: string;

  @IsOptional()
  sector_id?: string;

  @IsOptional()
  page?: string;

  @IsOptional()
  pageSize?: string;
}

export class GetCountryAndSectorWiseLeadsStatsDto {
  @IsOptional()
  country_id?: string;
  @IsOptional()
  sector_id?: string;
  @IsOptional()
  from_date?: string;
  @IsOptional()
  to_date?: string;
  @IsOptional()
  user_id?: string;
  @IsOptional()
  industry?: string;
}

export class GetCountryAndSectorWisePersonsDto {
  @IsOptional()
  country_id?: string;

  @IsOptional()
  sector_id?: string;

  @IsOptional()
  from_date?: string;

  @IsOptional()
  to_date?: string;

  @IsOptional()
  user_id?: string;

  @IsOptional()
  industry?: string;

  @IsOptional()
  page?: string;

  @IsOptional()
  size?: string;

  @IsOptional()
  selectedFilter?: string;
}

export class GetTeamAssignedPersonsStatsDto {
  @IsOptional()
  user_id?: string;
  @IsOptional()
  from_date?: string;
  @IsOptional()
  to_date?: string;
  @IsOptional()
  country_id?: string;
  @IsOptional()
  sector_id?: string;
}

export class GetUserWorkReportDto {
  @IsOptional()
  user_id?: string;
  @IsOptional()
  from_date?: string;
  @IsOptional()
  to_date?: string;
  @IsOptional()
  country_id?: string;
  @IsOptional()
  sector_id?: string;
}

export class GetRegionWiseLeadsContactStatsDto {
  @IsOptional()
  startDate?: string;

  @IsOptional()
  endDate?: string;
}

export class GetDetailLeadReportsDto {
  @IsOptional()
  startDate?: string;

  @IsOptional()
  endDate?: string;
}

export class GetPersonWiseLeadsStatsDto {
  @IsOptional()
  user_id?: string;

  @IsOptional()
  from_date?: string;

  @IsOptional()
  to_date?: string;

  @IsOptional()
  country_id?: string;

  @IsOptional()
  sector_id?: string;

  @IsOptional()
  industry?: string;
}
