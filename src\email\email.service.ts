import { Injectable } from '@nestjs/common';
import {
  SESClient,
  SendEmailCommand,
  SendRawEmailCommand,
} from '@aws-sdk/client-ses';
import { SendEmailDto } from './dto/sendEmail.dto';
import * as nodemailer from 'nodemailer';
import * as MailComposer from 'nodemailer/lib/mail-composer';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';
import { InjectRepository } from '@nestjs/typeorm';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { Repository } from 'typeorm';

@Injectable()
export class EmailService {
  private sesClient: SESClient;

  constructor(
    private readonly s3bucketService: S3bucketService,
    @InjectRepository(MailBox)
    private readonly mailBoxRepository: Repository<MailBox>,
  ) {
    this.sesClient = new SESClient({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string,
      },
    });
  }

  async sendEmail(sendEmailDto: SendEmailDto): Promise<void> {
    console.log('sendEmailDto', sendEmailDto);
    const {
      to,
      subject,
      body,
      from,
      cc = [],
      bcc = [],
      replyTo = [],
    } = sendEmailDto;

    const params = {
      Source: `Ultimate Outsourcing <${from || process.env.DEFAULT_FROM_EMAIL}>`,
      Destination: {
        ToAddresses: to instanceof Array ? to : [to], // Ensure `to` is an array
        CcAddresses: cc,
        BccAddresses: bcc,
      },
      Message: {
        Subject: { Data: subject },
        Body: {
          Html: { Data: body },
        },
      },
      ReplyToAddresses: replyTo,
    };

    console.log(params);

    try {
      await this.sesClient.send(new SendEmailCommand(params));
      console.log(
        `Email sent successfully to: ${to instanceof Array ? to.join(', ') : to}`,
      );
      const mailBoxEntry = this.mailBoxRepository.create({
        name: null,
        email: from || process.env.DEFAULT_FROM_EMAIL,
        type: 'SENT',
        subject: subject,
        message: body,
        attachment: null,
        cc: cc.length ? cc.join(',') : null,
        bcc: bcc.length ? bcc.join(',') : null,
        reply_to: replyTo.length ? replyTo.join(',') : null,
        sender: from || process.env.DEFAULT_FROM_EMAIL,
        recipient: Array.isArray(to) ? to.join(',') : to,
        date: new Date().toISOString(),
        reason: null,
        category: null,
        replacement_email: null,
        read_by_user: false,
        read_by_system: false,
        schedule_date: null,
        assignee: null,
        assigneeId: null,
      });

      await this.mailBoxRepository.save(mailBoxEntry);
      return;
    } catch (error) {
      console.error('Email sending failed:', error);
      throw error;
    }
  }

  async sendEmailWithAttachmentsOld(
    sendEmailDto: SendEmailDto & {
      attachments: { filename: string; content: Buffer; contentType: string }[];
    },
  ): Promise<void> {
    const {
      to,
      subject,
      body,
      from,
      cc = [],
      bcc = [],
      replyTo = [],
      attachments = [],
    } = sendEmailDto;
    console.log('jkshfsfsdfsgsdfg', attachments);
    const mail = new MailComposer({
      from: `Ultimate Outsourcing <${from || process.env.DEFAULT_FROM_EMAIL}>`,
      to,
      cc,
      bcc,
      replyTo,
      subject,
      html: body,
      attachments,
    });

    try {
      const message = await new Promise<Buffer>((resolve, reject) => {
        mail.compile().build((err: Error | null, msg: Buffer) => {
          if (err) reject(err);
          else resolve(msg);
        });
      });

      const rawMessage = {
        RawMessage: {
          Data: message,
        },
      };

      await this.sesClient.send(new SendRawEmailCommand(rawMessage));
      console.log(
        `Email with attachments sent successfully to: ${Array.isArray(to) ? to.join(', ') : to}`,
      );
    } catch (error) {
      console.error('Email with attachments failed:', error);
      throw error;
    }
  }

  async sendEmailWithAttachments(sendEmailDto: SendEmailDto): Promise<void> {
    const {
      to = [],
      subject,
      body,
      from,
      cc = [],
      bcc = [],
      replyTo = [],
      attachments = [],
      fileUrls = [],
    } = sendEmailDto;

    const normalizedTo = Array.isArray(to) ? to : [to];

    const finalAttachments = [...attachments];

    if (fileUrls.length > 0) {
      console.log('Generating ZIP from S3 URLs:', fileUrls);
      try {
        const zip = await this.s3bucketService.zipFilesAsAttachment(fileUrls);
        finalAttachments.push({
          filename: zip.filename,
          content: zip.content,
          contentType: zip.contentType,
        });
        console.log('ZIP file generated from S3 URLs:', zip);
      } catch (zipError) {
        console.error('Failed to generate ZIP from S3 files:', zipError);
        throw new Error('Could not attach ZIP file from S3 URLs');
      }
    }

    const mail = new MailComposer({
      from: `Ultimate Outsourcing <${from || process.env.DEFAULT_FROM_EMAIL}>`,
      to: normalizedTo,
      cc,
      bcc,
      replyTo,
      subject,
      html: body,
      attachments: finalAttachments,
    });

    try {
      const message = await new Promise<Buffer>((resolve, reject) => {
        mail.compile().build((err: Error | null, msg: Buffer) => {
          if (err) reject(err);
          else resolve(msg);
        });
      });

      const mailBoxEntry = this.mailBoxRepository.create({
        name: null,
        email: from || process.env.DEFAULT_FROM_EMAIL,
        type: 'SENT',
        subject: subject,
        message: body,
        attachment: fileUrls.length ? fileUrls.join(',') : null,
        cc: cc.length ? cc.join(',') : null,
        bcc: bcc.length ? bcc.join(',') : null,
        reply_to: replyTo.length ? replyTo.join(',') : null,
        sender: from || process.env.DEFAULT_FROM_EMAIL,
        recipient: Array.isArray(to) ? to.join(',') : to,
        date: new Date().toISOString(),
        reason: null,
        category: null,
        replacement_email: null,
        read_by_user: false,
        read_by_system: false,
        schedule_date: null,
        assignee: null,
        assigneeId: null,
      });

      await this.mailBoxRepository.save(mailBoxEntry);

      await this.sesClient.send(
        new SendRawEmailCommand({
          RawMessage: { Data: message },
        }),
      );

      console.log(`Email with attachments sent to: ${normalizedTo.join(', ')}`);
    } catch (error) {
      console.error('Email send failed:', error);
      throw error;
    }
  }

  async sendSimpleEmail(
    to: string | string[],
    subject: string,
    body: string,
    cc: string[] = [],
    bcc: string[] = [],
    replyTo: string[] = [],
  ): Promise<void> {
    const params = {
      Source: `Ultimate Outsourcing <${process.env.DEFAULT_FROM_EMAIL}>`,
      Destination: {
        ToAddresses: Array.isArray(to) ? to : [to],
        CcAddresses: cc,
        BccAddresses: bcc,
      },
      Message: {
        Subject: { Data: subject },
        Body: {
          Html: { Data: body },
        },
      },
      ReplyToAddresses: replyTo,
    };

    try {
      await this.sesClient.send(new SendEmailCommand(params));
      console.log(
        `Simple email sent successfully to: ${Array.isArray(to) ? to.join(', ') : to}`,
      );
    } catch (error) {
      console.error('Simple email sending failed:', error);
      throw error;
    }
  }

  async website_manual_request_email(
    firt_name: string,
    last_name: string,
    job_title: string,
    company: string,
    email: string,
    requested_manual: string,
  ): Promise<void> {
    const subject = 'Website Manual Request';
    const body = `
      <p>A new website manual request has been received:</p>
      <ul>
        <li><strong>Name:</strong> ${firt_name} ${last_name}</li>
        <li><strong>Job Title:</strong> ${job_title}</li>
        <li><strong>Company:</strong> ${company}</li>
        <li><strong>Email:</strong> ${email}</li>
        <li><strong>Requested Manual:</strong> ${requested_manual}</li>
      </ul>
    `;

    const toAddresses = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    const params = {
      Source: `Ultimate Outsourcing <${process.env.DEFAULT_FROM_EMAIL}>`,
      Destination: {
        ToAddresses: toAddresses,
      },
      Message: {
        Subject: { Data: subject },
        Body: {
          Html: { Data: body },
        },
      },
    };

    try {
      await this.sesClient.send(new SendEmailCommand(params));
      console.log(
        `Website manual request email sent successfully to: ${toAddresses.join(', ')}`,
      );
    } catch (error) {
      console.error('Website manual request email sending failed:', error);
      throw error;
    }
  }
}
