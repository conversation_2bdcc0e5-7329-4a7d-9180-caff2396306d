import { People } from 'src/people/people.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class Experience {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  company_name: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  company_img: string;

  @Column({
    nullable: true,
  })
  position: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  duration: string;

  @Column({
    type: 'date',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  start_date: Date;

  @Column({ nullable: true, type: 'date' })
  end_date: Date;

  @Column({
    type: 'text',
    nullable: true,
  })
  responsibilities: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  location: string;
  @ManyToOne(() => People, (person) => person.experiences, {
    onDelete: 'CASCADE',
  })
  person: People;

  @Column({ nullable: true })
  personId: number; // Optional: if you want to store the personId directly in the experience entity
}
