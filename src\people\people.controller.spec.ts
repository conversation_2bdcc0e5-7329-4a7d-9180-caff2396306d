import { Test, TestingModule } from '@nestjs/testing';
import { PeopleController } from './people.controller';

describe('PeopleController', () => {
  let controller: PeopleController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PeopleController],
    }).compile();

    controller = module.get<PeopleController>(PeopleController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should have all expected controller methods', () => {
    const controllerMethods = [
      'createPerson',
      'addPeopleInBulk',
      'uploadCsv',
      'updateLiCandidate',
      'updatePerson',
      'findAll',
      'getAllPersons',
      'findPersonByCompanyId',
      'findUniquePersonTitles',
      'findSearchPersons',
      'findPersonsOfCompany',
      'findOne',
      'deletePerson',
      'findAllClients',
      'findClientById',
      'findClientByServiceId',
      'findClientServiceById',
      'renewClient',
      'updateClientStatus',
      'getClientServiceStats',
      'findAllProspects',
      'findProspectById',
      'findProspectByServiceId',
      'findProspectServiceById',
      'renewProspect',
      'updateProspectStatus',
      'getProspectServiceStats',
      'getProspectSubscriptionStatus',
      'updateProspectSubscriptionStatus',
      'getAllProspectStatuses',
      'updateProspectStatusByClientNumberAndServiceId',
      'getUserPerson',
      'upsertPerson',
      'getPersonByPersonType',
      'getPartiallyIntrestedProspects',
      'getTrialProspects',
      'getInFutureProspects',
      'getConvertedProspects',
      'candidateStatsBySource',
      'bdDashboard',
      'getCandidates',
      'searchCandidatesWithBooleanQuery',
      'searchCandidatesWithAdvancedFilters',
    ];

    controllerMethods.forEach(method => {
      expect(typeof controller[method]).toBe('function');
    });
  });

});
