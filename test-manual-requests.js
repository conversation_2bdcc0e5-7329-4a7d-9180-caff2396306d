const axios = require('axios');

const BASE_URL = 'http://localhost:5001';

async function testWebsiteManualRequests() {
  console.log('🧪 Testing Website Manual Request API...\n');

  try {
    // Test 1: Create a manual request
    console.log('1. Creating a manual request...');
    const createResponse = await axios.post(`${BASE_URL}/website-manual-requests`, {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      job_title: 'Software Engineer',
      company: 'Tech Corp Ltd',
      email: '<EMAIL>',
      manual_interest: 'BD Manual',
      s3_url: 'https://example-bucket.s3.amazonaws.com/test-file.pdf',
      file_url: 'https://example.com/test-file.pdf'
    });

    console.log('✅ Manual request created successfully!');
    console.log('Response:', JSON.stringify(createResponse.data, null, 2));
    const requestId = createResponse.data.id;

    // Test 2: Get all manual requests
    console.log('\n2. Getting all manual requests...');
    const getAllResponse = await axios.get(`${BASE_URL}/website-manual-requests`);
    console.log('✅ Retrieved all manual requests successfully!');
    console.log(`Found ${getAllResponse.data.length} manual requests`);

    // Test 3: Get specific manual request
    console.log('\n3. Getting specific manual request...');
    const getOneResponse = await axios.get(`${BASE_URL}/website-manual-requests/${requestId}`);
    console.log('✅ Retrieved specific manual request successfully!');
    console.log('Email sent status:', getOneResponse.data.email_sent);

    // Test 4: Get failed email requests
    console.log('\n4. Getting failed email requests...');
    const getFailedResponse = await axios.get(`${BASE_URL}/website-manual-requests/failed/emails`);
    console.log('✅ Retrieved failed email requests successfully!');
    console.log(`Found ${getFailedResponse.data.length} failed email requests`);

    // Test 5: Resend email (if needed)
    if (!getOneResponse.data.email_sent) {
      console.log('\n5. Resending email...');
      const resendResponse = await axios.patch(`${BASE_URL}/website-manual-requests/${requestId}/resend-email`);
      console.log('✅ Email resend attempted successfully!');
      console.log('Updated email status:', resendResponse.data.email_sent);
    } else {
      console.log('\n5. Email was already sent successfully, skipping resend test.');
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the server is running on port 5001');
      console.log('Run: npm run start:dev');
    }
  }
}

// Test different manual interest types
async function testManualInterestTypes() {
  console.log('\n🧪 Testing different manual interest types...\n');

  const testCases = [
    {
      first_name: 'Jane',
      last_name: 'Smith',
      job_title: 'HR Manager',
      company: 'Business Corp',
      email: '<EMAIL>',
      manual_interest: 'Recruitment Manual'
    },
    {
      first_name: 'Bob',
      last_name: 'Johnson',
      job_title: 'Sales Director',
      company: 'Sales Inc',
      email: '<EMAIL>',
      manual_interest: 'BD Manual'
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    try {
      console.log(`Creating request ${i + 1}: ${testCases[i].manual_interest}`);
      const response = await axios.post(`${BASE_URL}/website-manual-requests`, testCases[i]);
      console.log(`✅ Request ${i + 1} created successfully!`);
    } catch (error) {
      console.error(`❌ Request ${i + 1} failed:`, error.response?.data || error.message);
    }
  }
}

// Run tests
async function runAllTests() {
  await testWebsiteManualRequests();
  await testManualInterestTypes();
}

if (require.main === module) {
  runAllTests();
}

module.exports = { testWebsiteManualRequests, testManualInterestTypes };
