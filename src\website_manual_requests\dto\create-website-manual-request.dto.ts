import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, IsUrl, MaxLength } from 'class-validator';
import { ManualInterestType } from '../website_manual_requests.entity';

export class CreateWebsiteManualRequestDto {
  @ApiProperty({
    example: 'John',
    description: 'First name of the person making the request',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  first_name: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name of the person making the request',
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  last_name: string;

  @ApiProperty({
    example: 'Software Engineer',
    description: 'Job title of the person',
    maxLength: 200,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  job_title: string;

  @ApiProperty({
    example: 'Tech Corp Ltd',
    description: 'Company name',
    maxLength: 200,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  company: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the person',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'BD Manual',
    description: 'Type of manual interest',
    enum: ManualInterestType,
  })
  @IsEnum(ManualInterestType)
  @IsNotEmpty()
  manual_interest: ManualInterestType;

  @ApiProperty({
    example: 'https://bucket.s3.amazonaws.com/path/to/file.pdf',
    description: 'S3 bucket URL for the file',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  s3_url?: string;

  @ApiProperty({
    example: 'https://example.com/file.pdf',
    description: 'Direct file URL for download',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  file_url?: string;
}
