import {
  Controller,
  Post,
  Body,
  BadRequestException,
  Get,
  Query,
  Delete,
  Put,
  Param,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { AuthService } from './auth.service';
import * as bcrypt from 'bcrypt';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';
import {
  ApiBody,
  ApiOperation,
  ApiProperty,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';
import { ForgetPasswordDto } from './dto/forgetPassword.dto';
import { ResetPasswordTokenDto } from './dto/resetPasswordToken.dto';
import { VerifyDto } from './dto/verify.dto';
import { UpdateUserDto } from './dto/updateUser.dto';
import { GetAllUsersDto } from './dto/getAllUsers.dto';

@ApiTags('auth')
@Controller('auth')
export class UsersController {
  private transporter: nodemailer.Transporter;
  constructor(
    private readonly usersService: UsersService,
    private authService: AuthService,
    private configService: ConfigService,
  ) {
    this.transporter = nodemailer.createTransport({
      service: this.configService.get('EMAIL_SERVICE'),
      host: this.configService.get('EMAIL_HOST'),
      port: this.configService.get('EMAIL_PORT'),
      secure: this.configService.get('EMAIL_SECURE'),
      requireTLS: this.configService.get('EMAIL_REQUIRE_TLS'),
      auth: {
        user: this.configService.get('EMAIL_USER'),
        pass: this.configService.get('EMAIL_PASS'),
      },
      logger: true,
      debug: true,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Check if the user module is working' })
  async check() {
    const html = `<h1>Welcome to the user module. Its working fine</h1>`;
    return html;
  }

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @Post('register')
  async registerUser(@Body() createUserDto: CreateUserDto) {
    return await this.usersService.createUser(createUserDto);
  }

  @Post('verify-email')
  @ApiOperation({ summary: 'Verify user email' })
  async verifyEmail(@Query('token') token: string) {
    if (!token) {
      throw new BadRequestException('Verification token is required.');
    }
    await this.usersService.verifyEmail(token);
    return { message: 'Email verified successfully' };
  }

  @Post('resend-verification-email')
  @ApiOperation({ summary: 'Resend verification email' })
  async resendVerificationEmail(
    @Query('email') email: string,
    @Query('source') source: string,
  ) {
    if (!email) {
      throw new BadRequestException('Email is required.');
    }
    await this.usersService.resendVerificationEmail(email, source);
    return { message: 'Verification email sent successfully' };
  }

  @Post('send-verification-code')
  @ApiOperation({ summary: 'Send verification code' })
  @ApiBody({
    description: 'Send verification code to email',
    type: String,
    required: true,
    examples: {
      email: {
        value: '<EMAIL>',
        description: 'Email address to send verification code',
      },
    },
  })
  @ApiProperty({
    type: 'string',
    description: 'Email address to send verification code',
    name: 'email',
    required: true,
  })
  async sendVerificationCode(@Body('email') email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }

    const user = await this.usersService.findOne(email);
    if (!user) {
      throw new BadRequestException('User not found');
    }
    await this.usersService.resendVerificationCode(email);
    return {
      message: 'Verification code sent to your email',
    };
  }

  @Post('login')
  @ApiOperation({ summary: 'Login a user' })
  async login(@Body() loginDTO: LoginDto) {
    const { email, password, source, designation } = loginDTO;
    const user = await this.usersService.findOneAndUpdateVerification(email);
    if (!user) {
      throw new BadRequestException('User not found');
    }
    if (source === 'website') {
      if (user.designation != designation) {
        throw new BadRequestException('Not Authorized to login');
      }
    }
    const isPasswordMatching = await bcrypt.compare(
      password,
      user.password_hash,
    );
    if (!isPasswordMatching) {
      throw new BadRequestException('Invalid credentials');
    }
    // await this.authService.sendVerificationCode(
    //   email,
    //   user.verification_code,
    //   user.verification_code_expires,
    // );
    return this.authService.login(
      email,
      user.password_hash,
      user.id,
      user.full_name,
      user.role,
      user.designation,
      user.working_mode,
    );
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify 2FA Code' })
  async verify(@Body() verifyDTO: VerifyDto) {
    const { email, verification_code } = verifyDTO;
    const user = await this.usersService.findOne(email);

    // if (!user || user.verification_code !== verification_code) {
    //   throw new BadRequestException('Invalid or expired verification code');
    // }

    // if (new Date() > new Date(user.verification_code_expires)) {
    //   throw new BadRequestException('Verification code expired');
    // }

    return this.authService.login(
      email,
      user.password_hash,
      user.id,
      user.full_name,
      user.role,
      user.designation,
      user.working_mode,
    );
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Forgot password' })
  async forgotPassword(@Body() forgetPasswordDto: ForgetPasswordDto) {
    const { email, source } = forgetPasswordDto;
    await this.usersService.forgotPassword(email, source);
    return 'Password reset link sent to your email';
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password' })
  async resetPassword(@Body() resetPasswordToken: ResetPasswordTokenDto) {
    await this.usersService.resetPasword(resetPasswordToken);
    return 'Password updated successfully';
  }

  @Post('update-user/:id')
  @ApiOperation({ summary: 'Update user' })
  async updateUser(@Param('id') id: string, @Body() userData: UpdateUserDto) {
    if (!id) {
      throw new BadRequestException('Invalid user ID.');
    }

    const user = await this.usersService.findUserById(id);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    await this.usersService.updateUser(id, userData);
    return { message: 'User updated successfully' };
  }

  @Post('getAllUsers')
  @ApiOperation({ summary: 'Get all users' })
  async findAllUsers(@Body() data: GetAllUsersDto) {
    try {
      console.log('Data in controller body: ', data);
      const users = await this.usersService.findAllUsers(data);
      return users;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('user/:id')
  @ApiOperation({ summary: 'Get user by id' })
  async getUser(id: string) {
    const user = await this.usersService.findUserById(id);
    if (!user) {
      throw new BadRequestException('User not found');
    }
    return user;
  }

  @Delete('delete-all')
  @ApiOperation({ summary: 'Delete user by id' })
  async deleteAllUser() {
    await this.usersService.deleteAllUsers();
    return 'User deleted successfully';
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete user by id' })
  async deleteUser(@Query('id') id: string) {
    const user = await this.usersService.findUserById(id);
    if (!user) {
      throw new BadRequestException('User not found');
    }
    await this.usersService.deleteUser(id);
    return 'User deleted successfully';
  }

  @Put('update-status')
  @ApiOperation({ summary: 'Update user status' })
  @ApiOperation({ summary: 'Update user status by ID' })
  @ApiBody({
    description: 'Update user status',
    type: 'object',
    required: true,
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'User ID', example: '12345' },
        status: {
          type: 'string',
          description: 'New status',
          example: 'active',
        },
      },
      required: ['id', 'status'],
    },
  })
  async updateUserStatus(@Body() body: { id: string; status: string }) {
    const { id, status } = body;
    try {
      await this.usersService.updateUserStatus(id, status);
      return 'User status updated successfully';
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('get-all-users')
  @ApiOperation({ summary: 'Get all users' })
  @ApiQuery({
    name: 'role',
    required: false,
    description: 'Filter by role',
    type: String,
  })
  @ApiQuery({
    name: 'designation',
    required: false,
    description: 'Filter by designation',
    type: String,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by status',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Number of users per page',
    type: Number,
  })
  @ApiQuery({
    name: 'searchString',
    required: false,
    description: 'Search string to filter users',
    type: String,
  })
  async getAllUsers(
    @Query('role') role?: string,
    @Query('designation') designation?: string,
    @Query('status') status?: string,
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('searchString') searchString?: string,
  ) {
    const users = await this.usersService.getAllUsersByFilters(
      role,
      designation,
      status,
      page,
      pageSize,
      searchString,
    );
    return users;
  }

  @Get('designationBasedUsers')
  @ApiOperation({ summary: 'Get users by designation' })
  @ApiQuery({
    name: 'designation',
    required: true,
    description: 'Designation to filter users',
    type: String,
  })
  async getUsersByDesignation(@Query('designation') designation: string) {
    const users = await this.usersService.getUsersByDesignation(designation);
    return users;
  }
}
