import { Module } from '@nestjs/common';
import { ResumeService } from './resume.service';
import { ResumeController } from './resume.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResumeTemplate } from 'src/resume-templates/resume-template.entity';
import { Resume } from './resume.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Resume, ResumeTemplate])],
  providers: [ResumeService],
  controllers: [ResumeController],
})
export class ResumeModule {}
