import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SequenceSteps } from './sequence_steps.entity';
import { Repository } from 'typeorm';
import { SequenceStepsDto } from './dto/sequenceSteps.dto';
import { UpdateSequenceStepDto } from './dto/updateSequenceStep.dto';

@Injectable()
export class SequenceStepsService {
  constructor(
    @InjectRepository(SequenceSteps)
    private readonly sequenceStepsRepository: Repository<SequenceSteps>,
  ) {}

  async createSequenceSteps(
    sequenceSteps: SequenceStepsDto,
  ): Promise<SequenceSteps> {
    try {
      const newSequenceSteps =
        this.sequenceStepsRepository.create(sequenceSteps);
      return await this.sequenceStepsRepository.save(newSequenceSteps);
    } catch (error) {
      throw new Error('Error creating sequence steps: ' + error.message);
    }
  }

  async getSequenceSteps(): Promise<SequenceSteps[]> {
    try {
      return await this.sequenceStepsRepository.find();
    } catch (error) {
      throw new Error('Error fetching sequence steps: ' + error.message);
    }
  }

  async getSequenceStepsById(id: number): Promise<SequenceSteps> {
    try {
      const sequenceSteps = await this.sequenceStepsRepository.findOne({
        where: { id },
      });
      if (!sequenceSteps) {
        throw new Error('Sequence steps not found');
      }
      return sequenceSteps;
    } catch (error) {
      throw new Error('Error fetching sequence steps: ' + error.message);
    }
  }

  async updateSequenceSteps(
    id: number,
    sequenceSteps: UpdateSequenceStepDto,
  ): Promise<SequenceSteps> {
    try {
      await this.sequenceStepsRepository.update(id, sequenceSteps);
      return await this.getSequenceStepsById(id);
    } catch (error) {
      throw new Error('Error updating sequence steps: ' + error.message);
    }
  }

  async deleteSequenceSteps(id: number): Promise<void> {
    try {
      const result = await this.sequenceStepsRepository.delete(id);
      if (result.affected === 0) {
        throw new Error('Sequence steps not found');
      }
    } catch (error) {
      throw new Error('Error deleting sequence steps: ' + error.message);
    }
  }
}
