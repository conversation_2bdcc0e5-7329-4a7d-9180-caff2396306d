import { ApiProperty } from '@nestjs/swagger';

export class ContactUsDto {
  @ApiProperty({
    description: 'First name of the contact person',
    example: '<PERSON>',
  })
  firstName: string;

  @ApiProperty({
    description: 'Last name of the contact person',
    example: '<PERSON><PERSON>',
  })
  lastName: string;

  @ApiProperty({
    description: 'Job title of the contact person',
    example: 'Software Engineer',
  })
  jobTitle: string;

  @ApiProperty({
    description: 'Company name of the contact person',
    example: 'Tech Corp',
  })
  companyName: string;

  @ApiProperty({
    description: 'Business email of the contact person',
    example: '<EMAIL>',
  })
  businessEmail: string;

  @ApiProperty({
    description: 'Phone number of the contact person',
    example: '+1234567890',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Post code of the company',
    example: '12345',
  })
  companyPostCode: string;

  @ApiProperty({
    description: 'Industry of the company',
    example: 'Information Technology',
  })
  industry: string;

  @ApiProperty({
    description: 'Services the contact person is interested in',
    example: 'Cloud Computing, DevOps',
  })
  interestedServices: string;

  @ApiProperty({
    description: 'Company website of the contact person',
    example: 'https://www.techcorp.com',
  })
  companyWebsite: string | null;
}
