import { <PERSON>, <PERSON>, Body, Param, Logger, HttpC<PERSON>, HttpStatus } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';

export interface EmailWebhookPayload {
  candidateId: number;
  stepId: number;
  event: 'delivered' | 'opened' | 'clicked' | 'replied' | 'bounced' | 'spam';
  timestamp: string;
  messageId?: string;
  recipientEmail?: string;
  responseData?: any;
}

export interface WhatsAppWebhookPayload {
  candidateId: number;
  stepId: number;
  event: 'delivered' | 'read' | 'replied';
  timestamp: string;
  messageId?: string;
  recipientPhone?: string;
  responseData?: any;
}

export interface SmsWebhookPayload {
  candidateId: number;
  stepId: number;
  event: 'delivered' | 'replied' | 'failed';
  timestamp: string;
  messageId?: string;
  recipientPhone?: string;
  responseData?: any;
}

export interface CallWebhookPayload {
  candidateId: number;
  stepId: number;
  event: 'answered' | 'no_answer' | 'busy' | 'failed';
  timestamp: string;
  callId?: string;
  recipientPhone?: string;
  callDuration?: number;
  responseData?: any;
}

export interface LinkedInWebhookPayload {
  candidateId: number;
  stepId: number;
  event: 'delivered' | 'viewed' | 'replied' | 'connection_accepted';
  timestamp: string;
  messageId?: string;
  recipientProfile?: string;
  responseData?: any;
}

@Controller('webhooks')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('email')
  @HttpCode(HttpStatus.OK)
  async handleEmailWebhook(@Body() payload: EmailWebhookPayload) {
    this.logger.log(`Received email webhook: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processEmailWebhook(payload);
      return { success: true, message: 'Email webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process email webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  @Post('whatsapp')
  @HttpCode(HttpStatus.OK)
  async handleWhatsAppWebhook(@Body() payload: WhatsAppWebhookPayload) {
    this.logger.log(`Received WhatsApp webhook: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processWhatsAppWebhook(payload);
      return { success: true, message: 'WhatsApp webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process WhatsApp webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  @Post('sms')
  @HttpCode(HttpStatus.OK)
  async handleSmsWebhook(@Body() payload: SmsWebhookPayload) {
    this.logger.log(`Received SMS webhook: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processSmsWebhook(payload);
      return { success: true, message: 'SMS webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process SMS webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  @Post('call')
  @HttpCode(HttpStatus.OK)
  async handleCallWebhook(@Body() payload: CallWebhookPayload) {
    this.logger.log(`Received call webhook: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processCallWebhook(payload);
      return { success: true, message: 'Call webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process call webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  @Post('linkedin')
  @HttpCode(HttpStatus.OK)
  async handleLinkedInWebhook(@Body() payload: LinkedInWebhookPayload) {
    this.logger.log(`Received LinkedIn webhook: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processLinkedInWebhook(payload);
      return { success: true, message: 'LinkedIn webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process LinkedIn webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  @Post('test/:candidateId/:stepId')
  @HttpCode(HttpStatus.OK)
  async testWebhook(
    @Param('candidateId') candidateId: number,
    @Param('stepId') stepId: number,
    @Body() payload: { event: string; medium: string },
  ) {
    this.logger.log(`Test webhook for candidate ${candidateId}, step ${stepId}: ${JSON.stringify(payload)}`);
    
    try {
      await this.webhooksService.processTestWebhook(candidateId, stepId, payload.event, payload.medium);
      return { success: true, message: 'Test webhook processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process test webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }
}
