import { ApiProperty, PartialType } from '@nestjs/swagger';
import { RoleDto } from './roles.dto';
import { IsNumber } from 'class-validator';

export class UpdateRoleDto extends PartialType(RoleDto) {
  @ApiProperty({
    example: 1,
    description: 'The ID of the role',
    required: true,
  })
  @IsNumber()
  id: number;
  @ApiProperty({
    description: 'The ID of the role to be updated',
    required: true,
  })
  attachment_url?: string;

  @ApiProperty({
    description: 'The ID of the role to be updated',
    required: true,
  })
  roleId?: number;
}
