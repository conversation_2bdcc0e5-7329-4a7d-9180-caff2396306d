import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleCandidate } from '../role_candidates.entity';
import { Repository } from 'typeorm';
import { Roles } from 'src/roles/roles.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Languages } from 'src/languages/langauges.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';

@Injectable()
export class RecruitmentService {
  constructor(
    @InjectRepository(RoleCandidate)
    private readonly roleCandidateRepository: Repository<RoleCandidate>,
    @InjectRepository(Roles)
    private readonly rolesRepository: Repository<Roles>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(PersonPhone)
    private readonly personPhoneRepository: Repository<PersonPhone>,
    @InjectRepository(Qualifications)
    private readonly qualificationsRepository: Repository<Qualifications>,
    @InjectRepository(Languages)
    private readonly languagesRepository: Repository<Languages>,
    @InjectRepository(PersonSkill)
    private readonly personSkillRepository: Repository<PersonSkill>,
    private readonly s3BucketService: S3bucketService,
  ) {}

  async updateRoleCandidateStage(
    chennel?: string,
    screening_stage?: string,
    partially_interested_stage?: string,
    submission_stage?: string,
    role_candidate_id?: number,
  ): Promise<RoleCandidate[]> {
    try {
      // Step 1: Fetch role candidate
      const roleCandidate = await this.roleCandidateRepository.findOneBy({
        id: role_candidate_id,
      });

      if (!roleCandidate) {
        throw new NotFoundException('Role candidate not found');
      }

      if (screening_stage) roleCandidate.screening_stage = screening_stage;
      if (partially_interested_stage)
        roleCandidate.partially_interested_stage = partially_interested_stage;
      if (submission_stage) roleCandidate.submission_stage = submission_stage;
      roleCandidate.current_channel = chennel;
      roleCandidate.stage = 'ENGAGED';

      await this.roleCandidateRepository.save(roleCandidate);

      return [roleCandidate];
    } catch (error) {
      console.error('Error updating role candidate stage:', error);
      throw new InternalServerErrorException(
        'Failed to update role candidate stage',
      );
    }
  }
}
