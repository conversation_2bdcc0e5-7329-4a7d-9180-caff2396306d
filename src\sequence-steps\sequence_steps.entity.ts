import { EmailTemplates } from 'src/email-templates/emailTemplates.entity';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
@Entity('sequence_steps')
export class SequenceSteps {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({
    type: 'int',
    nullable: true,
    default: 0,
  })
  order: number;

  @Column({
    type: 'enum',
    enum: ['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'OTHER'],
    default: 'OUTREACH',
  })
  type: string;

  @Column({
    type: 'enum',
    enum: ['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'],
    default: 'EMAIL',
  })
  medium: string;

  @ManyToOne(
    () => EmailTemplates,
    (emailTemplate) => emailTemplate.sequenceSteps,
    { nullable: true },
  )
  emailTemplate: EmailTemplates;

  @Column({
    nullable: true,
  })
  templateId: number;

  @ManyToOne(() => RoleSequence, (roleSequence) => roleSequence.sequenceSteps, {
    nullable: true,
  })
  roleSequence: RoleSequence;

  @Column({
    nullable: true,
  })
  roleSequenceId: number;
}
