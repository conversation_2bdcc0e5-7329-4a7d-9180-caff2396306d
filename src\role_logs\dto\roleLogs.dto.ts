import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { RoleLogsAction, RoleLogsStatus, RoleLogsType } from './rol_los.enum';
import { ApiProperty } from '@nestjs/swagger';

export class RoleLogsDto {
  @ApiProperty({
    description: 'Action type',
    enum: RoleLogsAction,
    required: true,
    example: RoleLogsAction.CREATE,
  })
  @IsString()
  @IsEnum(RoleLogsAction)
  action: RoleLogsAction;

  @ApiProperty({
    description: 'Timestamp',
    type: Date,
    required: true,
    example: '2023-10-01T00:00:00Z',
  })
  @IsDate()
  timestamp: Date;

  @ApiProperty({
    description: 'Details',
    type: String,
    required: false,
    example: 'Details about the action',
  })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiProperty({
    description: 'Role number',
    type: Number,
    required: false,
    example: 12345,
  })
  @IsOptional()
  @IsInt()
  role_number?: number;

  @ApiProperty({
    description: 'Log status type',
    enum: RoleLogsStatus,
    required: false,
    example: RoleLogsStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(RoleLogsStatus)
  log_status_type?: RoleLogsStatus;

  @ApiProperty({
    description: 'Log status at',
    enum: RoleLogsType,
    required: false,
    example: RoleLogsType.RESOURCER,
  })
  @IsOptional()
  @IsEnum(RoleLogsType)
  log_status_at?: RoleLogsType;

  @ApiProperty({
    description: 'Start time',
    type: Date,
    required: false,
    example: '2023-10-01T00:00:00Z',
  })
  @IsOptional()
  @IsDate()
  start_time?: Date;

  @ApiProperty({
    description: 'End time',
    type: Date,
    required: false,
    example: '2023-10-01T00:00:00Z',
  })
  @IsOptional()
  @IsDate()
  end_time?: Date;

  @ApiProperty({
    description: 'Comment',
    type: String,
    required: false,
    example: 'This is a comment',
  })
  @IsOptional()
  @IsString()
  comment?: string;

  @ApiProperty({
    description: 'Time spent',
    type: String,
    required: false,
    example: '2 hours',
  })
  @IsOptional()
  @IsString()
  time_spent?: string;

  @ApiProperty({
    description: 'Is done',
    type: Boolean,
    required: false,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  is_done?: boolean;

  @ApiProperty({
    description: 'Status',
    type: String,
    required: false,
    example: 'active',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  status?: string;

  @ApiProperty({
    description: 'Role ID',
    type: Number,
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsInt()
  roleId?: number;

  @ApiProperty({
    description: 'User ID',
    type: Number,
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsString()
  userId?: string;
}
