import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common';
import { JobAlertsService } from './job-alerts.service';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CreateJobAlertDto } from './dto/jobAlert.dto';
import { JobAlerts } from './job-alerts.entity';

@ApiTags('Job Alerts')
@Controller('job-alerts')
export class JobAlertsController {
  constructor(private readonly jobAlertsService: JobAlertsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a job alert' })
  @ApiResponse({ status: 201, description: 'Job alert successfully created.' })
  @ApiResponse({ status: 500, description: 'Failed to create job alert.' })
  async createJobAlert(@Body() data: CreateJobAlertDto): Promise<JobAlerts> {
    return await this.jobAlertsService.createJobAlert(data);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all job alerts with pagination' })
  @ApiResponse({
    status: 200,
    description: 'List of job alerts retrieved successfully.',
  })
  async getAllJobAlerts(
    @Query('page') page: number,
    @Query('limit') limit: number,
  ): Promise<JobAlerts[]> {
    return await this.jobAlertsService.getAllJobAlerts(page, limit);
  }

  @Get(':id/send')
  @ApiOperation({ summary: 'Send a job alert' })
  @ApiResponse({ status: 200, description: 'Job alert sent successfully.' })
  @ApiResponse({ status: 404, description: 'Job alert not found.' })
  async sendJobAlert(@Param('id') id: number): Promise<boolean> {
    return await this.jobAlertsService.sendJobAlert(id);
  }

  @Get('candidate')
  @ApiOperation({ summary: 'Get job alert by candidate email' })
  @ApiResponse({
    status: 200,
    description: 'Job alert retrieved successfully.',
  })
  @ApiResponse({ status: 404, description: 'Job alert not found.' })
  async getJobAlertByCandidateEmail(
    @Query('email') email: string,
  ): Promise<JobAlerts | null> {
    return await this.jobAlertsService.getJobAlertByCandidateEmail(email);
  }

  @Get('search')
  @ApiOperation({ summary: 'Get job alerts by keywords' })
  @ApiResponse({
    status: 200,
    description: 'Job alerts retrieved successfully.',
  })
  async getJobAlertsByKeywords(
    @Query('keywords') keywords: string,
  ): Promise<JobAlerts[]> {
    return await this.jobAlertsService.getJobAlertsByKeywords(keywords);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a job alert' })
  @ApiResponse({ status: 200, description: 'Job alert updated successfully.' })
  @ApiResponse({ status: 404, description: 'Job alert not found.' })
  @ApiBody({ type: CreateJobAlertDto })
  async updateJobAlert(
    @Param('id') id: number,
    @Body() data: Partial<CreateJobAlertDto>,
  ): Promise<JobAlerts | null> {
    return await this.jobAlertsService.updateJobAlert(id, data);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a job alert' })
  @ApiResponse({ status: 200, description: 'Job alert deleted successfully.' })
  @ApiResponse({ status: 404, description: 'Job alert not found.' })
  async deleteJobAlert(@Param('id') id: number): Promise<boolean> {
    return await this.jobAlertsService.deleteJobAlert(id);
  }
}
