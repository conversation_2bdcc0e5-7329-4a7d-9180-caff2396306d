import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  IsDate,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ExperienceLevel {
  INTERN = 'INTERN',
  ENTRY_LEVEL = 'ENTRY_LEVEL',
  ASSOCIATE = 'ASSOCIATE',
  MID_SENIOR_LEVEL = 'MID_SENIOR_LEVEL',
  SENIOR_LEVEL = 'SENIOR_LEVEL',
  DIRECTOR = 'DIRECTOR',
  EXECUTIVE = 'EXECUTIVE',
}

export enum JobType {
  FULL_TIME = 'FULL_TIME',
  PART_TIME = 'PART_TIME',
  CONTRACT = 'CONTRACT',
  TEMPORARY = 'TEMPORARY',
  INTERNSHIP = 'INTERNSHIP',
}

export enum JobLocation {
  REMOTE = 'REMOTE',
  ONSITE = 'ONSITE',
  HYBRID = 'HYBRID',
}

export enum SalaryPeriod {
  HOURLY = 'HOURLY',
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  BIWEEKLY = 'BIWEEKLY',
  MONTHLY = 'MONTHLY',
  ANNUALLY = 'ANNUALLY',
}

export enum Currency {
  USD = 'USD',
  PKR = 'PKR',
  EUR = 'EUR',
  GBP = 'GBP',
  AED = 'AED',
  SAR = 'SAR',
  QAR = 'QAR',
  OMR = 'OMR',
  KWD = 'KWD',
  BHD = 'BHD',
  CAD = 'CAD',
  AUD = 'AUD',
  JPY = 'JPY',
  CNY = 'CNY',
  INR = 'INR',
  BDT = 'BDT',
  LKR = 'LKR',
  NPR = 'NPR',
}

export class CreateJobDto {
  @ApiProperty({
    example: 'Software Engineer',
    description: 'Job title',
    required: true,
  })
  @IsString()
  title: string;

  @ApiProperty({
    example: 'Develop and maintain software applications',
    description: 'Job description',
    required: true,
  })
  @IsString()
  description: string;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'Job status',
    required: false,
  })
  @IsString()
  status: string;

  @ApiProperty({
    example: 'https://example.com/job-posting',
    description: 'Job posting link',
    required: false,
  })
  @IsUrl()
  @IsOptional()
  job_posting_link: string;

  @ApiProperty({
    example: '2025-06-01',
    description: 'Job posting date',
    required: false,
  })
  @IsDate()
  job_posting_date: Date;

  @ApiProperty({
    example: '2025-07-01',
    description: 'Job closing date',
    required: false,
  })
  @IsDate()
  job_closing_date: Date;

  @ApiProperty({
    example: 'MID_SENIOR_LEVEL',
    description: 'Experience level required for the job',
    enum: ExperienceLevel,
    required: false,
  })
  @IsEnum(ExperienceLevel)
  @IsOptional()
  experience_level?: ExperienceLevel;

  @ApiProperty({
    example: 'FULL_TIME',
    description: 'Type of job',
    enum: JobType,
    required: false,
  })
  @IsEnum(JobType)
  @IsOptional()
  job_type?: JobType;

  @ApiProperty({
    example: 'ONSITE',
    description: 'Job location type',
    enum: JobLocation,
    required: false,
  })
  @IsEnum(JobLocation)
  @IsOptional()
  job_location_type?: JobLocation;

  @ApiProperty({
    example: 'New York',
    description: 'City where the job is located',
    required: true,
  })
  @IsString()
  job_location_city: string;

  @ApiProperty({
    example: 'NY',
    description: 'State where the job is located',
    required: true,
  })
  @IsString()
  job_location_state: string;

  @ApiProperty({
    example: 'USA',
    description: 'Country where the job is located',
    required: true,
  })
  @IsString()
  job_location_country: string;

  @ApiProperty({
    example: '10001',
    description: 'ZIP code of job location',
    required: true,
  })
  @IsString()
  job_location_zip: string;

  @ApiProperty({
    example: 'USD',
    description: 'Currency for salary',
    enum: Currency,
    required: false,
  })
  @IsEnum(Currency)
  @IsOptional()
  currency?: Currency;

  @ApiProperty({
    example: 50000,
    description: 'Minimum salary',
    required: false,
  })
  @IsOptional()
  @IsInt()
  salary_min?: number;

  @ApiProperty({
    example: 100000,
    description: 'Maximum salary',
    required: false,
  })
  @IsOptional()
  @IsInt()
  salary_max?: number;

  @ApiProperty({
    example: 'ANNUALLY',
    description: 'Salary period',
    enum: SalaryPeriod,
    required: false,
  })
  @IsEnum(SalaryPeriod)
  @IsOptional()
  salary_period?: SalaryPeriod;

  @ApiProperty({
    example: 'Technology',
    description: 'Industry category of the job',
    required: false,
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({
    example: 'React',
    description: 'Skills required for the job',
    required: false,
  })
  @IsOptional()
  @IsString({ each: true })
  state: string;

  @ApiProperty({
    example: 'React',
    description: 'Skills required for the job',
    required: false,
  })
  @IsOptional()
  benifits?: string[];

  @ApiProperty({
    example: ['React', 'Node.js'],
    description: 'Skills required for the job',
    required: false,
  })
  @IsOptional()
  @IsString({ each: true })
  skills?: string[];

  @ApiProperty({
    description: 'Company associated with the job',
    required: false,
  })
  @IsOptional()
  companyId: number;

  @ApiProperty({ description: 'Person who posted the job', required: false })
  @IsOptional()
  personId: number;

  @ApiProperty({ description: 'User associated with the job', required: false })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Sector associated with the job',
    required: false,
  })
  @IsOptional()
  sectorId?: number;

  @ApiProperty({
    description: 'Country associated with the job',
    required: false,
  })
  @IsOptional()
  countryId?: number;

  @IsOptional()
  applicants: string;

  @IsOptional()
  skill_required: string;

  @IsOptional()
  SR_specified_industry: string;
}

export class GetAllJobsDto {
  @IsOptional()
  page: string;

  @IsOptional()
  pageSize: string;

  @IsOptional()
  search: string;

  @IsOptional()
  country_id: string;

  @IsOptional()
  sector_id: string;

  @IsOptional()
  remote: string;

  @IsOptional()
  jobType: JobType;

  @IsOptional()
  experienceLevel: string;

  @IsOptional()
  selectedCountry: string;

  @IsOptional()
  selectedSector: string;

  @IsOptional()
  startDate: string;

  @IsOptional()
  endDate: string;
}

export class GetAllCompaniesDto {
  @IsOptional()
  page: string;

  @IsOptional()
  size: string;

  @IsOptional()
  country_id: string;

  @IsOptional()
  sector_id: string;

  @IsOptional()
  findPeople: string;

  @IsOptional()
  startDate: string;

  @IsOptional()
  endDate: string;

  @IsOptional()
  withContactInfo: string;
}

export class GetCompaniesDto {
  @IsOptional()
  page: string;

  @IsOptional()
  pageSize: string;

  @IsOptional()
  search: string;

  @IsOptional()
  country_id: string;

  @IsOptional()
  sector_id: string;

  @IsOptional()
  level: string;

  @IsOptional()
  startDate: string;

  @IsOptional()
  endDate: string;

  @IsOptional()
  companySize: string;

  @IsOptional()
  industries: string;

  @IsOptional()
  premium_level: string;
}

export class searchJobPostsOfCompanyDto {
  @IsOptional()
  searchTerm: string;

  @IsOptional()
  company_id: number;
}
