import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { LeadsService } from './leads.service';
import {
  GetCountryAndSectorWiseLeadsStatsDto,
  GetCountryAndSectorWisePersonsDto,
  GetDetailLeadReportsDto,
  GetMarketingEmailsDto,
  GetPersonWiseLeadsStatsDto,
  GetRegionWiseLeadsContactStatsDto,
  GetTeamAssignedPersonsStatsDto,
  GetUserWorkReportDto,
} from './dto/leadsQuery.dto';

@Controller('leads')
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  @Get('getMarketingEmailsWithPersonName')
  async getMaerketingEmailsWithPersonName(
    @Query() queryParam: GetMarketingEmailsDto,
  ) {
    return this.leadsService.getMarketingEmailsWithPersonName(queryParam);
  }

  @Get('getCountryAndSectorWiseLeadsStats')
  async getCountryAndSectorWiseLeadsStats(
    @Query() queryParam: GetCountryAndSectorWiseLeadsStatsDto,
  ) {
    return this.leadsService.getCountryAndSectorWiseLeadsStats(queryParam);
  }

  @Get('getCountryAndSectorWisePersons')
  async getCountryAndSectorWisePersons(
    @Query() queryParam: GetCountryAndSectorWisePersonsDto,
  ) {
    return this.leadsService.getCountryAndSectorWisePersons(queryParam);
  }

  @Get('getTeamAssignedPersonsStats')
  async getTeamAssignedPersonsStats(
    @Query() queryParam: GetTeamAssignedPersonsStatsDto,
  ) {
    return this.leadsService.getTeamAssignedPersonsStats(queryParam);
  }

  @Get('getUserWorkReportV3')
  async getUserWorkReportV3(@Query() queryParam: GetUserWorkReportDto) {
    return this.leadsService.getUserWorkReportV3(queryParam);
  }

  @Get('getRegionWiseLeadsContactStats')
  async getRegionWiseLeadsContactStats(
    @Query() queryParam: GetRegionWiseLeadsContactStatsDto,
  ) {
    return this.leadsService.getRegionWiseLeadsContactStats(queryParam);
  }

  @Get('getDetailLeadReports')
  async getDetailLeadReports(@Query() queryParam: GetDetailLeadReportsDto) {
    return this.leadsService.detailLeadsReport(queryParam);
  }

  @Get('getPersonWiseLeadsStats')
  async getPersonWiseLeadsStats(
    @Query() queryParam: GetPersonWiseLeadsStatsDto,
  ) {
    return this.leadsService.getPersonWiseLeadsStats(queryParam);
  }
}
