import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    example: 'johndo<PERSON>@example.com',
    description: 'The username of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'password',
    description: 'The password of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    example: 'website',
    description: 'login source',
    required: false,
  })
  @IsString()
  @IsOptional()
  source: string;

  @ApiProperty({
    example: 'admin',
    description: 'user designation',
    required: false,
  })
  @IsString()
  @IsOptional()
  designation: string;
}
