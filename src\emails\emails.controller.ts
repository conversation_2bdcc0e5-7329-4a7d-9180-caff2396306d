import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';
import { PersonEmailDto } from './dto/person-email.dto';
import { PersonEmailService } from './emails.service';
import { PersonEmail } from './emails.entity';

@ApiTags('Person Emails')
@Controller('person-emails')
export class PersonEmailController {
  constructor(private readonly personEmailService: PersonEmailService) {}

  @Post('create-without-personid')
  @ApiOperation({ summary: 'Create a new email without person ID' })
  @ApiBody({ type: PersonEmailDto })
  @ApiResponse({
    status: 201,
    description: 'Email created successfully',
    type: PersonEmail,
  })
  async createWithoutPerson(
    @Body() emailDto: PersonEmailDto,
  ): Promise<PersonEmail> {
    return this.personEmailService.createWithoutPerson(emailDto);
  }

  @Post(':personId')
  @ApiOperation({ summary: 'Create a new email for a person' })
  @ApiParam({ name: 'personId', description: 'ID of the person' })
  @ApiBody({ type: PersonEmailDto })
  @ApiResponse({
    status: 201,
    description: 'Email created successfully',
    type: PersonEmail,
  })
  async create(
    @Param('personId') personId: number,
    @Body() emailDto: PersonEmailDto,
  ): Promise<PersonEmail> {
    return this.personEmailService.create(personId, emailDto);
  }

  @Get(':personId')
  @ApiOperation({ summary: 'Get all emails associated with a person' })
  @ApiParam({ name: 'personId', description: 'ID of the person' })
  @ApiResponse({
    status: 200,
    description: 'List of emails',
    type: [PersonEmail],
  })
  async findAllByPerson(
    @Param('personId') personId: number,
  ): Promise<PersonEmail[]> {
    return this.personEmailService.findAllByPerson(personId);
  }

  @Put(':emailId')
  @ApiOperation({ summary: 'Update an email' })
  @ApiParam({ name: 'emailId', description: 'ID of the email to update' })
  @ApiBody({ type: PersonEmailDto })
  @ApiResponse({ status: 200, description: 'Updated email', type: PersonEmail })
  async update(
    @Param('emailId') emailId: number,
    @Body() emailDto: PersonEmailDto,
  ): Promise<PersonEmail> {
    return this.personEmailService.update(emailId, emailDto);
  }

  @Delete(':emailId')
  @ApiOperation({ summary: 'Delete an email' })
  @ApiParam({ name: 'emailId', description: 'ID of the email to delete' })
  @ApiResponse({ status: 200, description: 'Email deleted successfully' })
  async delete(@Param('emailId') emailId: number): Promise<void> {
    return this.personEmailService.delete(emailId);
  }
}
