import { RoleSequence } from 'src/sequence/sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum SequenceStepStatus {
  PENDING = 'PENDING',
  QUEUED = 'QUEUED',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED',
  REPLIED = 'REPLIED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
}

export enum StepType {
  SEQUENTIAL = 'SEQUENTIAL',
  PARALLEL = 'PARALLEL',
}

@Entity('candidate_sequence_status')
@Index(['candidateId', 'sequenceId', 'stepId'], { unique: true })
export class CandidateSequenceStatus {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => RoleCandidate, { nullable: false })
  candidate: RoleCandidate;

  @Column()
  @Index()
  candidateId: number;

  @ManyToOne(() => RoleSequence, { nullable: false })
  sequence: RoleSequence;

  @Column()
  @Index()
  sequenceId: number;

  @ManyToOne(() => SequenceSteps, { nullable: false })
  step: SequenceSteps;

  @Column()
  @Index()
  stepId: number;

  @Column({
    type: 'enum',
    enum: SequenceStepStatus,
    default: SequenceStepStatus.PENDING,
  })
  @Index()
  status: SequenceStepStatus;

  @Column({
    type: 'enum',
    enum: StepType,
    default: StepType.SEQUENTIAL,
  })
  stepType: StepType;

  @Column({ type: 'int', default: 0 })
  @Index()
  stepOrder: number;

  @Column({ type: 'timestamp', nullable: true })
  scheduledAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  sentAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deliveredAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  openedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  clickedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  repliedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  completedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  failedAt: Date;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'int', default: 0 })
  attemptCount: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  responseData: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
