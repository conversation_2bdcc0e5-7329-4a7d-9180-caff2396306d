import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Designation, Role, Status } from './create-user.dto';

export class UpdateUserDto {

  @ApiProperty({
    example: '<PERSON>',
    description: 'The full name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  full_name?: string;

  @ApiProperty({
    example: 'John',
    description: 'The first name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  first_name?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'The last name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  last_name?: string;

  @ApiProperty({
    example: 'johndoe',
    description: 'The username of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({
    example: ['CRM', 'WEBSITE'],
    description: 'Platform of the user from where he/she is being registered',
    required: false,
  })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiProperty({
    example: 'ONSITE',
    description: 'Working mode of the user',
    required: false,
    type: 'enum',
    enum: ['ONSITE', 'REMOTE', 'HYBRID'],
  })
  @IsEnum(['ONSITE', 'REMOTE', 'HYBRID'])
  @IsString()
  @IsOptional()
  working_mode?: string;

  @ApiProperty({
    example: 'USER',
    description: 'The role of the user',
    required: false,
    enum: Role,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiProperty({
    example: 'RECRUITER',
    description: 'The designation of the user',
    required: false,
    enum: Designation,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Designation)
  designation?: Designation;

  @ApiProperty({
    example: '12345-1234567-1',
    description: 'The CNIC of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  cnic?: string;

  @ApiProperty({
    example: '',
    description: 'The password of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_original?: string;

  @ApiProperty({
    example: '12345',
    description: 'The password hash of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_hash?: string;

  @ApiProperty({
    example: '12345',
    description: 'The password salt of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_salt?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({
    example: '1990-01-01',
    description: 'The birth date of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  birth_date?: string;

  @ApiProperty({
    example: 'US',
    description: 'The country code of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  country_code?: string;

  @ApiProperty({
    example: '1234567890',
    description: 'The phone number of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  phone_number?: string;

  @ApiProperty({
    example: '************',
    description: 'The display phone number of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  display_phone_number?: string;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'The profile picture of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  profile_picture?: string;

  @ApiProperty({
    example: 'password',
    description: 'The password of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password?: string;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'The status of the user',
    required: false,
    enum: Status,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Status)
  status?: Status;
}
