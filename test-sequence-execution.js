/**
 * Test Script for Sequence Execution System
 * 
 * This script demonstrates how to use the recruitment workflow automation system.
 * Run this after the application is started and database is seeded with test data.
 */

const axios = require('axios');

const BASE_URL = 'https://19cxs75g-5001.inc1.devtunnels.ms';

// Test data
const testSequence = {
  name: 'Test Recruitment Sequence',
  description: 'A test sequence for demonstrating the workflow automation',
  status: 'ACTIVE',
  userId: '1'
};

const testSteps = [
  {
    name: 'Initial Email Outreach',
    order: 0,
    type: 'OUTREACH',
    medium: 'EMAIL',
    templateId: 1,
    roleSequenceId: null // Will be set after sequence creation
  },
  {
    name: 'Follow-up SMS',
    order: 1,
    type: 'FOLLOW_UP',
    medium: 'SMS',
    templateId: 2,
    roleSequenceId: null
  },
  {
    name: 'WhatsApp Message',
    order: 1, // Same order as SMS - will execute in parallel
    type: 'FOLLOW_UP',
    medium: 'WHATSAPP',
    templateId: 3,
    roleSequenceId: null
  },
  {
    name: 'LinkedIn Connection',
    order: 2,
    type: 'FOLLOW_UP',
    medium: 'LINKEDIN',
    templateId: 4,
    roleSequenceId: null
  },
  {
    name: 'Final Call',
    order: 3,
    type: 'REMINDER',
    medium: 'CALL',
    templateId: 5,
    roleSequenceId: null
  }
];

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest(method, url, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${url}:`, error.response || error.message);
    throw error;
  }
}

async function testSequenceExecution() {
  console.log('🚀 Starting Sequence Execution Test\n');

  try {
    // 1. Create a test sequence
    console.log('1. Creating test sequence...');
    const sequence = await makeRequest('POST', '/sequence/create', testSequence);
    console.log(`✅ Sequence created with ID: ${sequence.id}\n`);

    // 2. Create sequence steps
    console.log('2. Creating sequence steps...');
    for (const step of testSteps) {
      step.roleSequenceId = sequence.id;
      const createdStep = await makeRequest('POST', '/sequence-steps/create', step);
      console.log(`✅ Step created: ${createdStep.name} (Order: ${createdStep.order}, Medium: ${createdStep.medium})`);
    }
    console.log('');

    // 3. Get sequence with steps
    console.log('3. Retrieving sequence with steps...');
    const sequenceWithSteps = await makeRequest('GET', `/sequence/${sequence.id}/with-steps`);
    console.log(`✅ Sequence has ${sequenceWithSteps.sequenceSteps.length} steps\n`);

    // 4. Check queue statistics before starting
    console.log('4. Checking queue statistics...');
    const queueStats = await makeRequest('GET', '/queue/stats');
    console.log('📊 Queue Statistics:', JSON.stringify(queueStats, null, 2));
    console.log('');

    // 5. Start sequence for test candidates
    console.log('5. Starting sequence for test candidates...');
    const testCandidateIds = [1, 2, 3]; // Assuming these candidates exist
    const startResult = await makeRequest('POST', `/sequence/${sequence.id}/start`, {
      candidateIds: testCandidateIds
    });
    console.log(`✅ ${startResult.message}\n`);

    // 6. Wait a bit and check queue statistics again
    console.log('6. Waiting 5 seconds and checking queue activity...');
    await sleep(5000);
    
    const updatedQueueStats = await makeRequest('GET', '/queue/stats');
    console.log('📊 Updated Queue Statistics:', JSON.stringify(updatedQueueStats, null, 2));
    console.log('');

    // 7. Check candidate sequence status
    console.log('7. Checking candidate sequence status...');
    for (const candidateId of testCandidateIds) {
      try {
        const candidateStatus = await makeRequest('GET', `/candidate-sequence-status/candidate/${candidateId}/sequence/${sequence.id}`);
        console.log(`📋 Candidate ${candidateId} status:`, candidateStatus.map(s => ({
          stepOrder: s.stepOrder,
          status: s.status,
          medium: s.step?.medium
        })));
      } catch (error) {
        console.log(`⚠️ Could not get status for candidate ${candidateId}`);
      }
    }
    console.log('');

    // 8. Simulate webhook responses
    console.log('8. Simulating webhook responses...');
    
    // Simulate email reply for candidate 1
    await makeRequest('POST', '/webhooks/test/1/1', {
      event: 'replied',
      medium: 'email'
    });
    console.log('✅ Simulated email reply for candidate 1');

    // Simulate WhatsApp delivery for candidate 2
    await makeRequest('POST', '/webhooks/test/2/3', {
      event: 'delivered',
      medium: 'whatsapp'
    });
    console.log('✅ Simulated WhatsApp delivery for candidate 2');

    console.log('');

    // 9. Wait and check final statistics
    console.log('9. Final statistics check...');
    await sleep(3000);
    
    const finalQueueStats = await makeRequest('GET', '/queue/stats');
    console.log('📊 Final Queue Statistics:', JSON.stringify(finalQueueStats, null, 2));

    const sequenceStats = await makeRequest('GET', `/sequence/${sequence.id}/stats`);
    console.log('📈 Sequence Statistics:', JSON.stringify(sequenceStats, null, 2));

    console.log('\n🎉 Test completed successfully!');
    console.log('\n📝 What happened:');
    console.log('   1. Created a test sequence with 5 steps');
    console.log('   2. Steps were ordered to demonstrate parallel and sequential execution');
    console.log('   3. Started the sequence for 3 test candidates');
    console.log('   4. Jobs were queued and processed by workers');
    console.log('   5. Simulated webhook responses to trigger next steps');
    console.log('   6. System automatically progressed through the workflow');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testQueueManagement() {
  console.log('\n🔧 Testing Queue Management\n');

  try {
    // Test adding a job to each queue
    const queues = ['email', 'whatsapp', 'sms', 'call', 'linkedin'];
    
    for (const queue of queues) {
      console.log(`Adding test job to ${queue} queue...`);
      await makeRequest('POST', `/queue/${queue}-queue/test`, {
        candidateId: 999,
        stepId: 999
      });
      console.log(`✅ Test job added to ${queue} queue`);
    }

    // Check queue stats
    await sleep(2000);
    const stats = await makeRequest('GET', '/queue/stats');
    console.log('\n📊 Queue Statistics after test jobs:');
    console.log(JSON.stringify(stats, null, 2));

  } catch (error) {
    console.error('❌ Queue management test failed:', error.message);
  }
}

// Run the tests
async function runAllTests() {
  console.log('🧪 Recruitment Workflow Automation System - Test Suite\n');
  console.log('Make sure the application is running on https://19cxs75g-5001.inc1.devtunnels.ms\n');

  await testSequenceExecution();
  await testQueueManagement();

  console.log('\n✨ All tests completed!');
  console.log('\n🔗 Useful endpoints to explore:');
  console.log('   - GET /queue/stats - Monitor queue activity');
  console.log('   - GET /sequence/get-all - List all sequences');
  console.log('   - POST /webhooks/test/:candidateId/:stepId - Simulate responses');
  console.log('   - GET /candidate-sequence-status/status/PENDING - View pending steps');
}

// Check if axios is available
if (typeof require !== 'undefined') {
  runAllTests().catch(console.error);
} else {
  console.log('This script requires Node.js and axios. Run: npm install axios');
}
