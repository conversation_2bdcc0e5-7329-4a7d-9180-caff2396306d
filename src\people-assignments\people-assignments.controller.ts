import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UsePipes,
  ValidationPipe,
  Query,
} from '@nestjs/common';
import { PeopleAssignmentsService } from './people-assignments.service';
import { CreatePeopleAssignmentDto } from './dto/create-people-assignment.dto';
import { UpdatePeopleAssignmentDto } from './dto/update-people-assignment.dto';
import {
  AssignPersonsToUserDto,
  GetAssignedPersonsDto,
} from './dto/get-assigned-persons.dto';
import {
  AddEmailFromSpreadsheetDto,
  AddReplacementEmailsDto,
  MarkAsEmailNotFoundDto,
} from './dto/add-email-from-spreadsheet.dto';

@Controller('people-assignments')
export class PeopleAssignmentsController {
  constructor(
    private readonly peopleAssignmentsService: PeopleAssignmentsService,
  ) {}

  @Post()
  create(@Body() createPeopleAssignmentDto: CreatePeopleAssignmentDto) {
    return this.peopleAssignmentsService.create(createPeopleAssignmentDto);
  }

  @Post('addReplacementEmails')
  addReplacementEmails(@Body() data: AddReplacementEmailsDto) {
    return this.peopleAssignmentsService.addReplacementEmails(data);
  }

  @Post('addEmailsFromSpreadSheet')
  addEmailsFromSpreadSheet(@Body() data: AddEmailFromSpreadsheetDto) {
    return this.peopleAssignmentsService.addEmailsFromSpreadSheet(data);
  }

  @Post('markAsEmailNotFound')
  markAsEmailNotFound(@Body() data: MarkAsEmailNotFoundDto) {
    return this.peopleAssignmentsService.markAsEmailNotFound(data);
  }

  @Get('getAssignedPersonsByUserId')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async getAssignedPersons(@Query() query: GetAssignedPersonsDto) {
    return this.peopleAssignmentsService.getAssignedPersonsByUserId(query);
  }

  @Get('assignPersonsToUser')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async assignPersons(@Query() queryParams: AssignPersonsToUserDto) {
    return this.peopleAssignmentsService.assignPersonsByUserId(queryParams);
  }

  @Get('all')
  findAll() {
    console.log('I am called in controller in find all');
    return this.peopleAssignmentsService.findAll();
  }

  @Get('id/:id')
  findOne(@Param('id') id: string) {
    return this.peopleAssignmentsService.findOne(+id);
  }

  @Patch('id/:id')
  update(
    @Param('id') id: string,
    @Body() updatePeopleAssignmentDto: UpdatePeopleAssignmentDto,
  ) {
    return this.peopleAssignmentsService.update(+id, updatePeopleAssignmentDto);
  }

  @Delete('id/:id')
  remove(@Param('id') id: string) {
    return this.peopleAssignmentsService.remove(+id);
  }
}
