# Database Configuration
DB_TYPE=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=devxpert
DB_NAME=ultimatedb

# Redis Configuration for Queue System
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRATION=1d

# Application Configuration
PORT=5000
NODE_ENV=development

# Email Service Configuration (for actual email sending)
EMAIL_SERVICE_API_KEY=your-email-service-api-key
EMAIL_FROM_ADDRESS=<EMAIL>

# WhatsApp Service Configuration
WHATSAPP_API_KEY=your-whatsapp-api-key
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id

# SMS Service Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# LinkedIn API Configuration
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Call Service Configuration
CALL_SERVICE_API_KEY=your-call-service-api-key

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-for-verification