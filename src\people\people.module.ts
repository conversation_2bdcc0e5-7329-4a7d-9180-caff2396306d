import { Modu<PERSON> } from '@nestjs/common';
import { PeopleService } from './people.service';
import { PeopleController } from './people.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { People } from './people.entity';
import { Service } from 'src/service/service.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Experience } from 'src/experience/experience.entity';
import { Languages } from 'src/languages/langauges.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { Company } from 'src/company/company.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      Service,
      PersonSkill,
      Qualifications,
      Experience,
      Languages,
      RoleCandidate,
      Company,
      PeopleAssignment,
      MailBox,
    ]),
  ],
  providers: [PeopleService],
  controllers: [PeopleController],
})
export class PeopleModule {}
