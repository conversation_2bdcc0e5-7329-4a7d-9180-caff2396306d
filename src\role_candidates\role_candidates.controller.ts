import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RoleCandidatesService } from './role_candidates.service';
import { RoleCandidateDto } from './dto/role_candidate.dto';
import { UpdateConnectionStatusDto } from './dto/update-connection-status.dto';
import { RoleCandidate } from './role_candidates.entity';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('role candidates')
@Controller('role-candidates')
export class RoleCandidatesController {
  constructor(private readonly roleCandidatesService: RoleCandidatesService) {}

  private handleError(error: any) {
    if (error.status === 404) {
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
    throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  @Post('create')
  @ApiOperation({ summary: 'Create a new Role Candidate' })
  @ApiBody({ type: RoleCandidateDto })
  @ApiResponse({
    status: 201,
    description: 'The role candidate has been successfully created.',
    type: RoleCandidateDto,
  })
  @ApiResponse({ status: 404, description: 'Role not found.' })
  @ApiResponse({ status: 500, description: 'Internal Server Error.' })
  async createRoleCandidate(
    @Body() roleCandidateDto: RoleCandidateDto,
  ): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.createRoleCandidate(
        roleCandidateDto,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Post('add-linkedin-to-role')
  @ApiBody({
    type: RoleCandidateDto,
    description: 'Add LinkedIn profile to a role',
  })
  @ApiConsumes('application/json')
  @ApiOperation({ summary: 'Add LinkedIn profile to a role' })
  @ApiResponse({
    status: 201,
    description: 'LinkedIn profile added successfully',
    type: RoleCandidateDto,
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async addLinkedinToRole(
    @Body('businessEmail') businessEmail: string[],
    @Body('businessNumber') businessNumber: string[],
    @Body('personalEmail') businessName: string[],
    @Body('personalNumber') personalNumber: string[],
    @Body('profile_url') profile_url: string,
    @Body('profileType') profileType: string,
    @Body('roleId', ParseIntPipe) roleId: number,
    @Body('userId') userId: string,
    @Body('clientId') clientId?: number,
    @Body('prospectId') prospectId?: number,
  ): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.addLinkedinToRole(
        businessEmail,
        businessNumber,
        businessName,
        personalNumber,
        profile_url,
        profileType,
        roleId,
        userId,
        clientId,
        prospectId,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-role-candidates')
  @ApiOperation({ summary: 'Get all role candidates' })
  @ApiQuery({ name: 'roleId', required: true })
  @ApiQuery({ name: 'userId', required: true })
  @ApiQuery({ name: 'clientId', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'source', required: false })
  async getRoleCandidatesV1(
    @Query('roleId', ParseIntPipe) roleId: number,
    @Query('userId') userId: string,
    @Query('clientId') clientId?: number,
    @Query('type') type?: string,
    @Query('source') source?: string,
  ): Promise<RoleCandidate[]> {
    try {
      return await this.roleCandidatesService.getRoleCandidatesV1(
        roleId,
        userId,
        clientId,
        type,
        source,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-role-candidates-paginated')
  @ApiOperation({ summary: 'Get role candidates with pagination' })
  @ApiQuery({ name: 'roleId', required: true })
  @ApiQuery({ name: 'userId', required: true })
  @ApiQuery({ name: 'clientId', required: false })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiResponse({
    status: 200,
    description: 'Role candidates retrieved successfully with pagination',
  })
  async getRoleCandidates(
    @Query('roleId', ParseIntPipe) roleId: number,
    @Query('userId') userId: string,
    @Query('clientId') clientId?: number,
    @Query('type') type?: string,
    @Query('page', ParseIntPipe) page = 1,
    @Query('limit', ParseIntPipe) limit = 10,
  ): Promise<{ data: RoleCandidate[]; total: number }> {
    try {
      return await this.roleCandidatesService.getRoleCandidates(
        roleId,
        userId,
        clientId,
        type,
        page,
        limit,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('change-role-candidate-status')
  @ApiOperation({ summary: 'Change the status of a role candidate' })
  @ApiQuery({ name: 'roleId', required: true })
  @ApiQuery({ name: 'userId', required: true })
  @ApiQuery({ name: 'profile_url', required: false })
  async changeRoleCandidateStatus(
    @Query('roleId', ParseIntPipe) roleId: number,
    @Query('userId') userId: string,
    @Query('profile_url') profile_url?: string,
  ): Promise<RoleCandidate[]> {
    try {
      return await this.roleCandidatesService.changeRoleCandidateStatus(
        profile_url,
        roleId,
        userId,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Delete('delete-role-candidate')
  @ApiOperation({ summary: 'Delete a role candidate' })
  @ApiQuery({ name: 'id', required: true })
  @ApiResponse({
    status: 200,
    description: 'Role candidate deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Role candidate not found' })
  async deleteRoleCandidate(@Query('id', ParseIntPipe) id: number) {
    try {
      return await this.roleCandidatesService.deleteRoleCandidate(id);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Post('add-cv-to-role')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Add CV to a role' })
  @ApiResponse({
    status: 201,
    description: 'CV added successfully',
    type: RoleCandidateDto,
  })
  @ApiResponse({ status: 404, description: 'Role not found' })
  async addCvToRole(
    @UploadedFile() file: Express.Multer.File,
    @Body('data') data: string,
  ): Promise<RoleCandidate> {
    try {
      const parsedData = JSON.parse(data);
      return await this.roleCandidatesService.addCvToRole(parsedData, file);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-pending-linkedin-profile')
  @ApiOperation({ summary: 'Get pending LinkedIn profiles' })
  async getPendingLinkedinProfile(): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.getPendingLinkedinProfile();
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-role-candidate-count/:roleId')
  @ApiParam({ name: 'roleId', required: true, type: Number })
  @ApiOperation({ summary: 'Get LinkedIn and CV profile counts for a role' })
  async getRoleCandidateCount(
    @Param('roleId', ParseIntPipe) roleId: number,
  ): Promise<{
    linkedin_count: number;
    cv_count: number;
    total_count: number;
  }> {
    try {
      const countData =
        await this.roleCandidatesService.getRoleCandidateCount(roleId);
      return {
        ...countData,
        total_count: countData.linkedin_count + countData.cv_count,
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-360-and-pre-qualification-candidates/:roleId')
  @ApiParam({ name: 'roleId', required: true, type: Number })
  @ApiOperation({
    summary: 'Get 360 and pre-qualification candidates for a role',
  })
  async get360AndPreQualificationCandidates(
    @Param('roleId', ParseIntPipe) roleId: number,
  ): Promise<RoleCandidate[]> {
    try {
      return await this.roleCandidatesService.get360AndPreQualificationCandidates(
        roleId,
      );
    } catch (error) {
      this.handleError(error);
    }
  }

  @Put('mark-profile-ready-for-connection/:id')
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiOperation({
    summary: 'Mark a profile as ready to send connection request',
    description: 'Updates the LinkedIn connection send status to READY_TO_SEND for the specified role candidate',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile marked as ready for connection successfully',
    type: RoleCandidate,
  })
  @ApiResponse({ status: 404, description: 'Role candidate not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async markProfileReadyForConnection(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.markProfileReadyForConnection(id);
    } catch (error) {
      this.handleError(error);
    }
  }

  @Get('get-ready-to-send-connection-profile')
  @ApiOperation({
    summary: 'Get one profile ready to send connection request',
    description: 'Retrieves the oldest LinkedIn profile that is ready to send a connection request and marks it as SENT',
  })
  @ApiResponse({
    status: 200,
    description: 'Ready to send profile retrieved successfully',
    type: RoleCandidate,
  })
  @ApiResponse({ status: 404, description: 'No profiles ready to send connection request found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async getOneReadyToSendConnectionProfile(): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.getOneReadyToSendConnectionProfile();
    } catch (error) {
      this.handleError(error);
    }
  }

  @Put('update-connection-request-status/:id')
  @ApiParam({ name: 'id', required: true, type: Number })
  @ApiOperation({
    summary: 'Update connection request status',
    description: 'Updates the LinkedIn connection send status and optionally the response status for a role candidate',
  })
  @ApiBody({ type: UpdateConnectionStatusDto })
  @ApiResponse({
    status: 200,
    description: 'Connection request status updated successfully',
    type: RoleCandidate,
  })
  @ApiResponse({ status: 404, description: 'Role candidate not found' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  async updateConnectionRequestStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateConnectionStatusDto: UpdateConnectionStatusDto,
  ): Promise<RoleCandidate> {
    try {
      return await this.roleCandidatesService.updateConnectionRequestStatus(
        id,
        updateConnectionStatusDto.connectionStatus,
        updateConnectionStatusDto.responseStatus,
      );
    } catch (error) {
      this.handleError(error);
    }
  }
}
