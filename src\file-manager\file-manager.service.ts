import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Not, Like } from 'typeorm';
import { FileManager } from './file-manager.entity';
import { S3bucketService } from '../s3bucket/s3bucket.service';
import {
  CreateFolderDto,
  RenameFileDto,
  MoveToTrashDto,
  UploadFileDto,
  ListFilesDto,
} from './dto';

@Injectable()
export class FileManagerService {
  constructor(
    @InjectRepository(FileManager)
    private readonly fileManagerRepository: Repository<FileManager>,
    private readonly s3Service: S3bucketService,
  ) {}

  // Create a new folder
  async createFolder(userId: number, createFolderDto: CreateFolderDto): Promise<FileManager> {
    const { name, parentId, description } = createFolderDto;

    // Validate parent folder exists and belongs to user
    if (parentId) {
      const parentFolder = await this.findFileById(parentId, userId);
      if (!parentFolder.is_folder) {
        throw new BadRequestException('Parent must be a folder');
      }
    }

    // Check if folder with same name exists in the same parent
    const existingFolder = await this.fileManagerRepository.findOne({
      where: {
        user_id: userId,
        parent_id: parentId || IsNull(),
        name,
        is_folder: true,
        deleted_at: IsNull(),
      },
    });

    if (existingFolder) {
      throw new BadRequestException('Folder with this name already exists in this location');
    }

    // Create folder record
    const folder = this.fileManagerRepository.create({
      user_id: userId,
      name,
      is_folder: true,
      parent_id: parentId,
      description,
    });

    return await this.fileManagerRepository.save(folder);
  }

  // Get upload URL for a file
  async getUploadUrl(userId: number, uploadFileDto: UploadFileDto): Promise<{
    uploadUrl: string;
    fileId: number;
  }> {
    const { fileName, mimeType, fileSize, parentId, description } = uploadFileDto;

    // Validate parent folder exists and belongs to user
    if (parentId) {
      const parentFolder = await this.findFileById(parentId, userId);
      if (!parentFolder.is_folder) {
        throw new BadRequestException('Parent must be a folder');
      }
    }

    // Generate folder path for S3
    const folderPath = await this.generateS3FolderPath(userId, parentId);

    // Generate pre-signed upload URL
    const { uploadUrl, s3Key } = await this.s3Service.generateUploadUrl(
      fileName,
      mimeType,
      userId,
      folderPath,
    );

    // Create file record in database
    const file = this.fileManagerRepository.create({
      user_id: userId,
      name: fileName,
      is_folder: false,
      s3_key: s3Key,
      parent_id: parentId,
      mime_type: mimeType,
      file_size: fileSize,
      description,
      s3_url: `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${s3Key}`,
    });

    const savedFile = await this.fileManagerRepository.save(file);

    return {
      uploadUrl,
      fileId: savedFile.id,
    };
  }

  // Get download URL for a file
  async getDownloadUrl(fileId: number, userId: number): Promise<string> {
    const file = await this.findFileById(fileId, userId);

    if (file.is_folder) {
      throw new BadRequestException('Cannot download a folder');
    }

    if (!file.s3_key) {
      throw new BadRequestException('File has no S3 key');
    }

    return await this.s3Service.generateDownloadUrl(file.s3_key);
  }

  // List files and folders
  async listFiles(userId: number, listFilesDto: ListFilesDto): Promise<FileManager[]> {
    const { parentId, includeTrash, search } = listFilesDto;

    // Validate parent folder exists and belongs to user
    if (parentId) {
      await this.findFileById(parentId, userId);
    }

    const queryBuilder = this.fileManagerRepository
      .createQueryBuilder('file')
      .where('file.user_id = :userId', { userId })
      .andWhere('file.parent_id = :parentId', { parentId: parentId || null });

    // Include/exclude trash
    if (!includeTrash) {
      queryBuilder.andWhere('file.deleted_at IS NULL');
    }

    // Search functionality
    if (search) {
      queryBuilder.andWhere('file.name ILIKE :search', { search: `%${search}%` });
    }

    queryBuilder.orderBy('file.is_folder', 'DESC').addOrderBy('file.name', 'ASC');

    return await queryBuilder.getMany();
  }

  // Rename a file or folder
  async renameFile(fileId: number, userId: number, renameFileDto: RenameFileDto): Promise<FileManager> {
    const { newName } = renameFileDto;
    const file = await this.findFileById(fileId, userId);

    // Check if new name already exists in the same parent
    const existingFile = await this.fileManagerRepository.findOne({
      where: {
        user_id: userId,
        parent_id: file.parent_id || IsNull(),
        name: newName,
        deleted_at: IsNull(),
      },
    });

    if (existingFile && existingFile.id !== fileId) {
      throw new BadRequestException('File with this name already exists in this location');
    }

    // If it's a file, we need to copy it in S3 with new name
    if (!file.is_folder && file.s3_key) {
      const folderPath = await this.generateS3FolderPath(userId, file.parent_id);
      const newS3Key = `users/${userId}/${folderPath}${folderPath ? '/' : ''}${Date.now()}-${newName}`;

      await this.s3Service.copyObject(file.s3_key, newS3Key);
      await this.s3Service.deleteObjectByKey(file.s3_key);

      file.s3_key = newS3Key;
      file.s3_url = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${newS3Key}`;
    }

    file.name = newName;
    return await this.fileManagerRepository.save(file);
  }

  // Move files/folders to trash
  async moveToTrash(userId: number, moveToTrashDto: MoveToTrashDto): Promise<void> {
    const { fileIds } = moveToTrashDto;

    for (const fileId of fileIds) {
      const file = await this.findFileById(fileId, userId);
      file.deleted_at = new Date();
      await this.fileManagerRepository.save(file);
    }
  }

  // Restore files/folders from trash
  async restoreFromTrash(fileId: number, userId: number): Promise<FileManager> {
    const file = await this.findFileById(fileId, userId);

    if (!file.deleted_at) {
      throw new BadRequestException('File is not in trash');
    }

    file.deleted_at = null;
    return await this.fileManagerRepository.save(file);
  }

  // Permanently delete files/folders
  async permanentlyDelete(fileId: number, userId: number): Promise<void> {
    const file = await this.findFileById(fileId, userId);

    // Delete from S3 if it's a file
    if (!file.is_folder && file.s3_key) {
      await this.s3Service.deleteObjectByKey(file.s3_key);
    }

    // Delete from database
    await this.fileManagerRepository.remove(file);
  }

  // Get file/folder by ID with user validation
  private async findFileById(fileId: number, userId: number): Promise<FileManager> {
    const file = await this.fileManagerRepository.findOne({
      where: { id: fileId, user_id: userId },
    });

    if (!file) {
      throw new NotFoundException('File or folder not found');
    }

    return file;
  }

  // Generate S3 folder path based on parent hierarchy
  private async generateS3FolderPath(userId: number, parentId?: number): Promise<string> {
    if (!parentId) {
      return '';
    }

    const pathParts: string[] = [];
    let currentParentId = parentId;

    while (currentParentId) {
      const parent = await this.findFileById(currentParentId, userId);
      pathParts.unshift(parent.name);
      currentParentId = parent.parent_id;
    }

    return pathParts.join('/');
  }
}
