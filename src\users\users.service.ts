import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { Users } from './users.entity';
import { EmailService } from 'src/email/email.service';
import { CreateUserDto } from './dto/create-user.dto';
import * as crypto from 'crypto';
import { ResetPasswordTokenDto } from './dto/resetPasswordToken.dto';
import { addMinutes } from 'date-fns';
import { UpdateUserDto } from './dto/updateUser.dto';
import { GetAllUsersDto } from './dto/getAllUsers.dto';
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(Users)
    private usersRepository: Repository<Users>,
    private emailService: EmailService,
  ) {}

  async findOne(email: string): Promise<Users | undefined> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findOneAndUpdateVerification(
    email: string,
  ): Promise<Users | undefined> {
    const user = await this.usersRepository.findOne({ where: { email } });

    if (!user) return undefined;

    const verificationCode = crypto.randomInt(100000, 999999).toString(); // Generates a 6-digit number
    const verificationExpires = addMinutes(new Date(), 10); // Expires in 10 minutes

    await this.usersRepository.update(user.id, {
      verification_code: verificationCode,
      verification_code_expires: verificationExpires,
    });

    return { ...user, verification_code: verificationCode }; // Include the code for sending it
  }

  async verifyVerificationCode(
    email: string,
    code: string,
  ): Promise<Users | undefined> {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) return undefined;

    if (user.verification_code !== code) {
      throw new BadRequestException('Invalid verification code');
    }

    if (user.verification_code_expires < new Date()) {
      throw new BadRequestException('Verification code has expired');
    }

    await this.usersRepository.update(user.id, {
      verification_code: null,
      verification_code_expires: null,
      email_verified: true,
    });

    return user;
  }

  async createUser(createUserDto: CreateUserDto): Promise<Users> {
    // Check if email already exists
    const existingUser = await this.usersRepository.findOne({
      where: { email: createUserDto.email },
    });
    if (existingUser) {
      throw new BadRequestException('Email already in use.');
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(createUserDto.password, salt);

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationExpires = new Date();
    verificationExpires.setHours(verificationExpires.getHours() + 24); // Expires in 24 hours

    // Create user entity
    const newUser = this.usersRepository.create({
      ...createUserDto,
      password_salt: salt,
      password_hash: hashedPassword,
      password_original: createUserDto.password,
      email_verification_token: verificationToken,
      email_verification_expires: verificationExpires,
    });

    const savedUser = await this.usersRepository.save(newUser);

    // Send verification email
    await this.sendVerificationEmail(
      savedUser.email,
      verificationToken,
      createUserDto.source,
    );

    return savedUser;
  }

  async sendVerificationEmail(
    email: string,
    token: string,
    source: string,
  ): Promise<void> {
    // const verificationLink = `${process.env.FRONTEND_URL_LOCAL_REDIRECT}/verify-email/${token}`;
    const devCrmUrl = process.env.DEV_REDIRECT_CRM;
    const devWebUrl = process.env.DEV_REDIRECT_WEB;
    const prodCrmUrl = process.env.PROD_REDIRECT_CRM;
    const prodWebUrl = process.env.PROD_REDIRECT_WEB;
    let verificationLink = null;
    if (source === 'CRM') {
      verificationLink =
        process.env.NODE_ENV === 'production'
          ? `${prodCrmUrl}/verify-email/${token}`
          : `${devCrmUrl}/verify-email/${token}`;
    } else if (source === 'WEBSITE') {
      verificationLink =
        process.env.NODE_ENV === 'production'
          ? `${prodWebUrl}/verify-email/${token}`
          : `${devWebUrl}/verify-email/${token}`;
    }

    console.log(
      'Verification Link:',
      verificationLink,
      '------ for source -----',
      source,
    ); // Debugging line
    const emailBody = `
      <h1>Email Verification</h1>
      <p>Click the link below to verify your email:</p>
      <a href="${
        verificationLink ? verificationLink : null
      }" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 16px; margin-top: 10px; border: none; cursor: pointer;"
      }">Click here to verify</a>
      <p>This link will expire in 24 hours.</p>
    `;

    await this.emailService.sendEmail({
      to: [email],
      subject: 'Verify Your Email',
      body: emailBody,
    });
  }

  async sendVerificationCode(email: string, code: string): Promise<void> {
    const emailBody = `
      <h1>Email Verification Code</h1>
      <p>Your verification code is: ${code}</p>
      <p>This code will expire in 10 minutes.</p>
    `;

    await this.emailService.sendEmail({
      to: [email],
      subject: 'Your Verification Code',
      body: emailBody,
    });
  }

  async verifyEmail(token: string): Promise<void> {
    const user = await this.usersRepository.findOne({
      where: { email_verification_token: token },
    });
    if (!user) {
      throw new BadRequestException('Invalid verification token.');
    }

    if (user.email_verification_expires < new Date()) {
      throw new BadRequestException('Verification token has expired.');
    }

    await this.usersRepository.update(
      { id: user.id },
      { email_verified: true },
    );

    // Clear verification token

    await this.usersRepository.update(
      { id: user.id },
      { email_verification_token: null, email_verification_expires: null },
    );

    return;
  }

  async resendVerificationEmail(email: string, source: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email already verified.');
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const verificationExpires = new Date();
    verificationExpires.setHours(verificationExpires.getHours() + 24); // Expires in 24 hours

    // Update user entity
    await this.usersRepository.update(
      { id: user.id },
      {
        email_verification_token: verificationToken,
        email_verification_expires: verificationExpires,
      },
    );

    // Send verification email
    await this.sendVerificationEmail(email, verificationToken, source);

    return;
  }

  async resendVerificationCode(email: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    // Generate new verification code
    const verificationCode = crypto.randomInt(100000, 999999).toString(); // Generates a 6-digit number
    const verificationExpires = addMinutes(new Date(), 10); // Expires in 10 minutes

    // Update user entity
    await this.usersRepository.update(user.id, {
      verification_code: verificationCode,
      verification_code_expires: verificationExpires,
    });

    // Send verification email with new code
    await this.sendVerificationCode(email, verificationCode);

    return;
  }

  async forgotPassword(email: string, source: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { email } });
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    // Generate password reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date();
    resetExpires.setHours(resetExpires.getHours() + 24); // Expires in 24 hours

    // Update user entity
    await this.usersRepository.update(
      { id: user.id },
      {
        password_reset_token: resetToken,
        password_reset_expires: resetExpires,
      },
    );

    // Send password reset email
    await this.sendPasswordResetEmail(email, resetToken, source);

    return;
  }

  async sendPasswordResetEmail(
    email: string,
    token: string,
    source: string,
  ): Promise<void> {
    const devCrmUrl = process.env.DEV_REDIRECT_CRM;
    const devWebUrl = process.env.DEV_REDIRECT_WEB;
    const prodCrmUrl = process.env.PROD_REDIRECT_CRM;
    const prodWebUrl = process.env.PROD_REDIRECT_WEB;
    let resetLink = null;
    if (source === 'CRM') {
      resetLink =
        process.env.NODE_ENV === 'production'
          ? `${prodCrmUrl}/reset-password/${token}`
          : `${devCrmUrl}/reset-password/${token}`;
    } else if (source === 'WEBSITE') {
      resetLink =
        process.env.NODE_ENV === 'production'
          ? `${prodWebUrl}/reset-password/${token}`
          : `${devWebUrl}/reset-password/${token}`;
    }

    const emailBody = `
      <h1>Password Reset</h1>
      <p>Click the link below to reset your password:</p>
      <a href="${resetLink}">Click here to reset your password</a>
      <p>This link will expire in 24 hours.</p>
    `;

    await this.emailService.sendEmail({
      to: [email],
      subject: 'Reset Your Password',
      body: emailBody,
    });
  }

  // async updateUser(updateUserDto: UpdateUserDto): Promise<Users> {
  //   const { id, ...updateData } = updateUserDto;

  //   const user = await this.usersRepository.findOne({ where: { id } });
  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }

  //   await this.usersRepository.update(id, updateData);

  //   return this.usersRepository.findOne({ where: { id } });
  // }

  async updateUser(id: string, user: UpdateUserDto): Promise<Users> {
    if (!id) {
      throw new BadRequestException('Invalid user ID.');
    }

    const existingUser = await this.usersRepository.findOne({ where: { id } });
    if (!existingUser) {
      throw new BadRequestException('User not found.');
    }

    // Ensure user object is not empty
    const updatePayload = { ...user };
    if (Object.keys(updatePayload).length === 0) {
      throw new BadRequestException('Update payload is empty.');
    }

    // Hash password if provided
    if (user.password) {
      const salt = await bcrypt.genSalt(10);
      updatePayload.password_hash = await bcrypt.hash(user.password, salt);
      updatePayload.password_salt = existingUser.password_salt;
      updatePayload.password_original = user.password;
    }

    // Perform update
    await this.usersRepository.update({ id }, updatePayload);

    return this.usersRepository.findOne({ where: { id } });
  }

  async resetPasword(resetPasswordDto: ResetPasswordTokenDto): Promise<void> {
    const { token, password } = resetPasswordDto;
    const user = await this.usersRepository.findOne({
      where: { password_reset_token: token },
    });
    if (!user) {
      throw new BadRequestException('Invalid reset token.');
    }

    if (user.password_reset_expires < new Date()) {
      throw new BadRequestException('Reset token has expired.');
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    await this.usersRepository.update(
      { id: user.id },
      {
        password_original: password,
        password_salt: salt,
        password_hash: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
      },
    );

    return;
  }

  async findUserById(id: string): Promise<Users> {
    return this.usersRepository.findOne({ where: { id } });
  }

  async findAllUsers(data: GetAllUsersDto) {
    console.log(data);
    const { pageNumber, pageSize, searchString, userStatus, userRole } = data;

    const limit = pageSize;
    const skip = pageNumber * limit;

    let whereCondition = {} as any;

    if (searchString && searchString.trim()) {
      const searchConditions: any = [
        { username: ILike(`%${searchString}%`) },
        { first_name: ILike(`%${searchString}%`) },
        { last_name: ILike(`%${searchString}%`) },
        { cnic: ILike(`%${searchString}%`) },
        { phone_number: ILike(`%${searchString}%`) },
        { email: ILike(`%${searchString}%`) },
        { working_mode: ILike(`%${searchString}%`) },
      ];
      if (!isNaN(Number(searchString))) {
        searchConditions.push({ cninc: Number(searchString) });
        searchConditions.push({ phone_number: Number(searchString) });
      }
      whereCondition = searchConditions.map((condition: any) => ({
        ...whereCondition,
        ...condition,
      }));
    }

    if (userStatus === 'all') {
      whereCondition = {};
    } else if (userStatus === 'approved') {
      whereCondition.status = 'APPROVED';
    } else if (userStatus === 'pending') {
      whereCondition.status = 'PENDING';
    }

    // if (userRole) {
    //   whereCondition.designation = userRole;
    // }

    try {
      const allUsers = await this.usersRepository.findAndCount({
        where: whereCondition,
        take: limit,
        skip: skip,
        order: {
          created_at: 'DESC',
        },
      });

      return {
        users: allUsers[0],
        totalCount: allUsers[1],
        pageNumber: pageNumber,
        pageSize: pageSize,
      };
    } catch (error) {
      console.log(error);
      throw new Error('Failed to fetch users' + error.message);
    }
  }

  async deleteAllUsers(): Promise<void> {
    await this.usersRepository.clear();
  }

  async getAllUsersByFilters(
    role?: string,
    designation?: string,
    status?: string,
    page: number = 0,
    pageSize: number = 10,
    searchString: string = null,
  ): Promise<Users[]> {
    try {
      const queryBuilder = this.usersRepository.createQueryBuilder('user');

      if (role) {
        queryBuilder.andWhere('user.role = :role', { role });
      }

      if (designation) {
        queryBuilder.andWhere('user.designation = :designation', {
          designation,
        });
      }

      if (status) {
        queryBuilder.andWhere('user.status = :status', { status });
      }

      if (searchString) {
        queryBuilder.andWhere(
          '(user.first_name LIKE :search OR user.last_name LIKE :search OR user.email LIKE :search)',
          { search: `%${searchString}%` },
        );
      }

      queryBuilder.skip(page * pageSize).take(pageSize);

      return await queryBuilder.getMany();
    } catch (error) {
      console.error('Error fetching users:', error);
      throw new InternalServerErrorException(
        'An error occurred while fetching users.',
      );
    }
  }

  async updateUserStatus(id: string, status: string): Promise<Users> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.update(id, { status });

    return this.usersRepository.findOne({ where: { id } });
  }

  async deleteUser(id: string): Promise<void> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    await this.usersRepository.delete(id);
  }

  async getUsersByDesignation(designation: string = null): Promise<Users[]> {
    try {
      const queryBuilder = this.usersRepository.createQueryBuilder('user');

      if (designation) {
        queryBuilder.andWhere('user.designation = :designation', {
          designation,
        });
      }

      return await queryBuilder.getMany();
    } catch (error) {}
  }
}
