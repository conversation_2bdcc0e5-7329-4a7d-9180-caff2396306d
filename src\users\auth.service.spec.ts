import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';

import { AuthService } from './auth.service';
import { EmailService } from '../email/email.service';
import { SendEmailDto } from '../email/dto/sendEmail.dto';

describe('AuthService', () => {
  let service: AuthService;
  let jwtService: JwtService;
  let emailService: EmailService;

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
    emailService = module.get<EmailService>(EmailService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should generate JWT token successfully', async () => {
      const mockToken = 'jwt-token-123';
      mockJwtService.sign.mockReturnValue(mockToken);

      const result = await service.login(
        '<EMAIL>',
        'password123',
        'user-id-123',
        'John Doe',
        'USER',
        'RECRUITER',
      );

      expect(result).toEqual({ token: mockToken });
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        email: '<EMAIL>',
        full_name: 'John Doe',
        role: 'USER',
        designation: 'RECRUITER',
        userId: 'user-id-123',
      });
    });

    it('should handle different user roles and designations', async () => {
      const mockToken = 'admin-jwt-token';
      mockJwtService.sign.mockReturnValue(mockToken);

      const result = await service.login(
        '<EMAIL>',
        'adminpass',
        'admin-id',
        'Admin User',
        'ADMIN',
        'PROJECT_MANAGER',
      );

      expect(result).toEqual({ token: mockToken });
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        email: '<EMAIL>',
        full_name: 'Admin User',
        role: 'ADMIN',
        designation: 'PROJECT_MANAGER',
        userId: 'admin-id',
      });
    });
  });

  describe('sendVerificationCode', () => {
    it('should send verification code email successfully', async () => {
      const email = '<EMAIL>';
      const verificationCode = '123456';
      const verificationCodeExpires = new Date();
      
      mockEmailService.sendEmail.mockResolvedValue(true);

      await service.sendVerificationCode(email, verificationCode, verificationCodeExpires);

      const expectedEmailDto: SendEmailDto = {
        to: [email],
        subject: 'Login Verification Code',
        body: `Your login verification code is ${verificationCode}. It expires at ${verificationCodeExpires}`,
        from: process.env.DEFAULT_FROM_EMAIL,
      };

      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(expectedEmailDto);
    });

    it('should handle email service errors', async () => {
      const email = '<EMAIL>';
      const verificationCode = '123456';
      const verificationCodeExpires = new Date();
      
      mockEmailService.sendEmail.mockRejectedValue(new Error('Email service error'));

      await expect(
        service.sendVerificationCode(email, verificationCode, verificationCodeExpires),
      ).rejects.toThrow('Email service error');
    });

    it('should use environment variable for from email', async () => {
      const originalEnv = process.env.DEFAULT_FROM_EMAIL;
      process.env.DEFAULT_FROM_EMAIL = '<EMAIL>';

      const email = '<EMAIL>';
      const verificationCode = '123456';
      const verificationCodeExpires = new Date();
      
      mockEmailService.sendEmail.mockResolvedValue(true);

      await service.sendVerificationCode(email, verificationCode, verificationCodeExpires);

      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          from: '<EMAIL>',
        }),
      );

      // Restore original environment variable
      process.env.DEFAULT_FROM_EMAIL = originalEnv;
    });
  });

  describe('comparePassword', () => {
    it('should return true for matching passwords', async () => {
      const result = await service.comparePassword('password123', 'password123');

      expect(result).toBe(true);
    });

    it('should return false for non-matching passwords', async () => {
      const result = await service.comparePassword('password123', 'wrongpassword');

      expect(result).toBe(false);
    });

    it('should handle empty passwords', async () => {
      const result = await service.comparePassword('', '');

      expect(result).toBe(true);
    });

    it('should handle null/undefined passwords', async () => {
      const result1 = await service.comparePassword(null as any, 'password');
      const result2 = await service.comparePassword('password', null as any);
      const result3 = await service.comparePassword(undefined as any, undefined as any);

      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(result3).toBe(true);
    });
  });

  describe('service initialization', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have jwtService injected', () => {
      expect(jwtService).toBeDefined();
    });

    it('should have emailService injected', () => {
      expect(emailService).toBeDefined();
    });
  });
});
