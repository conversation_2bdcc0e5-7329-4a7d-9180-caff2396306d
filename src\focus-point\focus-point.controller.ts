import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { FocusPointService } from './focus-point.service';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { FocusPointDto } from './dto/focusPoint.dto';
import { UpdateFocusPointDto } from './dto/updateFocusPoint.dto';

@ApiTags('Focus Point')
@Controller('focus-point')
export class FocusPointController {
  constructor(
    private readonly focusPointService: FocusPointService, // Inject the service here
  ) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new focus point' })
  @ApiBody({
    type: FocusPointDto,
    description: 'Focus point data',
  })
  async createFocusPoint(@Body() focusPointDto: FocusPointDto) {
    return this.focusPointService.createFocusPoint(focusPointDto);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all focus points' })
  @ApiQuery({ name: 'searchString', required: false, type: String })
  @ApiQuery({ name: 'roleId', required: false, type: String })
  async getAllFocusPoints(
    @Query('searchString') searchString: string = null,
    @Query('roleId') roleId: string = null,
  ) {
    return this.focusPointService.getAllFocusPoints(
      searchString,
      roleId,
    );
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a focus point' })
  async deleteFocusPoint(@Param('id') id: string) {
    return this.focusPointService.deleteFocusPoint(id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a focus point by ID' })
  async getFocusPointById(@Param('id') id: string) {
    return this.focusPointService.getFocusPointById(id);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a focus point' })
  async updateFocusPoint(
    @Param('id') id: string,
    focusPointDto: UpdateFocusPointDto,
  ) {
    return this.focusPointService.updateFocusPoint(id, focusPointDto);
  }
}
