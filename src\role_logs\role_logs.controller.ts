import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { RoleLogsService } from './role_logs.service';
import { RoleLogsDto } from './dto/roleLogs.dto';
import { RoleLogs } from './role_logs.entity';
import { UpdateRoleLogsDto } from './dto/updateRoleLogs.dto';
import { GetTrialLogsByRoleIdDto } from './dto/role_logs.dto';

@ApiTags('RoleLogs')
@Controller('role-logs')
export class RoleLogsController {
  constructor(private readonly roleLogsService: RoleLogsService) {}

  @ApiOperation({ summary: 'Create a new role log' })
  @Post()
  async createRoleLog(@Body() roleLogDto: RoleLogsDto): Promise<RoleLogs> {
    return this.roleLogsService.createRoleLog(roleLogDto);
  }

  @ApiOperation({ summary: 'Get all role logs' })
  @Get()
  async getAllRoleLogs(@Query() query: any): Promise<RoleLogs[]> {
    return this.roleLogsService.getRoleLogs();
  }

  // startRole
  @ApiOperation({ summary: 'Start a role log' })
  @Post('start-role')
  @ApiQuery({
    name: 'roleId',
    required: true,
    type: Number,
  })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: String,
  })
  async startRoleLog(
    @Body('roleId') roleId: number,
    @Body('userId') userId: string,
  ): Promise<RoleLogs> {
    if (!roleId || !userId) {
      throw new Error('roleId and userId are required');
    }
    return this.roleLogsService.startRole(roleId, userId);
  }

  // mark role done
  @ApiOperation({ summary: 'Mark Role Done' })
  @Post('mark-role-done')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        roleId: { type: 'number' },
        userId: { type: 'string' },
      },
    },
  })
  async markRoleDone(
    @Body('roleId') roleId: number,
    @Body('userId') userId: string,
  ): Promise<RoleLogs> {
    if (!roleId || !userId) {
      throw new Error('roleId and userId are required');
    }
    return this.roleLogsService.MarkRoleDone(roleId, userId);
  }

  // mark role done
  @ApiOperation({ summary: 'Leave a role' })
  @Post('leave-role')
  @ApiQuery({
    name: 'roleId',
    required: true,
    type: Number,
  })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: String,
  })
  async markRoleLeft(
    @Body('roleId') roleId: number,
    @Body('userId') userId: string,
  ): Promise<RoleLogs> {
    if (!roleId || !userId) {
      throw new Error('roleId and userId are required');
    }
    return this.roleLogsService.MarkRoleLeft(roleId, userId);
  }
  

  @Get('getTotalTrialLogs')
  @ApiOperation({ summary: 'Get trial logs by roleId' })
  @ApiQuery({
    name: 'roleNumber',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'clientNumber',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'roleDate',
    required: false,
    type: Date,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    type: Date,
  })
  @ApiQuery({
    name: 'isAdvance',
    required: false,
    type: Boolean,
  })
  @ApiQuery({
    name: 'isPrevious',
    required: false,
    type: Boolean,
  })
  @ApiQuery({
    name: 'bdUserId',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'serviceId',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'acmUserId',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'roleId',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'searchString',
    required: false,
    type: String,
  })
  async getTrialLogsByRoleId(@Query() queryParams: GetTrialLogsByRoleIdDto): Promise<{
    cvsourcingRoles: RoleLogs[];
    preQualificationRoles: RoleLogs[];
    directRoles: RoleLogs[];
    trialRoles: RoleLogs[];
  }> {
    return this.roleLogsService.getTotalTrialLogs(queryParams);
  }

  // markRole Checked
  @Post('mark-role-checked')
  @ApiOperation({ summary: 'mark done in a role log' })
  @ApiQuery({
    name: 'roleId',
    required: true,
    type: Number,
  })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: String,
  })
  async markRoleChecked(
    @Body('roleId') roleId: number,
    @Body('userId') userId: string,
  ): Promise<RoleLogs> {
    if (!roleId || !userId) {
      throw new Error('roleId and userId are required');
    }
    return this.roleLogsService.markRoleChecked(roleId, userId);
  }

  @ApiOperation({ summary: 'Get a role log by ID' })
  @Get(':id')
  async getRoleLogById(@Param('id') id: number): Promise<RoleLogs> {
    return this.roleLogsService.getRoleLogById(id);
  }

  @ApiOperation({ summary: 'Update a role log by ID' })
  @Put(':id')
  async updateRoleLog(
    @Param('id') id: number,
    @Body() updateRoleLogDto: UpdateRoleLogsDto,
  ): Promise<RoleLogs> {
    return this.roleLogsService.updateRoleLog(id, updateRoleLogDto);
  }

  @ApiOperation({ summary: 'Delete a role log by ID' })
  @Delete(':id')
  async deleteRoleLog(@Param('id') id: number): Promise<void> {
    return this.roleLogsService.deleteRoleLog(id);
  }

  @Get(':roleId')
  @ApiOperation({ summary: 'Get role logs by roleId' })
  async getRoleLogsByRoleId(
    @Param('roleId') roleId: number,
  ): Promise<RoleLogs[]> {
    return this.roleLogsService.getRoleLogsByRoleId(roleId);
  }
  @Get('role-activity/:roleId')
  @ApiOperation({ summary: 'Get role logs by roleId' })
  async getRoleActivityByRoleId(
    @Param('roleId') roleId: number,
  ): Promise<RoleLogs[]> {
    return this.roleLogsService.getRoleActivityByRoleId(roleId);
  }

  @Put('change-role-status/:roleLogId')
  @ApiOperation({ summary: 'Change role status' })
  async changeRoleStatus(
    @Param('roleLogId') roleLogId: number,
    @Body() status: string,
  ): Promise<RoleLogs> {
    return this.roleLogsService.changeRoleLogStatus(roleLogId, status);
  }
}
