import { Module } from '@nestjs/common';
import { RoleLogsService } from './role_logs.service';
import { RoleLogsController } from './role_logs.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleLogs } from './role_logs.entity';
import { Roles } from 'src/roles/roles.entity';
import { Users } from 'src/users/users.entity';

@Module({
  imports: [TypeOrmModule.forFeature([RoleLogs, Roles, Users])],
  providers: [RoleLogsService],
  controllers: [RoleLogsController],
})
export class RoleLogsModule {}
