import { Module } from '@nestjs/common';
import { CandidateApplicationController } from './candidate-application.controller';
import { CandidateApplicationService } from './candidate-application.service';
import { EmailModule } from '../email/email.module';
import { S3bucketModule } from '../s3bucket/s3bucket.module';

@Module({
  imports: [EmailModule, S3bucketModule],
  controllers: [CandidateApplicationController],
  providers: [CandidateApplicationService],
  exports: [CandidateApplicationService],
})
export class CandidateApplicationModule {}