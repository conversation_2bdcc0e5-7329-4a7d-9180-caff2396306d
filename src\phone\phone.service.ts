import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { People } from 'src/people/people.entity';
import { PersonPhone } from './phone.entity';
import { PersonPhoneDto } from './dto/person-phone.dto';

@Injectable()
export class PersonPhoneService {
  constructor(
    @InjectRepository(PersonPhone)
    private readonly personPhoneRepository: Repository<PersonPhone>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
  ) {}

  async create(personId: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone> {
    const person = await this.peopleRepository.findOne({ where: { id: personId } });
    if (!person) {
      throw new Error('Person not found');
    }

    const personPhone = this.personPhoneRepository.create({
      ...personPhoneDto,
      person,
    });

    return this.personPhoneRepository.save(personPhone);
  }

  async findAll(): Promise<PersonPhone[]> {
    return this.personPhoneRepository.find({ relations: ['person'] });
  }

  async findOne(id: number): Promise<PersonPhone> {
    return this.personPhoneRepository.findOne({ where: { id }, relations: ['person'] });
  }

  async update(id: number, personPhoneDto: PersonPhoneDto): Promise<PersonPhone> {
    await this.personPhoneRepository.update(id, personPhoneDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.personPhoneRepository.delete(id);
  }
}