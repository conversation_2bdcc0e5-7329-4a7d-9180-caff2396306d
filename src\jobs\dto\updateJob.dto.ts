import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateJobDto } from './createJob.dto';
import { IsDate, isNotEmpty, IsNumber } from 'class-validator';

export class UpdateJobDto extends PartialType(CreateJobDto) {
  @ApiProperty({
    example: '2025-07-01',
    description: 'Job posting date',
    required: true,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    example: '2025-07-01',
    description: 'Job closing date',
    required: true,
  })
  @IsDate()
  job_closing_date: Date;
}
