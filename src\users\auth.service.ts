import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SendEmailDto } from 'src/email/dto/sendEmail.dto';
import { EmailService } from 'src/email/email.service';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private emailService: EmailService,
  ) {}

  async login(
    email: string,
    password: string,
    userId: string,
    full_name: string,
    role: string,
    designation: string,
    working_mode: string,
  ) {
    const payload = {
      email,
      full_name,
      role,
      designation,
      working_mode,
      userId,
    };
    return {
      user: payload,
      token: this.jwtService.sign(payload),
    };
  }

  async sendVerificationCode(
    email: string,
    verificationCode: string,
    verificationCodeExpires: Date,
  ) {
    const sendEmailDto: SendEmailDto = {
      to: [email],
      subject: 'Login Verification Code',
      body: `Your login verification code is ${verificationCode}. It expires at ${verificationCodeExpires}`,
      from: process.env.DEFAULT_FROM_EMAIL,
    };

    // Assuming emailService is injected and available
    await this.emailService.sendEmail(sendEmailDto);
  }
  async comparePassword(enteredPassword: string, dbPassword: string) {
    return enteredPassword === dbPassword;
  }
}
