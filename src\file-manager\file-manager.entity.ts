import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { Users } from '../users/users.entity';

@Entity('file_manager')
@Index(['user_id', 'parent_id']) // Optimize queries for user's files in specific folders
@Index(['user_id', 'is_folder']) // Optimize queries for user's folders
@Index(['s3_key'], { unique: true, where: 's3_key IS NOT NULL' }) // Ensure unique S3 keys
export class FileManager {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: false })
  @Index()
  user_id: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ type: 'boolean', default: false })
  is_folder: boolean;

  @Column({ type: 'varchar', length: 500, nullable: true })
  s3_key: string; // S3 object key (null for folders)

  @Column({ type: 'int', nullable: true })
  @Index()
  parent_id: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  mime_type: string; // File MIME type

  @Column({ type: 'bigint', nullable: true })
  file_size: number; // File size in bytes

  @Column({ type: 'varchar', length: 500, nullable: true })
  s3_url: string; // Full S3 URL for quick access

  @Column({ type: 'text', nullable: true })
  description: string; // Optional file/folder description

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>; // Additional metadata (tags, custom properties, etc.)

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  deleted_at: Date; // Soft delete timestamp

  // Relations
  @ManyToOne(() => Users, (user) => user.fileManagerItems, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'user_id' })
  user: Users;

  @ManyToOne(() => FileManager, (fileManager) => fileManager.children, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'parent_id' })
  parent: FileManager;

  @OneToMany(() => FileManager, (fileManager) => fileManager.parent)
  children: FileManager[];

  // Virtual properties
  get isInTrash(): boolean {
    return this.deleted_at !== null;
  }

  get fullPath(): string {
    // This would need to be computed by traversing up the parent chain
    // Implementation would be in the service layer
    return this.s3_key || '';
  }
}
