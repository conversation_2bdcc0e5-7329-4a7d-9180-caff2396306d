import { Module } from '@nestjs/common';
import { SkillsService } from './skills.service';
import { SkillsController } from './skills.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PersonSkill } from './skills.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PersonSkill])],
  providers: [SkillsService],
  controllers: [SkillsController],
})
export class SkillsModule {}
