import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsNotEmpty } from 'class-validator';

export enum PersonEmailType {
  PERSONAL = 'PERSONAL',
  BUSINESS = 'BUSINESS',
}


export class PersonEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the person',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'PERSONAL',
    description: 'Type of email (PERSONAL or BUSINESS)',
    enum: PersonEmailType,
  })
  @IsEnum(PersonEmailType)
  email_type: PersonEmailType;
}