import { Process, Processor, InjectQueue } from '@nestjs/bull';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { QueueJobData } from '../queue.service';
import { EmailService } from '../../email/email.service';
import { CandidateSequenceStatusService } from '../../candidate-sequence-status/candidate-sequence-status.service';
import { SequenceStepStatus } from '../../candidate-sequence-status/candidate-sequence-status.entity';

@Injectable()
@Processor(QUEUE_NAMES.EMAIL)
export class EmailQueueProcessor implements OnModuleInit {
  private readonly logger = new Logger(EmailQueueProcessor.name);

  constructor(
    private readonly emailService: EmailService,
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    @InjectQueue(QUEUE_NAMES.EMAIL) private readonly emailQueue: Queue,
  ) {}

  async onModuleInit() {
    this.logger.log('EmailQueueProcessor initialized');
    console.log('🚀 EMAIL PROCESSOR: EmailQueueProcessor initialized');

    try {
      // Check queue status
      const waiting = await this.emailQueue.getWaiting();
      const active = await this.emailQueue.getActive();
      const completed = await this.emailQueue.getCompleted();
      const failed = await this.emailQueue.getFailed();

      console.log('🚀 EMAIL PROCESSOR: Queue stats:', {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length
      });

      // Check if queue is paused
      const isPaused = await this.emailQueue.isPaused();
      console.log('🚀 EMAIL PROCESSOR: Queue is paused:', isPaused);

      // Resume queue if paused
      if (isPaused) {
        console.log('� EMAIL PROCESSOR: Resuming paused queue...');
        await this.emailQueue.resume();
      }

      // Check Redis connection
      const redisClient = (this.emailQueue as any).client;
      if (redisClient) {
        console.log('� EMAIL PROCESSOR: Redis client status:', redisClient.status);
      }

      // Clean any stalled jobs
      try {
        await this.emailQueue.clean(0, 'failed');
        await this.emailQueue.clean(0, 'completed');
        console.log('� EMAIL PROCESSOR: Cleaned old failed and completed jobs');
      } catch (cleanError) {
        console.log('🚀 EMAIL PROCESSOR: Error cleaning jobs:', cleanError.message);
      }

      console.log('� EMAIL PROCESSOR: Initialization completed successfully');

    } catch (error) {
      console.error('� EMAIL PROCESSOR: Error during initialization:', error);
      this.logger.error('Error during initialization:', error);
    }
  }

  @Process('send-email')
  async handleSendEmail(job: Job<QueueJobData>) {
    const { candidateSequenceStatusId, candidateId, stepId, recipientEmail } = job.data;

    this.logger.log(`Processing email job ${job.id} for candidate ${candidateId}, step ${stepId}`);
    console.log(`🔥 EMAIL PROCESSOR: Processing job ${job.id} for candidate ${candidateId}, step ${stepId}`);

    // Validate required data
    if (!candidateSequenceStatusId) {
      const error = 'candidateSequenceStatusId is required';
      this.logger.error(error);
      throw new Error(error);
    }

    if (!recipientEmail) {
      const error = 'recipientEmail is required';
      this.logger.error(error);
      throw new Error(error);
    }

    if (!candidateId || !stepId) {
      const error = 'candidateId and stepId are required';
      this.logger.error(error);
      throw new Error(error);
    }

    try {
      // Verify that the candidate sequence status exists before processing
      console.log(`🔥 EMAIL PROCESSOR: Verifying candidate sequence status ${candidateSequenceStatusId} exists...`);

      try {
        // Update status to QUEUED
        await this.candidateSequenceStatusService.updateStatus(
          candidateSequenceStatusId,
          SequenceStepStatus.QUEUED,
        );
        console.log(`🔥 EMAIL PROCESSOR: ✅ Updated status to QUEUED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
      } catch (statusError) {
        // If the candidate sequence status doesn't exist, log detailed error and fail gracefully
        this.logger.error(`Candidate sequence status ${candidateSequenceStatusId} not found: ${statusError.message}`);
        console.error(`🔥 EMAIL PROCESSOR: ❌ Candidate sequence status ${candidateSequenceStatusId} not found: ${statusError.message}`);

        // Try to get more information about what went wrong
        console.log(`🔥 EMAIL PROCESSOR: Job data for debugging:`, {
          candidateSequenceStatusId,
          candidateId,
          stepId,
          recipientEmail,
          jobId: job.id
        });

        throw new Error(`Candidate sequence status ${candidateSequenceStatusId} not found. This may indicate a data consistency issue.`);
      }

      // Send dummy email using AWS SES
      const dummySubject = `Test Email - Sequence Step ${stepId}`;
      const dummyBody = `
        <h2>Test Email from Sequence Automation</h2>
        <p>Hello,</p>
        <p>This is a test email sent from our sequence automation system.</p>
        <p><strong>Details:</strong></p>
        <ul>
          <li>Candidate ID: ${candidateId}</li>
          <li>Step ID: ${stepId}</li>
          <li>Sent to: ${recipientEmail}</li>
          <li>Sent at: ${new Date().toISOString()}</li>
        </ul>
        <p>Best regards,<br>Ultimate Outsourcing Team</p>
      `;

      await this.emailService.sendSimpleEmail(
        recipientEmail,
        dummySubject,
        dummyBody,
      );

      // Update status to SENT
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.SENT,
        {
          sentTo: recipientEmail,
          sentAt: new Date().toISOString(),
        },
      );

      this.logger.log(`✅ Email sent successfully for candidate ${candidateId}, step ${stepId}`);
      console.log(`✅ EMAIL PROCESSOR: Email sent successfully for candidate ${candidateId}, step ${stepId}`);

      // Simulate delivery confirmation (emails are typically delivered immediately)
      setTimeout(async () => {
        try {
          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.DELIVERED,
          );
          this.logger.log(`Email delivery confirmed for candidate ${candidateId}, step ${stepId}`);
        } catch (error) {
          this.logger.error(`Failed to update delivery status: ${error.message}`);
        }
      }, 1000);

      return { success: true, candidateId, stepId };

    } catch (error) {
      // Enhanced error logging with categorization
      const errorContext = {
        jobId: job.id,
        candidateId,
        stepId,
        candidateSequenceStatusId,
        recipientEmail,
        errorType: error.constructor.name,
        errorMessage: error.message,
        errorStack: error.stack,
        timestamp: new Date().toISOString(),
      };

      this.logger.error(`❌ Failed to send email for candidate ${candidateId}:`, errorContext);
      console.log(`❌ EMAIL PROCESSOR: Failed to send email for candidate ${candidateId}:`, {
        error: error.message,
        type: error.constructor.name,
        jobId: job.id,
      });

      // Categorize error types for better handling
      let errorCategory = 'UNKNOWN';
      if (error.message.includes('Candidate sequence status') && error.message.includes('not found')) {
        errorCategory = 'DATA_INTEGRITY';
      } else if (error.message.includes('Email') || error.message.includes('SMTP')) {
        errorCategory = 'EMAIL_SERVICE';
      } else if (error.message.includes('required')) {
        errorCategory = 'VALIDATION';
      }

      // Only update status if candidateSequenceStatusId exists
      if (candidateSequenceStatusId) {
        try {
          // Update attempt count and status
          await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);

          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.FAILED,
            {
              error: error.message,
              errorCategory,
              errorType: error.constructor.name,
              failedAt: new Date().toISOString(),
              jobId: job.id,
            },
          );

          this.logger.log(`Updated status to FAILED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
        } catch (statusError) {
          this.logger.error(`Critical: Failed to update status for candidateSequenceStatusId ${candidateSequenceStatusId}:`, {
            statusError: statusError.message,
            originalError: error.message,
            candidateId,
            stepId,
          });
        }
      } else {
        this.logger.warn(`Cannot update status - candidateSequenceStatusId is missing for candidate ${candidateId}, step ${stepId}`);
      }

      // Re-throw to trigger Bull's retry mechanism
      throw error;
    }
  }


}
