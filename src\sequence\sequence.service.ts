import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleSequence } from './sequence.entity';
import { Repository } from 'typeorm';
import { RoleSequenceDto, UpdateRoleSequenceDto } from './dto/sequence..dto';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { QueueService, QueueJobData } from 'src/queue/queue.service';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { SequenceStepStatus, StepType } from 'src/candidate-sequence-status/candidate-sequence-status.entity';

@Injectable()
export class SequenceService {
  private readonly logger = new Logger(SequenceService.name);

  constructor(
    @InjectRepository(RoleSequence)
    private readonly roleSequenceRepository: Repository<RoleSequence>,
    @InjectRepository(SequenceSteps)
    private readonly stepRepository: Repository<SequenceSteps>,
    @InjectRepository(RoleCandidate)
    private readonly candidateRepository: Repository<RoleCandidate>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(PersonPhone)
    private readonly personPhoneRepository: Repository<PersonPhone>,
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    private readonly queueService: QueueService,
  ) {}

  async createRoleSequence(
    roleSequence: RoleSequenceDto,
  ): Promise<RoleSequence> {
    try {
      const newRoleSequence = this.roleSequenceRepository.create(roleSequence);
      const savedSequence = await this.roleSequenceRepository.save(newRoleSequence);

      // Auto-start sequence with test candidates if it's ACTIVE
      if (savedSequence.status === 'ACTIVE') {
        this.logger.log(`Auto-starting sequence ${savedSequence.id} with test candidates`);
        await this.autoStartSequenceWithTestCandidates(savedSequence.id);
      }

      return savedSequence;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error creating role sequence',
        error.message,
      );
    }
  }

  async getRoleSequence(): Promise<RoleSequence[]> {
    try {
      return await this.roleSequenceRepository.find();
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role sequences',
        error.message,
      );
    }
  }

  async getRoleSequenceById(id: number): Promise<RoleSequence> {
    try {
      const roleSequence = await this.roleSequenceRepository.findOne({
        where: { id },
      });
      if (!roleSequence) {
        throw new NotFoundException('Role sequence not found');
      }
      return roleSequence;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role sequence',
        error.message,
      );
    }
  }

  async updateRoleSequence(
    id: number,
    updateRoleSequenceDto: UpdateRoleSequenceDto,
  ): Promise<RoleSequence> {
    try {
      const roleSequence = await this.getRoleSequenceById(id);
      if (!roleSequence) {
        throw new NotFoundException('Role sequence not found');
      }
      Object.assign(roleSequence, updateRoleSequenceDto);
      return await this.roleSequenceRepository.save(roleSequence);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error updating role sequence',
        error.message,
      );
    }
  }

  async deleteRoleSequence(id: number): Promise<void> {
    try {
      const roleSequence = await this.getRoleSequenceById(id);
      if (!roleSequence) {
        throw new NotFoundException('Role sequence not found');
      }
      await this.roleSequenceRepository.remove(roleSequence);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error deleting role sequence',
        error.message,
      );
    }
  }

  /**
   * Start sequence with test candidates (public method)
   */
  async startSequenceWithTestCandidates(sequenceId: number): Promise<void> {
    this.logger.log(`Starting sequence ${sequenceId} with test candidates (public method)`);

    // Check if there are any role candidates in the database
    const totalCandidates = await this.candidateRepository.count();
    if (totalCandidates === 0) {
      throw new Error('No role candidates found in the database. Please add some candidates first.');
    }

    await this.autoStartSequenceWithTestCandidates(sequenceId);
  }

  /**
   * Filter out candidates that are already in the sequence
   */
  private async filterNewCandidates(candidateIds: number[], sequenceId: number): Promise<number[]> {
    // Get existing candidate sequence statuses for this sequence
    const existingStatuses = await this.candidateSequenceStatusService.getCandidatesInSequence(sequenceId);
    const existingCandidateIds = new Set(existingStatuses.map((status: any) => status.candidateId));

    // Filter out candidates that are already in the sequence
    const newCandidateIds = candidateIds.filter(candidateId => !existingCandidateIds.has(candidateId));

    this.logger.log(`Filtered candidates: ${candidateIds.length} total, ${existingCandidateIds.size} existing, ${newCandidateIds.length} new`);

    return newCandidateIds;
  }

  /**
   * Start sequence with role candidates for a specific role
   */
  async startSequenceWithRoleCandidates(sequenceId: number, roleId: number, limit: number = 10): Promise<void> {
    try {
      this.logger.log(`Starting sequence ${sequenceId} with role candidates for role ${roleId}`);

      // Get role candidates for the specified role
      const roleCandidates = await this.candidateRepository.find({
        where: {
          roleId: roleId,
          // Remove candidate_status filter for now to get all candidates
        },
        relations: ['candidate', 'candidate.emails', 'candidate.phones', 'role'],
        take: limit,
      });

      if (roleCandidates.length === 0) {
        throw new Error(`No role candidates found for role ${roleId}`);
      }

      const candidateIds = roleCandidates.map(c => c.id);
      this.logger.log(`Starting sequence ${sequenceId} with ${candidateIds.length} role candidates for role ${roleId}`);

      // Start the sequence with role candidates
      await this.startSequence(sequenceId, candidateIds);

      this.logger.log(`Sequence ${sequenceId} started successfully with ${candidateIds.length} role candidates`);
    } catch (error) {
      this.logger.error(`Failed to start sequence ${sequenceId} with role candidates: ${error.message}`);
      throw error;
    }
  }

  /**
   * Auto-start sequence with test candidates
   */
  private async autoStartSequenceWithTestCandidates(sequenceId: number): Promise<void> {
    try {
      this.logger.log(`Auto-starting sequence ${sequenceId} with test candidates`);

      // Get the sequence to find its associated role
      const sequence = await this.roleSequenceRepository.findOne({
        where: { id: sequenceId },
        relations: ['sequenceSteps'],
      });

      if (!sequence) {
        this.logger.error(`Sequence ${sequenceId} not found`);
        return;
      }

      if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
        this.logger.warn(`Sequence ${sequenceId} has no steps, skipping auto-start`);
        return;
      }

      // Get role candidates for this sequence (assuming sequence has roleId or we can derive it)
      // For now, get first 5 role candidates from the database with proper relations
      const testCandidates = await this.candidateRepository.find({
        take: 5,
        relations: ['candidate', 'candidate.emails', 'candidate.phones', 'role'],
      });

      if (testCandidates.length === 0) {
        this.logger.warn(`No role candidates found to auto-start sequence ${sequenceId}. This is normal if no candidates exist yet.`);
        return;
      }

      // Filter out candidates that don't have proper candidate records
      const validCandidates = testCandidates.filter(c => c.candidate && c.candidate.id);

      if (validCandidates.length === 0) {
        this.logger.warn(`No valid role candidates with proper candidate records found for sequence ${sequenceId}`);
        return;
      }

      const candidateIds = validCandidates.map(c => c.id);
      this.logger.log(`Auto-starting sequence ${sequenceId} with ${candidateIds.length} role candidates: ${candidateIds.join(', ')}`);

      // Start the sequence with test candidates
      await this.startSequence(sequenceId, candidateIds);

      this.logger.log(`Sequence ${sequenceId} auto-started successfully with ${candidateIds.length} candidates`);
    } catch (error) {
      this.logger.error(`Failed to auto-start sequence ${sequenceId}: ${error.message}`, error.stack);
      // Don't throw error here to avoid breaking sequence creation
    }
  }

  /**
   * Start a sequence for multiple candidates
   */
  async startSequence(sequenceId: number, candidateIds: number[]): Promise<void> {
    this.logger.log(`Starting sequence ${sequenceId} for ${candidateIds.length} candidates: [${candidateIds.join(', ')}]`);

    try {
      // Validate input parameters
      if (!sequenceId) {
        throw new Error('Sequence ID is required');
      }

      if (!candidateIds || candidateIds.length === 0) {
        throw new Error('At least one candidate ID is required');
      }

      // Get sequence with steps
      const sequence = await this.roleSequenceRepository.findOne({
        where: { id: sequenceId },
        relations: ['sequenceSteps'],
      });

      if (!sequence) {
        throw new NotFoundException(`Sequence with ID ${sequenceId} not found`);
      }

      if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
        throw new Error(`Sequence ${sequenceId} has no steps defined`);
      }

      this.logger.log(`Found sequence "${sequence.name}" with ${sequence.sequenceSteps.length} steps`);

      // Validate that all candidate IDs exist
      for (const candidateId of candidateIds) {
        const candidate = await this.candidateRepository.findOne({
          where: { id: candidateId },
          relations: ['candidate'],
        });
        if (!candidate) {
          throw new Error(`Candidate with ID ${candidateId} not found`);
        }
        this.logger.log(`Validated candidate ${candidateId}: ${candidate.candidate?.first_name} ${candidate.candidate?.last_name}`);
      }

      // Filter out candidates that are already in this sequence
      const newCandidateIds = await this.filterNewCandidates(candidateIds, sequenceId);

      if (newCandidateIds.length === 0) {
        this.logger.warn(`All candidates are already in sequence ${sequenceId}`);
        return;
      }

      this.logger.log(`Adding ${newCandidateIds.length} new candidates to sequence ${sequenceId}: [${newCandidateIds.join(', ')}]`);

      // Initialize candidate sequence status for new candidates only
      await this.candidateSequenceStatusService.initializeCandidateSequence(
        newCandidateIds,
        sequenceId,
      );

      // Start first steps for all candidates (including existing ones)
      await this.startFirstSteps(candidateIds, sequenceId);

      this.logger.log(`Sequence ${sequenceId} started successfully for ${candidateIds.length} candidates`);
    } catch (error) {
      this.logger.error(`Failed to start sequence ${sequenceId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException(
        'Error starting sequence',
        error.message,
      );
    }
  }

  /**
   * Start the first steps of a sequence for candidates
   */
  private async startFirstSteps(candidateIds: number[], sequenceId: number): Promise<void> {
    for (const candidateId of candidateIds) {
      // Get first steps (order = 0 or minimum order)
      const firstSteps = await this.candidateSequenceStatusService.getPendingSteps(
        candidateId,
        sequenceId,
        0,
      );

      if (firstSteps.length === 0) {
        // If no steps with order 0, get the minimum order
        const allSteps = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
          candidateId,
          sequenceId,
        );
        const minOrder = Math.min(...allSteps.map(s => s.stepOrder));
        const minOrderSteps = await this.candidateSequenceStatusService.getPendingSteps(
          candidateId,
          sequenceId,
          minOrder,
        );
        await this.executeSteps(candidateId, minOrderSteps);
      } else {
        await this.executeSteps(candidateId, firstSteps);
      }
    }
  }

  /**
   * Execute steps for a candidate
   */
  private async executeSteps(candidateId: number, steps: any[]): Promise<void> {
    for (const step of steps) {
      await this.executeStep(candidateId, step);
    }
  }

  /**
   * Execute a single step for a candidate
   */
  private async executeStep(candidateId: number, candidateSequenceStatus: any): Promise<void> {
    try {
      console.log(`🚀 SEQUENCE-SERVICE: executeStep called for candidate ${candidateId}, status ID ${candidateSequenceStatus.id}`);
      console.log(`🚀 SEQUENCE-SERVICE: candidateSequenceStatus details:`, {
        id: candidateSequenceStatus.id,
        candidateId: candidateSequenceStatus.candidateId,
        sequenceId: candidateSequenceStatus.sequenceId,
        stepId: candidateSequenceStatus.stepId,
        stepOrder: candidateSequenceStatus.stepOrder,
        status: candidateSequenceStatus.status
      });

      // Get candidate details with contact information
      console.log(`🚀 SEQUENCE-SERVICE: Fetching candidate ${candidateId} with contact information...`);
      const candidate = await this.candidateRepository.findOne({
        where: { id: candidateId },
        relations: ['candidate', 'candidate.emails', 'candidate.phones'],
      });

      if (!candidate) {
        console.error(`🚀 SEQUENCE-SERVICE: ❌ Candidate ${candidateId} not found`);
        throw new Error(`Candidate ${candidateId} not found`);
      }

      console.log(`🚀 SEQUENCE-SERVICE: ✅ Found candidate:`, {
        id: candidate.id,
        candidateId: candidate.candidateId,
        roleId: candidate.roleId,
        candidateName: candidate.candidate ? `${candidate.candidate.first_name} ${candidate.candidate.last_name}` : 'No candidate record',
        emailCount: candidate.candidate?.emails?.length || 0,
        phoneCount: candidate.candidate?.phones?.length || 0
      });

      // Get step details
      console.log(`🚀 SEQUENCE-SERVICE: Fetching step ${candidateSequenceStatus.stepId} details...`);
      const step = await this.stepRepository.findOne({
        where: { id: candidateSequenceStatus.stepId },
        relations: ['emailTemplate'],
      });

      if (!step) {
        console.error(`🚀 SEQUENCE-SERVICE: ❌ Step ${candidateSequenceStatus.stepId} not found`);
        throw new Error(`Step ${candidateSequenceStatus.stepId} not found`);
      }

      console.log(`🚀 SEQUENCE-SERVICE: ✅ Found step:`, {
        id: step.id,
        name: step.name,
        medium: step.medium,
        type: step.type,
        order: step.order,
        templateId: step.templateId,
        hasEmailTemplate: !!step.emailTemplate
      });

      // Verify the candidate sequence status exists before creating job
      console.log(`🚀 SEQUENCE-SERVICE: Verifying candidateSequenceStatus ${candidateSequenceStatus.id} exists...`);
      const verifyStatus = await this.candidateSequenceStatusService.findById(candidateSequenceStatus.id);
      if (!verifyStatus) {
        console.error(`🚀 SEQUENCE-SERVICE: ❌ CandidateSequenceStatus ${candidateSequenceStatus.id} not found in database!`);
        throw new Error(`CandidateSequenceStatus ${candidateSequenceStatus.id} not found in database`);
      }
      console.log(`🚀 SEQUENCE-SERVICE: ✅ CandidateSequenceStatus ${candidateSequenceStatus.id} verified in database`);

      // Prepare job data
      const jobData: QueueJobData = {
        candidateSequenceStatusId: candidateSequenceStatus.id,
        candidateId,
        stepId: step.id,
        sequenceId: candidateSequenceStatus.sequenceId,
        medium: step.medium,
        templateId: step.templateId,
        metadata: {
          stepName: step.name,
          stepType: step.type,
          candidateName: `${candidate.candidate?.first_name} ${candidate.candidate?.last_name}`,
        },
      };

      console.log(`🚀 SEQUENCE-SERVICE: Prepared initial job data:`, jobData);

      // Add contact information based on medium
      console.log(`🚀 SEQUENCE-SERVICE: Adding contact information for medium: ${step.medium}`);
      await this.addContactInformation(jobData, candidate.candidate, step.medium);

      console.log(`🚀 SEQUENCE-SERVICE: Final job data with contact info:`, jobData);

      // Add to appropriate queue
      console.log(`🚀 SEQUENCE-SERVICE: Adding job to ${step.medium} queue...`);
      await this.queueService.addJobToQueue(step.medium, jobData);

      console.log(`🚀 SEQUENCE-SERVICE: ✅ Step ${step.id} (${step.name}) queued for candidate ${candidateId} via ${step.medium}`);
      this.logger.log(`Step ${step.id} queued for candidate ${candidateId} via ${step.medium}`);
    } catch (error) {
      console.error(`🚀 SEQUENCE-SERVICE: ❌ Failed to execute step for candidate ${candidateId}:`, error.message);
      console.error(`🚀 SEQUENCE-SERVICE: ❌ Error stack:`, error.stack);
      this.logger.error(`Failed to execute step for candidate ${candidateId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Add contact information to job data based on medium
   */
  private async addContactInformation(
    jobData: QueueJobData,
    person: People,
    medium: string,
  ): Promise<void> {
    console.log(`🚀 SEQUENCE-SERVICE: addContactInformation called for medium: ${medium}`);
    console.log(`🚀 SEQUENCE-SERVICE: Person data:`, {
      id: person?.id,
      firstName: person?.first_name,
      lastName: person?.last_name,
      emailCount: person?.emails?.length || 0,
      phoneCount: person?.phones?.length || 0,
      profileUrl: person?.profile_url
    });

    if (!person) {
      console.error(`🚀 SEQUENCE-SERVICE: ❌ No person data provided for contact information`);
      return;
    }

    switch (medium.toUpperCase()) {
      case 'EMAIL':
        console.log(`🚀 SEQUENCE-SERVICE: Processing EMAIL medium...`);
        console.log(`🚀 SEQUENCE-SERVICE: Available emails:`, person.emails?.map(e => ({
          id: e.id,
          email: e.email,
          type: e.email_type
        })));

        const primaryEmail = person.emails?.find(e => e.email_type === 'BUSINESS') || person.emails?.[0];
        if (primaryEmail) {
          jobData.recipientEmail = primaryEmail.email;
          console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient email: ${primaryEmail.email} (type: ${primaryEmail.email_type})`);
        } else {
          console.error(`🚀 SEQUENCE-SERVICE: ❌ No email found for person ${person.id}`);
        }
        break;

      case 'SMS':
      case 'WHATSAPP':
      case 'CALL':
        console.log(`🚀 SEQUENCE-SERVICE: Processing ${medium} medium...`);
        console.log(`🚀 SEQUENCE-SERVICE: Available phones:`, person.phones?.map(p => ({
          id: p.id,
          phone: p.phone_number,
          type: p.phone_type
        })));

        const primaryPhone = person.phones?.find(p => p.phone_type === 'BUSINESS') || person.phones?.[0];
        if (primaryPhone) {
          jobData.recipientPhone = primaryPhone.phone_number;
          console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient phone: ${primaryPhone.phone_number} (type: ${primaryPhone.phone_type})`);
        } else {
          console.error(`🚀 SEQUENCE-SERVICE: ❌ No phone found for person ${person.id}`);
        }
        break;

      case 'LINKEDIN':
        console.log(`🚀 SEQUENCE-SERVICE: Processing LINKEDIN medium...`);
        if (person.profile_url) {
          jobData.recipientLinkedIn = person.profile_url;
          console.log(`🚀 SEQUENCE-SERVICE: ✅ Set recipient LinkedIn: ${person.profile_url}`);
        } else {
          console.error(`🚀 SEQUENCE-SERVICE: ❌ No LinkedIn profile URL found for person ${person.id}`);
        }
        break;

      default:
        console.error(`🚀 SEQUENCE-SERVICE: ❌ Unknown medium: ${medium}`);
        break;
    }

    console.log(`🚀 SEQUENCE-SERVICE: Final contact info added to job data:`, {
      recipientEmail: jobData.recipientEmail,
      recipientPhone: jobData.recipientPhone,
      recipientLinkedIn: jobData.recipientLinkedIn
    });
  }

  /**
   * Process next steps after a step is completed
   */
  async processNextSteps(candidateId: number, completedStepId: number): Promise<void> {
    try {
      // Get the completed step status
      const completedStatus = await this.candidateSequenceStatusService
        .getCandidateSequenceStatus(candidateId, 0)
        .then(statuses => statuses.find(s => s.stepId === completedStepId));

      if (!completedStatus) {
        throw new Error('Completed step status not found');
      }

      const sequenceId = completedStatus.sequenceId;
      const currentOrder = completedStatus.stepOrder;

      // Check if all steps at current order are completed
      const currentOrderStatuses = await this.candidateSequenceStatusService
        .getCandidateSequenceStatus(candidateId, sequenceId)
        .then(statuses => statuses.filter(s => s.stepOrder === currentOrder));

      const allCurrentCompleted = currentOrderStatuses.every(
        s => s.status === SequenceStepStatus.COMPLETED || s.status === SequenceStepStatus.FAILED,
      );

      if (allCurrentCompleted) {
        // Get next steps
        const nextSteps = await this.candidateSequenceStatusService.getNextSteps(
          candidateId,
          sequenceId,
          currentOrder,
        );

        if (nextSteps.length > 0) {
          await this.executeSteps(candidateId, nextSteps);
          this.logger.log(`Next steps triggered for candidate ${candidateId} after completing step ${completedStepId}`);
        } else {
          this.logger.log(`Sequence completed for candidate ${candidateId}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to process next steps for candidate ${candidateId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get sequence with steps dynamically from database
   */
  async getSequenceWithSteps(sequenceId: number): Promise<RoleSequence> {
    const sequence = await this.roleSequenceRepository.findOne({
      where: { id: sequenceId },
      relations: ['sequenceSteps', 'sequenceSteps.emailTemplate'],
    });

    if (!sequence) {
      throw new NotFoundException('Sequence not found');
    }

    // Sort steps by order
    if (sequence.sequenceSteps) {
      sequence.sequenceSteps.sort((a, b) => a.order - b.order);
    }

    return sequence;
  }

  /**
   * Get sequence execution statistics
   */
  async getSequenceStats(sequenceId: number): Promise<any> {
    const sequence = await this.getSequenceWithSteps(sequenceId);

    // Get all candidate statuses for this sequence
    const allStatuses = await this.candidateSequenceStatusService
      .getStepsByStatus(SequenceStepStatus.PENDING, 10000)
      .then(statuses => statuses.filter(s => s.sequenceId === sequenceId));

    const stats = {
      sequenceId,
      sequenceName: sequence.name,
      totalSteps: sequence.sequenceSteps.length,
      totalCandidates: new Set(allStatuses.map(s => s.candidateId)).size,
      statusBreakdown: {
        pending: 0,
        queued: 0,
        sent: 0,
        delivered: 0,
        opened: 0,
        clicked: 0,
        replied: 0,
        completed: 0,
        failed: 0,
        skipped: 0,
      },
    };

    // Count statuses
    for (const status of allStatuses) {
      stats.statusBreakdown[status.status.toLowerCase()]++;
    }

    return stats;
  }

  /**
   * Debug method to check sequence and candidate data
   */
  async debugSequenceData(sequenceId: number): Promise<any> {
    try {
      // Get sequence with steps
      const sequence = await this.roleSequenceRepository.findOne({
        where: { id: sequenceId },
        relations: ['sequenceSteps'],
      });

      // Get all role candidates
      const allCandidates = await this.candidateRepository.find({
        take: 10,
        relations: ['candidate', 'role'],
      });

      // Get existing candidate sequence statuses for this sequence
      const existingStatuses = await this.candidateSequenceStatusService.getCandidatesInSequence(sequenceId);

      return {
        sequence: {
          exists: !!sequence,
          id: sequence?.id,
          name: sequence?.name,
          status: sequence?.status,
          stepsCount: sequence?.sequenceSteps?.length || 0,
          steps: sequence?.sequenceSteps?.map(step => ({
            id: step.id,
            name: step.name,
            order: step.order,
            medium: step.medium,
            type: step.type,
            templateId: step.templateId,
            roleSequenceId: step.roleSequenceId
          })) || []
        },
        candidates: {
          totalCount: allCandidates.length,
          candidates: allCandidates.map(candidate => ({
            id: candidate.id,
            candidateId: candidate.candidateId,
            roleId: candidate.roleId,
            hasCandidate: !!candidate.candidate,
            candidateName: candidate.candidate ? `${candidate.candidate.first_name} ${candidate.candidate.last_name}` : 'No candidate record',
            hasRole: !!candidate.role,
            roleName: candidate.role?.title || 'No role'
          }))
        },
        existingStatuses: {
          count: existingStatuses.length,
          statuses: existingStatuses.map(status => ({
            candidateId: status.candidateId,
            stepId: status.stepId,
            status: status.status,
            stepOrder: status.stepOrder
          }))
        },
        validation: {
          sequenceExists: !!sequence,
          sequenceHasSteps: sequence?.sequenceSteps?.length > 0,
          candidatesExist: allCandidates.length > 0,
          candidatesHaveValidRecords: allCandidates.filter(c => c.candidate && c.candidate.id).length
        }
      };
    } catch (error) {
      this.logger.error(`Failed to debug sequence ${sequenceId}: ${error.message}`);
      return {
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * Check database health for sequence execution
   */
  async checkDatabaseHealth(): Promise<any> {
    try {
      // Check sequences
      const sequenceCount = await this.roleSequenceRepository.count();
      const sequences = await this.roleSequenceRepository.find({
        take: 5,
        relations: ['sequenceSteps'],
      });

      // Check sequence steps
      const stepCount = await this.stepRepository.count();
      const steps = await this.stepRepository.find({
        take: 5,
        relations: ['roleSequence'],
      });

      // Check role candidates
      const candidateCount = await this.candidateRepository.count();
      const candidates = await this.candidateRepository.find({
        take: 5,
        relations: ['candidate', 'role'],
      });

      // Check people (candidates)
      const peopleCount = await this.peopleRepository.count();
      const people = await this.peopleRepository.find({
        take: 5,
        relations: ['emails', 'phones'],
      });

      return {
        summary: {
          sequences: sequenceCount,
          steps: stepCount,
          roleCandidates: candidateCount,
          people: peopleCount,
          healthy: sequenceCount > 0 && stepCount > 0 && candidateCount > 0 && peopleCount > 0
        },
        details: {
          sequences: sequences.map(seq => ({
            id: seq.id,
            name: seq.name,
            status: seq.status,
            stepsCount: seq.sequenceSteps?.length || 0
          })),
          steps: steps.map(step => ({
            id: step.id,
            name: step.name,
            medium: step.medium,
            order: step.order,
            sequenceId: step.roleSequenceId,
            hasSequence: !!step.roleSequence
          })),
          roleCandidates: candidates.map(candidate => ({
            id: candidate.id,
            candidateId: candidate.candidateId,
            roleId: candidate.roleId,
            hasCandidate: !!candidate.candidate,
            hasRole: !!candidate.role
          })),
          people: people.map(person => ({
            id: person.id,
            name: `${person.first_name} ${person.last_name}`,
            emailCount: person.emails?.length || 0,
            phoneCount: person.phones?.length || 0
          }))
        },
        recommendations: this.generateHealthRecommendations(sequenceCount, stepCount, candidateCount, peopleCount)
      };
    } catch (error) {
      this.logger.error(`Failed to check database health: ${error.message}`);
      return {
        error: error.message,
        healthy: false
      };
    }
  }

  private generateHealthRecommendations(sequences: number, steps: number, candidates: number, people: number): string[] {
    const recommendations = [];

    if (sequences === 0) {
      recommendations.push('No sequences found. Create a sequence first using the Advanced Sequences component.');
    }

    if (steps === 0) {
      recommendations.push('No sequence steps found. Add steps to your sequences.');
    }

    if (candidates === 0) {
      recommendations.push('No role candidates found. Add candidates to roles first.');
    }

    if (people === 0) {
      recommendations.push('No people records found. Import or create candidate profiles.');
    }

    if (candidates > 0 && people === 0) {
      recommendations.push('Role candidates exist but no people records found. Ensure candidates are linked to people records.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Database appears healthy for sequence execution.');
    }

    return recommendations;
  }
}
