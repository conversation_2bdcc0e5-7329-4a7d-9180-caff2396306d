import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Body,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { JobApplicationsService } from './job_applications.service';
import { JobApplicationDto } from './dto/job_applications.dto';
import { JobApplications } from './job_applications.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';

@ApiTags('Job Applications')
@Controller('job-applications')
export class JobApplicationsController {
  constructor(
    private readonly jobApplicationsService: JobApplicationsService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new job application' })
  @ApiResponse({
    status: 201,
    description: 'The job application has been successfully created.',
    type: JobApplications,
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async createJobApplication(
    @Body() jobApplicationDto: JobApplicationDto,
  ): Promise<JobApplications> {
    return this.jobApplicationsService.createJobApplication(jobApplicationDto);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get job applications by user ID' })
  @ApiParam({ name: 'userId', type: String, description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'List of job applications for the specified user.',
    type: [JobApplications],
  })
  async getJobApplicationsByUserId(
    @Param('userId') userId: string,
  ): Promise<JobApplications[]> {
    return this.jobApplicationsService.getJobApplicationsByUserId(userId);
  }

  @Get('job/:jobId')
  @ApiOperation({ summary: 'Get job applications by job ID' })
  @ApiParam({ name: 'jobId', type: Number, description: 'Job ID' })
  @ApiResponse({
    status: 200,
    description: 'List of job applications for the specified job.',
    type: [JobApplications],
  })
  async getJobApplicationsByJobId(
    @Param('jobId', ParseIntPipe) jobId: number,
  ): Promise<JobApplications[]> {
    return this.jobApplicationsService.getJobApplicationsByJobId(jobId);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'Update the status of a job application' })
  @ApiParam({ name: 'id', type: Number, description: 'Job Application ID' })
  @ApiBody({
    description: 'Status to update',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The job application status has been successfully updated.',
    type: JobApplications,
  })
  async updateJobApplicationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body('status') status: string,
  ): Promise<JobApplications> {
    return this.jobApplicationsService.updateJobApplicationStatus(id, status);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a job application' })
  @ApiParam({ name: 'id', type: Number, description: 'Job Application ID' })
  @ApiResponse({
    status: 200,
    description: 'The job application has been successfully deleted.',
  })
  async deleteJobApplication(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<void> {
    return this.jobApplicationsService.deleteJobApplication(id);
  }

  //   get job applicants
  @Get('job/:jobId/applicants')
  @ApiOperation({ summary: 'Get job applicants by job ID' })
  @ApiParam({ name: 'jobId', type: Number, description: 'Job ID' })
  @ApiResponse({
    status: 200,
    description: 'List of job applicants for the specified job.',
    type: [JobApplications],
  })
  async getJobApplicantsByJobId(
    @Param('jobId', ParseIntPipe) jobId: number,
  ): Promise<JobApplications[]> {
    return this.jobApplicationsService.getJobApplicantsByJobId(jobId);
  }

  @Get('getAllApplicationStatusByUserId/:userId')
  @ApiOperation({ summary: 'Get job application status by user ID' })
  @ApiParam({ name: 'userId', type: Number, description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'List of job application status for the specified user.',
    type: [JobApplications],
  })
  async getAllApplicationStatusByUserId(
    @Param('userId', ParseIntPipe) userId: string,
  ): Promise<JobApplications[]> {
    return this.jobApplicationsService.getAllApplicantJobStatuses(userId);
  }

  @Get('getAllJobApplicants')
  @ApiOperation({ summary: 'Get all job applicants' })
  @ApiResponse({
    status: 200,
    description: 'List of all job applicants.',
    type: [JobApplications],
  })
  async getAllJobApplicants(): Promise<{ job: Jobs; applicants: Users[] }[]> {
    return this.jobApplicationsService.getAllJobApplicants();
  }
}
