import { ApiProperty } from '@nestjs/swagger';
import { ManualInterestType } from '../website_manual_requests.entity';

export class WebsiteManualRequestResponseDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'Unique identifier for the manual request',
  })
  id: string;

  @ApiProperty({
    example: 'John',
    description: 'First name of the person',
  })
  first_name: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name of the person',
  })
  last_name: string;

  @ApiProperty({
    example: 'Software Engineer',
    description: 'Job title of the person',
  })
  job_title: string;

  @ApiProperty({
    example: 'Tech Corp Ltd',
    description: 'Company name',
  })
  company: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the person',
  })
  email: string;

  @ApiProperty({
    example: 'BD Manual',
    description: 'Type of manual interest',
    enum: ManualInterestType,
  })
  manual_interest: ManualInterestType;

  @ApiProperty({
    example: 'https://bucket.s3.amazonaws.com/path/to/file.pdf',
    description: 'S3 bucket URL for the file',
    required: false,
  })
  s3_url?: string;

  @ApiProperty({
    example: 'https://example.com/file.pdf',
    description: 'Direct file URL for download',
    required: false,
  })
  file_url?: string;

  @ApiProperty({
    example: false,
    description: 'Whether the email has been sent',
  })
  email_sent: boolean;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'When the email was sent',
    required: false,
  })
  email_sent_at?: Date;

  @ApiProperty({
    example: 'Email sending failed: Invalid recipient',
    description: 'Error message if email sending failed',
    required: false,
  })
  email_error?: string;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'When the request was created',
  })
  created_at: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00Z',
    description: 'When the request was last updated',
  })
  updated_at: Date;
}
