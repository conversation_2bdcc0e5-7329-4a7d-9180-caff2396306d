import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';

@Injectable()
@Processor(QUEUE_NAMES.CALL)
export class CallQueueProcessor {
  private readonly logger = new Logger(CallQueueProcessor.name);

  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
  ) {}

  @Process('make-call')
  async handleMakeCall(job: Job<QueueJobData>) {
    const { candidateSequenceStatusId, candidateId, stepId, recipientPhone, body } = job.data;
    
    this.logger.log(`Processing call job for candidate ${candidateId}, step ${stepId}`);

    try {
      // Update status to QUEUED
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.QUEUED,
      );

      // Simulate call initiation
      await this.makeCall(recipientPhone, body);

      // Update status to SENT (call initiated)
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.SENT,
        {
          calledNumber: recipientPhone,
          initiatedAt: new Date().toISOString(),
        },
      );

      this.logger.log(`Call initiated successfully for candidate ${candidateId}, step ${stepId}`);

      // Simulate call completion
      setTimeout(async () => {
        try {
          const callOutcome = Math.random() > 0.3 ? 'answered' : 'no_answer';
          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            callOutcome === 'answered' ? SequenceStepStatus.COMPLETED : SequenceStepStatus.DELIVERED,
            {
              callOutcome,
              callDuration: callOutcome === 'answered' ? Math.floor(Math.random() * 300) + 30 : 0,
            },
          );
          this.logger.log(`Call ${callOutcome} for candidate ${candidateId}, step ${stepId}`);
        } catch (error) {
          this.logger.error(`Failed to update call status: ${error.message}`);
        }
      }, 10000); // 10 second delay to simulate call duration

    } catch (error) {
      this.logger.error(`Failed to make call for candidate ${candidateId}: ${error.message}`);
      
      await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
      
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.FAILED,
        {
          error: error.message,
          failedAt: new Date().toISOString(),
        },
      );

      throw error;
    }
  }

  private async makeCall(to: string, script: string): Promise<void> {
    this.logger.log(`Making call to: ${to}`);
    this.logger.log(`Call script: ${script?.substring(0, 100)}...`);

    // Simulate call setup delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate occasional failures
    if (Math.random() < 0.1) { // 10% failure rate
      throw new Error('Simulated call service failure');
    }

    this.logger.log('Call initiated successfully (mock)');
  }
}
