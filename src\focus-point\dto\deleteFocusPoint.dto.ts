import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsUUID } from 'class-validator';

export class DeleteFocusPointDto {
  @ApiProperty({
    description: 'The ID of the focus point to delete',
    required: true,
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'deletion status',
    required: true,
  })
  @IsBoolean()
  is_deleted: boolean;

  @ApiProperty({
    description: 'deletion date',
    required: true,
  })
  @IsDate()
  deleted_at: Date;
}
