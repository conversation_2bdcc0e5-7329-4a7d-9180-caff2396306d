import { Injectable } from '@nestjs/common';
import { Country } from './country.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Sector } from 'src/sector/sector.entity';

@Injectable()
export class CountryService {
  constructor(
    @InjectRepository(Country)
    private countryRepository: Repository<Country>,
    @InjectRepository(Sector)
    private sectorRepository: Repository<Sector>,
  ) {}

  async createCountry(
    name: string,
    code: string,
    timezone: string,
    flag: string,
    region: string,
  ): Promise<void> {
    await this.countryRepository.insert({
      name,
      code,
      timezone,
      flag,
      region,
    });
  }

  async updateCountry(
    id: number,
    name: string,
    code: string,
    timezone: string,
    flag: string,
    region: string,
  ): Promise<Country> {
    const country = await this.countryRepository.findOne({ where: { id } });

    await this.countryRepository.update(
      { id },
      {
        name,
        code,
        timezone,
        flag,
        region,
      },
    );

    return country;
  }

  async deleteCountry(id: number): Promise<void> {
    await this.countryRepository.delete({ id });
  }

  async findAll(): Promise<Country[]> {
    return this.countryRepository.find();
  }

  async findOne(id: number): Promise<Country> {
    return this.countryRepository.findOne({ where: { id } });
  }

  async findByName(name: string): Promise<Country> {
    return this.countryRepository.findOne({ where: { name } });
  }

  async getAllRegions() {
    const regions = await this.countryRepository
      .createQueryBuilder('country')
      .select('country.region', 'region')
      .distinct(true)
      .getRawMany();

    return regions.map((region) => region.region);
  }

  async getCountriesAndSector() {
    const countries = await this.countryRepository.find();
    const sectors = await this.sectorRepository.find();
    return { countries, sectors };
  }

  async getAllCountries(region?: string) {
    if (region) {
      const countries = await this.countryRepository.find({
        where: { region },
      });
      return countries;
    } else {
      const countries = await this.countryRepository.find();
      return countries;
    }
  }
}
