import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional } from 'class-validator';

export class ProjectDto {
  @ApiProperty({
    description: 'The unique identifier of the project',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'The name of the project',
    example: 'E-commerce Website Development',
    required: false
  })
  @IsString()
  @IsOptional()
  project_name: string;

  @ApiProperty({
    description: 'The duration of the project',
    example: '6 months',
    required: false
  })
  @IsString()
  @IsOptional()
  project_duration: string;

  @ApiProperty({
    description: 'The associated entity or organization for the project',
    example: 'ABC Company',
    required: false
  })
  @IsString()
  @IsOptional()
  project_associated: string;

  @ApiProperty({
    description: 'Array of skills required for the project',
    example: ['React', 'Node.js', 'TypeScript'],
    required: false,
    type: [String]
  })
  @IsArray()
  @IsOptional()
  project_skill: string[];

  @ApiProperty({
    description: 'Detailed description of the project',
    example: 'A full-stack e-commerce platform with payment integration and inventory management',
    required: false
  })
  @IsString()
  @IsOptional()
  project_description: string;

  
}
