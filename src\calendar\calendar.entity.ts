import { People } from 'src/people/people.entity';
import { Roles } from 'src/roles/roles.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  Timestamp,
} from 'typeorm';

@Entity('calendar')
export class Calendar {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ type: 'enum', enum: ['PERSONAL', 'WORK', 'OTHER'], nullable: true })
  calendar_type: string;

  @Index()
  @Column({
    type: 'enum',
    enum: ['MEETING', 'INTERVIEW', 'OTHER'],
    nullable: true,
  })
  event_type: string;

  @Index()
  @Column({
    type: 'enum',
    enum: ['ONLINE', 'ONSITE', 'PHONE', 'OTHER'],
    nullable: true,
  })
  interview_type: string;

  @Column({ type: 'timestamp', nullable: true })
  start_date: Timestamp;

  @Column({ type: 'varchar', length: 255, nullable: true })
  event_name: string;

  @Index()
  @Column({
    type: 'enum',
    enum: ['CLIENT', 'PROSPECT', 'CANDIDATE', 'OTHER'],
    nullable: true,
  })
  client_type: string;

  @Column({
    type: 'enum',
    enum: ['ZOOM', 'GOOGLE MEETS', 'TEAMS', 'PHYSICAL', 'OTHER'],
    nullable: true,
  })
  event_source: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  event_link: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  event_location: string;

  @Column({ type: 'text', nullable: true })
  event_status: string;

  @Column({ type: 'timestamp', nullable: true })
  end_date: Timestamp;

  @Column({ type: 'timestamp', nullable: true })
  reminder_date: Timestamp;

  @ManyToOne(() => Users, (user) => user.calendar, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  user: Users;

  @Column({ type: 'varchar', nullable: true })
  userId: string;

  @ManyToOne(() => People, (person) => person.calendars, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  person: People;

  @Column({ type: 'varchar', nullable: true })
  personId: string;

  @ManyToOne(() => People, (candidate) => candidate.calendars, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  candidate: People;

  @Column({ type: 'varchar', nullable: true })
  candidateId: string;

  @ManyToOne(() => Roles, (role) => role.calendars, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  role: Roles;

  @Column({ type: 'varchar', nullable: true })
  roleId: string;
}
