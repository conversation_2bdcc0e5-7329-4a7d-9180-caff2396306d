import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ResumeTemplate } from './resume-template.entity';
import { ResumeTemplateService } from './resume-templates.service';
import { ResumeTemplateDto } from './dto/resumeTemplates.dto';

@ApiTags('Resume Templates')
@Controller('resume-templates')
export class ResumeTemplateController {
  constructor(private readonly resumeTemplateService: ResumeTemplateService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new resume template' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Resume template created successfully',
    type: ResumeTemplate,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async create(@Body() dto: ResumeTemplateDto): Promise<ResumeTemplate> {
    return await this.resumeTemplateService.create(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all resume templates' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all resume templates',
    type: [ResumeTemplate],
  })
  async findAll(): Promise<ResumeTemplate[]> {
    return await this.resumeTemplateService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific resume template by ID' })
  @ApiParam({ name: 'id', type: Number, description: 'Resume template ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Resume template found',
    type: ResumeTemplate,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Resume template not found',
  })
  async findOne(@Param('id') id: number): Promise<ResumeTemplate> {
    return await this.resumeTemplateService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a resume template' })
  @ApiParam({ name: 'id', type: Number, description: 'Resume template ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Resume template updated successfully',
    type: ResumeTemplate,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Resume template not found',
  })
  async update(
    @Param('id') id: number,
    @Body() dto: ResumeTemplateDto,
  ): Promise<ResumeTemplate> {
    return await this.resumeTemplateService.update(id, dto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a resume template' })
  @ApiParam({ name: 'id', type: Number, description: 'Resume template ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Resume template deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Resume template not found',
  })
  async delete(@Param('id') id: number): Promise<void> {
    return await this.resumeTemplateService.delete(id);
  }
}
