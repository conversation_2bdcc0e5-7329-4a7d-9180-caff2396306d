import { Modu<PERSON> } from '@nestjs/common';
import { PersonPhoneService } from './phone.service';
import { PersonPhoneController } from './phone.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PersonPhone } from './phone.entity';
import { People } from 'src/people/people.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PersonPhone, People])],
  providers: [PersonPhoneService],
  controllers: [PersonPhoneController],
})
export class PhoneModule {}
