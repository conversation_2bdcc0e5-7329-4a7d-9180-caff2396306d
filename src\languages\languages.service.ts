import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Languages } from './langauges.entity';
import { Repository } from 'typeorm';
import { LanguageDto, UpdateLanguagesDto } from './dto/languages.dto';

@Injectable()
export class LanguagesService {
  constructor(
    @InjectRepository(Languages)
    private readonly languagesRepository: Repository<Languages>,
  ) {}

  async createLanguage(languageData: LanguageDto): Promise<Languages> {
    try {
      const newLanguage = this.languagesRepository.create(languageData);
      return await this.languagesRepository.save(newLanguage);
    } catch (error) {
      throw new Error(`Failed to create language: ${error.message}`);
    }
  }

  async findAllLanguages(): Promise<Languages[]> {
    try {
      return await this.languagesRepository.find();
    } catch (error) {
      throw new Error(`Failed to retrieve languages: ${error.message}`);
    }
  }

  async findLanguageById(id: number): Promise<Languages> {
    try {
      const language = await this.languagesRepository.findOne({
        where: { id },
      });
      if (!language) {
        throw new Error(`Language with ID ${id} not found`);
      }
      return language;
    } catch (error) {
      throw new Error(`Failed to retrieve language: ${error.message}`);
    }
  }

  async updateLanguage(updateData: UpdateLanguagesDto): Promise<Languages> {
    try {
      const language = await this.findLanguageById(updateData.id);
      Object.assign(language, updateData);
      return await this.languagesRepository.save(language);
    } catch (error) {
      throw new Error(`Failed to update language: ${error.message}`);
    }
  }

  async deleteLanguage(id: number): Promise<void> {
    try {
      const language = await this.findLanguageById(id);
      await this.languagesRepository.remove(language);
    } catch (error) {
      throw new Error(`Failed to delete language: ${error.message}`);
    }
  }
}
