import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { RoleCandidate } from './role_candidates.entity';

export enum CommunicationType {
  EMAIL = 'EMAIL',
  WHATSAPP = 'WHATSAPP',
  SMS = 'SMS',
  CALL = 'CALL',
  LINKEDIN = 'LINKEDIN',
}

@Entity('role_candidate_logs')
export class RoleCandidateLog {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => RoleCandidate, { nullable: false })
  roleCandidate: RoleCandidate;

  @Index()
  @ApiProperty()
  @Column()
  roleCandidateId: number;

  @ApiProperty({ enum: CommunicationType })
  @Column({ type: 'enum', enum: CommunicationType })
  type: CommunicationType;

  @ApiProperty({ required: false })
  @Column({ type: 'varchar', nullable: true })
  status: string; // e.g. SENT, DELIVERED, FAILED, REPLIED

  @ApiProperty({ required: false })
  @Column({ type: 'varchar', nullable: true })
  messageId: string; // e.g. emailId, whatsappSid, smsSid, etc.

  @ApiProperty({ required: false })
  @Column({ type: 'text', nullable: true })
  details: string; // optional extra info

  @ApiProperty()
  @CreateDateColumn()
  createdAt: Date;
}
