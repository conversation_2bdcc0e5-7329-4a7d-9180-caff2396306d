import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import { TwillioService } from '../../twillio/twillio.service';

@Injectable()
@Processor(QUEUE_NAMES.SMS)
export class SmsQueueProcessor {
  private readonly logger = new Logger(SmsQueueProcessor.name);

  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    private readonly twillioService: TwillioService,
  ) {}

  @Process('send-sms')
  async handleSendSms(job: Job<QueueJobData>) {
    const { candidateSequenceStatusId, candidateId, stepId, recipientPhone } = job.data;

    this.logger.log(`Processing SMS job for candidate ${candidateId}, step ${stepId}`);

    try {
      // Update status to QUEUED
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.QUEUED,
      );

      // Send dummy SMS using Twilio
      const dummyMessage = `Test SMS: Sequence automation for candidate ${candidateId}, step ${stepId}. Sent at ${new Date().toISOString()}`;
      const result = await this.twillioService.sendSMSMessage(recipientPhone, dummyMessage);

      // Update status to SENT
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.SENT,
        {
          sentTo: recipientPhone,
          sentAt: new Date().toISOString(),
          messageSid: result.sid,
        },
      );

      this.logger.log(`SMS sent successfully for candidate ${candidateId}, step ${stepId}`);

      // Simulate delivery confirmation
      setTimeout(async () => {
        try {
          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.DELIVERED,
          );
          this.logger.log(`SMS delivery confirmed for candidate ${candidateId}, step ${stepId}`);
        } catch (error) {
          this.logger.error(`Failed to update delivery status: ${error.message}`);
        }
      }, 2000);

    } catch (error) {
      this.logger.error(`Failed to send SMS for candidate ${candidateId}: ${error.message}`);
      
      await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
      
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.FAILED,
        {
          error: error.message,
          failedAt: new Date().toISOString(),
        },
      );

      throw error;
    }
  }


}
