version: '3.9'
services:
  nestapp:
    container_name: nestapp
    image: francescoxx/nestapp:1.0.0
    build: .
    ports:
      - '5000:5000'
    environment:
      - DB_TYPE=postgres
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=postgres
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - PORT=5000
    depends_on:
      - db
      - redis

  db:
    container_name: db
    image: postgres:14
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - '5432:5432'
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    container_name: redis
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redisdata:/data
    command: redis-server --appendonly yes

  redis-commander:
    container_name: redis-commander
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - '8081:8081'
    depends_on:
      - redis

volumes:
  pgdata: {}
  redisdata: {}