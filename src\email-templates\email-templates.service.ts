import {
  BadGatewayException,
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { EmailTemplates } from './emailTemplates.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailTemplatesDto } from './dto/emailTemplates.dto';
import { UpdateEmailTemplateDto } from './dto/updateEmailTemplate.dto';

@Injectable()
export class EmailTemplatesService {
  constructor(
    @InjectRepository(EmailTemplates)
    private readonly emailTemplatesRepository: Repository<EmailTemplates>,
  ) {}

  async createEmailTemplate(
    emailTemplate: Partial<EmailTemplatesDto>,
  ): Promise<EmailTemplates> {
    try {
      const newEmailTemplate =
        this.emailTemplatesRepository.create(emailTemplate);
      return await this.emailTemplatesRepository.save(newEmailTemplate);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error creating email template',
        error.message,
      );
    }
  }

  async getEmailTemplateByType(
    type: string,
  ): Promise<EmailTemplates[]> {
    try {
      const emailTemplates = await this.emailTemplatesRepository.find({
        where: { type },
      });
      if (!emailTemplates || emailTemplates.length === 0) {
        throw new NotFoundException('Email template not found');
      };
      return emailTemplates;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving email template',
        error.message,
      );
    }
  }

  async getEmailTemplateById(id: string): Promise<EmailTemplates> {
    try {
      const emailTemplate = await this.emailTemplatesRepository.findOne({
        where: { id },
      });
      if (!emailTemplate) {
        throw new NotFoundException('Email template not found');
      }
      return emailTemplate;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving email template',
        error.message,
      );
    }
  }

  async updateEmailTemplate(
    id: string,
    emailTemplate: Partial<EmailTemplatesDto>,
  ): Promise<UpdateEmailTemplateDto> {
    try {
      const existingTemplate = await this.emailTemplatesRepository.findOne({
        where: { id },
      });
      if (!existingTemplate) {
        throw new NotFoundException('Email template not found');
      }
      const updatedTemplate = Object.assign(existingTemplate, emailTemplate);
      return await this.emailTemplatesRepository.save(updatedTemplate);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error updating email template',
        error.message,
      );
    }
  }

  async deleteEmailTemplate(id: string): Promise<void> {
    try {
      const result = await this.emailTemplatesRepository.delete({ id });
      if (result.affected === 0) {
        throw new NotFoundException('Email template not found');
      }
    } catch (error) {
      throw new InternalServerErrorException(
        'Error deleting email template',
        error.message,
      );
    }
  }

  async getAllEmailTemplates(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = null,
  ): Promise<EmailTemplates[]> {
    try {
      const query = this.emailTemplatesRepository
        .createQueryBuilder('email_template')
        .skip(page * pageSize)
        .take(pageSize);

      if (searchString) {
        query.where('email_template.name LIKE :searchString', {
          searchString: `%${searchString}%`,
        });
      }

      const emailTemplates = await query.getMany();
      return emailTemplates;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving email templates',
        error.message,
      );
    }
  }


}
