import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export enum SkillProficiencyLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
}

export class PersonSkillDto {
  @ApiProperty({
    example: 'JavaScript',
    description: 'The name of the skill',
  })
  @IsString()
  @IsNotEmpty()
  skill_name: string;

  @ApiProperty({
    example: 'ADVANCED',
    description: 'Proficiency level of the skill',
    enum: SkillProficiencyLevel,
    required: false,
  })
  @IsEnum(SkillProficiencyLevel)
  @IsOptional()
  proficiency_level?: SkillProficiencyLevel;

  @ApiProperty({
    example: 'personId',
    description: 'Proficiency level of the skill',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  personId?: number;
}
