import {
  Column,
  CreateDateC<PERSON>umn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { People } from 'src/people/people.entity';

@Entity()
export class PersonSkill {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  skill_name: string;

  @Column({
    type: 'enum',
    enum: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],
    nullable: true,
  })
  proficiency_level?: string;

  @ManyToOne(() => People, (person) => person.skills, { onDelete: 'CASCADE' })
  person: People;

  @Column({ nullable: true })
  personId: number;
  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
