import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PersonEmailDto } from './dto/person-email.dto';
import { People } from 'src/people/people.entity';
import { PersonEmail } from './emails.entity';

@Injectable()
export class PersonEmailService {
  constructor(
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
  ) {}

  async create(personId: number, emailDto: PersonEmailDto): Promise<PersonEmail> {
    const person = await this.peopleRepository.findOne({ where: { id: personId } });
    if (!person) {
      throw new NotFoundException(`Person with ID ${personId} not found`);
    }
    const email = this.personEmailRepository.create({ ...emailDto, person });
    return this.personEmailRepository.save(email);
  }

  async createWithoutPerson(emailDto: PersonEmailDto): Promise<PersonEmail> {
    const email = this.personEmailRepository.create(emailDto);
    return this.personEmailRepository.save(email);
  }

  async findAllByPerson(personId: number): Promise<PersonEmail[]> {
    return this.personEmailRepository.find({ where: { person: { id: personId } } });
  }

  async update(emailId: number, emailDto: PersonEmailDto): Promise<PersonEmail> {
    const email = await this.personEmailRepository.findOne({ where: { id: emailId } });
    if (!email) {
      throw new NotFoundException(`Email with ID ${emailId} not found`);
    }
    Object.assign(email, emailDto);
    return this.personEmailRepository.save(email);
  }

  async delete(emailId: number): Promise<void> {
    const email = await this.personEmailRepository.findOne({ where: { id: emailId } });
    if (!email) {
      throw new NotFoundException(`Email with ID ${emailId} not found`);
    }
    await this.personEmailRepository.remove(email);
  }
}
