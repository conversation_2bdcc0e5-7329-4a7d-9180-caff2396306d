import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNotEmpty, IsString } from 'class-validator';

export class JobApplicationDto {
  @ApiProperty({
    description: 'Status of the job application',
    enum: ['PENDING', 'ACCEPTED', 'REJECTED'],
    default: 'PENDING',
  })
  @IsEnum(['PENDING', 'ACCEPTED', 'REJECTED'])
  @IsNotEmpty()
  status: string;

  @ApiProperty({
    description: 'Action type of the job application',
    enum: ['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'],
    default: 'APPLIED',
  })
  @IsEnum(['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'])
  @IsNotEmpty()
  action_type: string;

  @ApiProperty({
    description: 'ID of the job associated with the application',
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  jobId: number;

  @ApiProperty({
    description: 'ID of the user who applied for the job',
    example: 1,
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class UpdateJobApplicationDto {
  @ApiProperty({
    description: 'Status of the job application',
    enum: ['PENDING', 'ACCEPTED', 'REJECTED'],
    required: false,
  })
  @IsEnum(['PENDING', 'ACCEPTED', 'REJECTED'])
  status?: string;

  @ApiProperty({
    description: 'Action type of the job application',
    enum: ['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'],
    required: false,
  })
  @IsEnum(['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'])
  action_type?: string;
}
