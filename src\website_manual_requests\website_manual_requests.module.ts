import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebsiteManualRequestsService } from './website_manual_requests.service';
import { WebsiteManualRequestsController } from './website_manual_requests.controller';
import { WebsiteManualRequest } from './website_manual_requests.entity';
import { EmailModule } from '../email/email.module';
import { S3bucketModule } from '../s3bucket/s3bucket.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([WebsiteManualRequest]),
    EmailModule,
    S3bucketModule,
  ],
  providers: [WebsiteManualRequestsService],
  controllers: [WebsiteManualRequestsController],
  exports: [WebsiteManualRequestsService],
})
export class WebsiteManualRequestsModule {}
