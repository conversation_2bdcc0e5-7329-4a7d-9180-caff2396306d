import { ApiProperty, PartialType } from '@nestjs/swagger';
import { ExperienceDto } from './experience.dto';
import { IsNumber } from 'class-validator';

export class UpdateExperienceDto extends PartialType(ExperienceDto) {
  @ApiProperty({
    example: 'ID of the experience to update',
    description: 'ID of the experience to update',
    required: true,
    type: Number,
  })
  @IsNumber()
  id: number;
}
