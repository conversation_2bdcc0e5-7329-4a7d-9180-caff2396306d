import { People } from 'src/people/people.entity';
import { Entity, Column, PrimaryGeneratedColumn, ManyToOne } from 'typeorm';

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  project_name: string;

   @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  project_duration: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,

  })
  project_associated_with: string;

  @Column({
    type: 'simple-array',
    nullable: true,
  })
  project_skill: string[];

  @Column({
    type: 'text',
    nullable: true,
  })
  project_description: string;

  // personId with relation to people table
  @ManyToOne(() => People, (people) => people.projects)
  person: People;

  @Column({
    type: 'int',
    nullable: true,
  })
  personId: number;
  
}