import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON>Optional, IsString, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EmailTemplatesDto {
  @ApiPropertyOptional({
    description: 'Unique identifier for the email template',
    example: 'd290f1ee-6c54-4b01-90e6-d701748f0851',
  })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiPropertyOptional({
    description: 'Name of the email template',
    example: 'Welcome Email',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Subject of the email template',
    example: 'Welcome to our platform!',
  })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiPropertyOptional({
    description: 'Body content of the email template',
    example: '<p>Thank you for joining us!</p>',
  })
  @IsString()
  @IsOptional()
  body?: string;

  @ApiPropertyOptional({
    description: 'Type of the email template',
    example: 'WELCOME',
    enum: [
      'CLIENT_WELCOME',
      'JOB_ALERTS',
      'RESUME_TEMPLATES',
      'SKILLS',
      'EXPERIENCE',
      'QUALIFICATIONS',
      'CLIENT_INTRODUCTION_TO_ACM',
      'SEND_CANDIDATE_TO_CLIENT',
      'MARKETING_EMAIL',
      'INVOICE',
      'OTHER',
    ],
  })
  @IsEnum([
    'WELCOME',
    'JOB_ALERTS',
    'RESUME_TEMPLATES',
    'SKILLS',
    'EXPERIENCE',
    'QUALIFICATIONS',
    'MAIL_BOX',
    'ROLES',
    'INVOICES',
    'CALENDAR',
    'EMAIL_TEMPLATES',
    'OTHER',
  ])
  @IsNotEmpty()
  type?: string;

  @ApiPropertyOptional({
    description: 'Sub type of the email template',
    example: 'CLIENT',
    enum: ['CLIENT', 'CANDIDATE'],
  })
  @IsEnum(['CLIENT', 'CANDIDATE'])
  @IsNotEmpty()
  sub_type?: string;

  @ApiPropertyOptional({
    description:
      'Unique identifier of the user associated with the email template',
    example: 'a123f1ee-6c54-4b01-90e6-d701748f0851',
  })
  @IsUUID()
  @IsOptional()
  userId?: string;
}
