import { Company } from 'src/company/company.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { People } from 'src/people/people.entity';
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';

@Entity()
export class Sector {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @OneToMany(() => Company, (company) => company.sector)
  companies: Company[];

  @OneToMany(() => People, (people) => people.sector)
  people: People[];

  @OneToMany(() => Jobs, (jobs) => jobs.sector)
  jobs: Jobs[];

}
