import { Module } from '@nestjs/common';
import { LanguagesService } from './languages.service';
import { LanguagesController } from './languages.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Languages } from './langauges.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Languages])],
  providers: [LanguagesService],
  controllers: [LanguagesController],
})
export class LanguagesModule {}
