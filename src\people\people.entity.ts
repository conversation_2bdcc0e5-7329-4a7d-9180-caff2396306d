import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { Company } from 'src/company/company.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Country } from 'src/country/country.entity';
import { Sector } from 'src/sector/sector.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import { Experience } from 'src/experience/experience.entity';
import { Roles } from 'src/roles/roles.entity';
import {
  PersonSource,
  PersonStatusType,
  PersonType,
  ProspectStatus,
  SubscriptionType,
} from './dto/people.enums';
import { Invoices } from 'src/invoices/invoices.entity';
import { Languages } from 'src/languages/langauges.entity';
import { Calendar } from 'src/calendar/calendar.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { JobPreferences } from 'src/preferences/prefrences.entity';

import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { Project } from 'src/projects/projects.entity';

@Entity()
export class People {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({
    nullable: true,
  })
  first_name: string;

  @Index()
  @Column({
    nullable: true,
  })
  last_name: string;

  @Index()
  @Column({
    nullable: true,
  })
  full_name: string;

  @Index()
  @Column({
    nullable: true,
  })
  current_title: string;

  @Column({ nullable: true })
  profile_img: string;

  @Column({ nullable: true })
  headline: string;

  @Column({ nullable: true })
  profile_url: string;

  @Column({ nullable: true })
  location: string;

  @Column({ type: 'text', nullable: true, default: null })
  summary: string;

  @Index()
  @Column({
    type: 'enum',
    enum: PersonType,
    default: 'OTHER',
  })
  person_type: string;

  @Index()
  @Column({
    type: 'varchar',
    nullable: true,
  })
  industry: string;

  @Column({ nullable: true })
  SR_specied_industry: string;

  @Index()
  @Column({
    type: 'varchar',
    nullable: true,
  })
  domain: string;

  @Column({ default: false })
  is_hiring: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    default: null
  })
  currently_employed: boolean;

  @Column({
    type: 'text',
    nullable: true,
    default: null
  })
  additional_information: string;

  @Column({
    type: 'enum',
    enum: PersonSource,
    nullable: true,
    default: PersonSource.OTHER,
  })
  profile_source: string;

  @Column({ nullable: true })
  profile_source_link: string;

  @Column('text', { nullable: true })
  cv_text: string;

  @Column('text', { nullable: true })
  cv_path: string;

  // for clients only
  @Column({ nullable: true })
  client_number: string;

  @Column({
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.FREE,
  })
  subscription_type: string;

  @Column({ nullable: true })
  subscription_start_date: Date;

  @Column({ nullable: true })
  subscription_end_date: Date;

  @Column({ nullable: true })
  reminder_date: Date;

  @Column({ nullable: true })
  amount_paid: number;

  @Column({ nullable: true })
  payment_date: Date;

  @Column({ nullable: true })
  credits: number;

  @Column({ nullable: true })
  credits_per_day: number;

  @Column({ nullable: true })
  credits_used: number;

  @Column({
    type: 'enum',
    enum: PersonStatusType,
    default: PersonStatusType.ACTIVE,
  })
  client_status: string;

  @Column({
    nullable: true,
    type: 'enum',
    enum: ProspectStatus,
    default: ProspectStatus.CONTACTED,
  })
  prospect_status: string; // for prospects only

  @Column({ nullable: true })
  prospect_status_date: Date; // for prospects only

  @Column({ nullable: true })
  prospect_status_comment: string; // for prospects only

  @Column({ nullable: true })
  scrapper_profile_name: string;

  @ManyToOne(() => Company, (company) => company.people, { nullable: true })
  company: Company;

  @Column({ nullable: true })
  companyId: number;

  @OneToMany(() => PersonEmail, (email) => email.person, { cascade: true })
  @JoinColumn({ name: 'emailId' })
  emails: PersonEmail[];

  @Column({ nullable: true })
  emailId: number;

  @OneToMany(() => PeopleAssignment, (assignment) => assignment.personId)
  user_assigned: PeopleAssignment[];

  @OneToMany(() => PersonPhone, (phone) => phone.person, { cascade: true })
  phones: PersonPhone[];

  @OneToMany(() => Qualifications, (qualifications) => qualifications.person, {
    cascade: true,
  })
  qualifications: Qualifications[];

  @OneToMany(() => PersonSkill, (skills) => skills.person, { cascade: true })
  skills: PersonSkill[];

  @ManyToOne(() => Country, (country) => country.people, { nullable: true })
  country: Country;

  @Column({
    nullable: true,
    default: null,
  })
  countryId: number;

  @ManyToOne(() => Sector, (sector) => sector.people, { nullable: true })
  sector: Sector;

  @Column({ nullable: true })
  sectorId: number;

  @OneToMany(() => Jobs, (jobs) => jobs.person)
  jobs: Jobs[];

  @ManyToOne(() => Service, (service) => service.people, { nullable: true })
  service: Service;

  @Column({ nullable: true })
  serviceId: number;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Users, (user) => user.people, { nullable: true })
  user: Users;

  @Column({ nullable: true })
  userId: string;

  @ManyToOne(() => Users, (user) => user.acmUser, { nullable: true })
  acmUser: Users; // ACM user who added this person

  @Column({ nullable: true })
  acmUserId: string; // ACM user who added this person

  @ManyToOne(() => Users, (user) => user.bdUser, { nullable: true })
  bdUser: Users; // ACM user who added this person

  @Column({ nullable: true })
  bdUserId: string; // ACM user who added this person

  @OneToMany(() => Experience, (experiences) => experiences.person, {
    cascade: true,
  })
  experiences: Experience[]; // OneToMany relationship with Experience entity

  @OneToMany(() => Roles, (roles) => roles.person, {
    cascade: true,
  })
  roles: People[]; // OneToMany relationship with Experience entity

  @OneToMany(() => Invoices, (invoice) => invoice.person, {
    cascade: true,
  })
  invoices: Invoices[]; // OneToMany relationship with Experience entity

  @OneToMany(() => Languages, (languages) => languages.person, {
    cascade: true,
    nullable: true,
  })
  languages: Invoices[]; // OneToMany relationship with Experience entity


  @OneToMany(() => RoleCandidate, (role_candidates) => role_candidates.client, {
    cascade: true,
  })
  role_candidates: RoleCandidate[]; // OneToMany relationship with RoleCvs entity

  @OneToMany(() => Calendar, (calendars) => calendars.person, {
    cascade: true,
  })
  calendars: Calendar[]; // OneToMany relationship with Experience entity

  @OneToMany(() => Project, (projects) => projects.person, {
    cascade: true,
  })
  projects: Project[]; // OneToMany relationship with Experience entity

  @OneToMany(() => JobPreferences, (jobPreferences) => jobPreferences.person, {
    cascade: true,
  })
  jobPreferences: JobPreferences[]; // OneToMany relationship with JobPreferences entity
}
