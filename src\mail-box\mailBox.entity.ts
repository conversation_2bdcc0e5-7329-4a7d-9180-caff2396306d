import { Entity, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsBoolean, IsEmail, IsString } from 'class-validator';
import { Users } from 'src/users/users.entity';
import { ProspectStatus } from 'src/people/dto/people.enums';

@Entity('mail_box')
export class MailBox {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsString()
  name: string;

  @Column({ type: 'varchar', length: 255, unique: false })
  @IsEmail()
  email: string;

  @Column({
    type: 'enum',
    enum: [
      'INBOX',
      'SENT',
      'DRAFT',
      'TRASH',
      'SPAM',
      'ARCHIVED',
      'IMPORTANT',
      'STARRED',
      'UNREAD',
      'READ',
      'BOUNCE',
      'REPLIED',
      'FORWARDED',
      'DELETED',
      'BLOCKED',
      'UNSUBSCRIBED',
      'SPAM_REPORT',
      'PHISHING_REPORT',
      'SCHEDULED',
    ],
    default: 'INBOX',
  })
  @IsString()
  type:
    | 'INBOX'
    | 'SENT'
    | 'DRAFT'
    | 'TRASH'
    | 'SPAM'
    | 'ARCHIVED'
    | 'IMPORTANT'
    | 'STARRED'
    | 'UNREAD'
    | 'READ'
    | 'BOUNCE'
    | 'REPLIED'
    | 'FORWARDED'
    | 'DELETED'
    | 'BLOCKED'
    | 'UNSUBSCRIBED'
    | 'SPAM_REPORT'
    | 'PHISHING_REPORT'
    | 'SCHEDULED';

  @Column({ type: 'text', nullable: true })
  @IsString()
  subject: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  message: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  attachment: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  cc: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  bcc: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  reply_to: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  sender: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  recipient: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  date: string;

  @Column({ type: 'text', nullable: true })
  @IsString()
  reason: string;

  @Column({
    type: 'enum',
    enum: [
      'Auto-Reply',
      'Potential Reply',
      'Bounce Back',
      'Unsubscribe',
      'DRAFT',
    ],
    default: null,
    nullable: true,
  })
  @IsString()
  category: string;

  @Column({
    type: 'enum',
    enum: ProspectStatus,
    default: ProspectStatus.CONTACTED,
  })
  @IsString()
  prospect_status: string; // for prospects only

  @Column({ type: 'text', nullable: true })
  @IsString()
  replacement_email: string;

  @Column({
    type: 'boolean',
    default: false,
  })
  @IsBoolean()
  read_by_user: boolean;

  @Column({
    type: 'boolean',
    default: false,
  })
  @IsBoolean()
  read_by_system: boolean;

  @Column({ type: 'date', nullable: true })
  @IsString()
  schedule_date: Date;

  @ManyToOne(() => Users, (assignee) => assignee.mailBoxes, {
    onDelete: 'CASCADE',
    nullable: true,
  })
  assignee: Users;

  @Column({ type: 'varchar', nullable: true })
  @IsString()
  assigneeId: string;

  @CreateDateColumn({type:'timestamptz' , default: () => 'CURRENT_TIMESTAMP'})
  created_at: Date;

  @UpdateDateColumn({type:'timestamptz' , default: () => 'CURRENT_TIMESTAMP'})
  updated_at: Date;
}
