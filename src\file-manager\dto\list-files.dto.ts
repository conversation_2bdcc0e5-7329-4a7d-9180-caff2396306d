import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsBoolean, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class ListFilesDto {
  @ApiProperty({
    description: 'Parent folder ID (null for root folder)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : null)
  parentId?: number;

  @ApiProperty({
    description: 'Include files in trash',
    example: false,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeTrash?: boolean = false;

  @ApiProperty({
    description: 'Search query to filter files/folders by name',
    example: 'document',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
