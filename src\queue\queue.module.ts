import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EmailQueueProcessor } from './processors/email-queue.processor';
import { WhatsAppQueueProcessor } from './processors/whatsapp-queue.processor';
import { SmsQueueProcessor } from './processors/sms-queue.processor';
import { CallQueueProcessor } from './processors/call-queue.processor';
import { LinkedInQueueProcessor } from './processors/linkedin-queue.processor';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CandidateSequenceStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import { CandidateSequenceStatusModule } from 'src/candidate-sequence-status/candidate-sequence-status.module';
import { QUEUE_NAMES } from './queue.constants';
import { EmailModule } from '../email/email.module';
import { TwillioModule } from '../twillio/twillio.module';

@Module({
  imports: [
    ConfigModule,
    EmailModule,
    TwillioModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const redisConfig = {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: parseInt(configService.get('REDIS_PORT', '6379')),
          password: configService.get('REDIS_PASSWORD') || undefined,
          db: parseInt(configService.get('REDIS_DB', '0')),
          retryDelayOnFailover: 100,
          enableReadyCheck: false,
          maxRetriesPerRequest: null,
          lazyConnect: true,
          connectTimeout: 10000,
          commandTimeout: 5000,
        };

        console.log('Redis configuration:', {
          host: redisConfig.host,
          port: redisConfig.port,
          db: redisConfig.db,
          hasPassword: !!redisConfig.password,
        });

        return {
          redis: redisConfig,
          defaultJobOptions: {
            removeOnComplete: 10,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: QUEUE_NAMES.EMAIL,
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
        settings: {
          stalledInterval: 30 * 1000,
          maxStalledCount: 1,
        },
      },
      {
        name: QUEUE_NAMES.WHATSAPP,
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
        settings: {
          stalledInterval: 30 * 1000,
          maxStalledCount: 1,
        },
      },
      {
        name: QUEUE_NAMES.SMS,
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
        settings: {
          stalledInterval: 30 * 1000,
          maxStalledCount: 1,
        },
      },
      {
        name: QUEUE_NAMES.CALL,
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
        settings: {
          stalledInterval: 30 * 1000,
          maxStalledCount: 1,
        },
      },
      {
        name: QUEUE_NAMES.LINKEDIN,
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
        settings: {
          stalledInterval: 30 * 1000,
          maxStalledCount: 1,
        },
      },
    ),
    TypeOrmModule.forFeature([CandidateSequenceStatus]),
    CandidateSequenceStatusModule,
  ],
  controllers: [QueueController],
  providers: [
    QueueService,
    EmailQueueProcessor,
    WhatsAppQueueProcessor,
    SmsQueueProcessor,
    CallQueueProcessor,
    LinkedInQueueProcessor,
  ],
  exports: [QueueService, BullModule],
})
export class QueueModule {
  constructor() {
    console.log('🔧 QUEUE MODULE: QueueModule instantiated');
    console.log('🔧 QUEUE MODULE: Processors should be registered:', {
      EmailQueueProcessor: EmailQueueProcessor.name,
      WhatsAppQueueProcessor: WhatsAppQueueProcessor.name,
    });
  }
}
