import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { PreferencesService } from './preferences.service';
import { JobPreferencesDTO } from './dto/createResumePreferences.dto';

@Controller('preferences')
@ApiTags('preferences')
export class PreferencesController {
  constructor(private readonly preferencesService: PreferencesService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new preference' })
  async createPreference(@Body() body: JobPreferencesDTO) {
    return this.preferencesService.createPreferences(body);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a preference' })
  async updatePreference(
    @Param('id') id: number,
    @Body() body: JobPreferencesDTO,
  ) {
    return this.preferencesService.updatePreferences(id, body);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a preference' })
  async deletePreference(@Param('id') id: number) {
    return this.preferencesService.deletePreferences(id);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all preferences' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'searchString',
    required: false,
    type: String,
  })
  async findAll(
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
    @Query('searchString') searchString?: string,
  ) {
    return this.preferencesService.getAllPreferences(
      page,
      pageSize,
      searchString,
    );
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Get a preference by id' })
  async findOne(@Param('id') id: number) {
    return this.preferencesService.getPreferencesById(id);
  }
}
