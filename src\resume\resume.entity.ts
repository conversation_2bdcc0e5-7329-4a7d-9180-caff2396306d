import { ResumeTemplate } from 'src/resume-templates/resume-template.entity';
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';

@Entity()
export class Resume {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string; // Can be a UUID or email

  @ManyToOne(() => ResumeTemplate, { eager: true })
  template: ResumeTemplate;

  @Column({ type: 'jsonb' }) // Stores user input data (name, skills, etc.)
  data: Record<string, any>;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;
}
