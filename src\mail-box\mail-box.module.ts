import { Module } from '@nestjs/common';
import { MailBoxService } from './mail-box.service';
import { MailBoxController } from './mail-box.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailBox } from './mailBox.entity';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MailBox, Users, People, PersonEmail])],
  providers: [MailBoxService],
  controllers: [MailBoxController],
})
export class MailBoxModule {}
