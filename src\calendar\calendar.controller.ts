import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { CalendarService } from './calendar.service';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { CalendarDTO } from './dto/calendar.dto';
import { UpdateCalendarDto } from './dto/updateCalendar.dto';

@ApiTags('Calendar')
@Controller('calendar')
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new calendar' })
  @ApiBody({ type: CalendarDTO })
  async createCalendar(@Body() calendar: CalendarDTO) {
    return this.calendarService.createCalendar(calendar);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update an existing calendar' })
  @ApiBody({ type: UpdateCalendarDto })
  async updateCalendar(
    @Param('id') id: number,
    @Body() calendar: UpdateCalendarDto,
  ) {
    return this.calendarService.updateCalendar(id, calendar);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all calendars' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getAllCalendars(
    @Query('page') page: number = 0,
    @Query('limit') limit: number = 10,
  ) {
    return this.calendarService.getAllCalendars(page, limit);
  }

  @Get('getByEventType/:eventType/:userId')
  @ApiOperation({ summary: 'Get calendars by event type' })
  async getCalendarsByEventType(
    @Param('eventType') eventType: string,
    @Param('userId') userId: string,
    @Query('pageSize') pageSize?: string,
  ) {
    return this.calendarService.getCalendarByEventType(
      eventType,
      userId,
      pageSize,
    );
  }
  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a calendar by ID' })
  async deleteCalendar(@Param('id') id: number) {
    return this.calendarService.deleteCalendar(id);
  }

  @Delete('deleteAll')
  @ApiOperation({ summary: 'Delete all calendars' })
  async deleteAllCalendars() {
    return this.calendarService.deleteAllCalendars();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a calendar by ID' })
  async getCalendarById(@Param('id') id: number) {
    return this.calendarService.getCalendarById(id);
  }
}
