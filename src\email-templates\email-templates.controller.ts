import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { EmailTemplatesService } from './email-templates.service';
import { EmailTemplatesDto } from './dto/emailTemplates.dto';
import { UpdateEmailTemplateDto } from './dto/updateEmailTemplate.dto';

@ApiTags('email-templates')
@Controller('email-templates')
export class EmailTemplatesController {
  constructor(private readonly emailTemplatesService: EmailTemplatesService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new email template' })
  async createEmailTemplate(@Body() emailTemplate: EmailTemplatesDto) {
    return this.emailTemplatesService.createEmailTemplate(emailTemplate);
  }

  @Get(':type')
  @ApiOperation({ summary: 'Get email template by type' })
  @ApiParam({ name: 'type', description: 'Email template type' })
  async getEmailTemplateByType(@Param('type') type: string) {
    return this.emailTemplatesService.getEmailTemplateByType(type);
  }
  
  @Get(':id')
  @ApiOperation({ summary: 'Get email template by ID' })
  @ApiParam({ name: 'id', description: 'Email template ID' })
  async getEmailTemplateById(@Param('id') id: string) {
    return this.emailTemplatesService.getEmailTemplateById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update email template by ID' })
  @ApiParam({ name: 'id', description: 'Email template ID' })
  async updateEmailTemplate(
    @Param('id') id: string,
    @Body() emailTemplate: UpdateEmailTemplateDto,
  ) {
    return this.emailTemplatesService.updateEmailTemplate(id, emailTemplate);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete email template by ID' })
  @ApiParam({ name: 'id', description: 'Email template ID' })
  async deleteEmailTemplate(@Param('id') id: string) {
    return this.emailTemplatesService.deleteEmailTemplate(id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all email templates' })
  async getAllEmailTemplates() {
    return this.emailTemplatesService.getAllEmailTemplates();
  }


}
