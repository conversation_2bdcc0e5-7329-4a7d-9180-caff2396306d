import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class StripeDto {
  @ApiProperty({
    example: 'tok_visa',
    description: 'The token of the card',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    example: 'usd',
    description: 'The currency of the payment',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiProperty({
    example: '1000',
    description: 'The amount of the payment',
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  amount: number;
}

export class CreateCheckoutSessionDto {
  @IsString()
  service: string;

  @IsString()
  prospect: string;

  @IsNumber()
  amount: number;

  @IsString()
  invoiceNumber: string;

  @IsString()
  invoiceDate: string;

  @IsString()
  dueDate: string;

  @IsString()
  successUrl: string;

  @IsString()
  cancelUrl: string;

  @IsString()
  currency: string;
}
