import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class ServiceDto {

  @ApiProperty({
    example: 'Service Name',
    description: 'The name of the service',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'Service Description',
    description: 'The description of the service',
    required: false,
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    example: 100,
    description: 'The price of the service',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  identity_number_start: number;

  @ApiProperty({
    example: 200,
    description: 'The price of the service',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  identity_number_end: number;

  @ApiProperty({
    example: 100,
    description: 'The price of the service',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  trial_number_fixed: number;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'The status of the service',
    required: false,
  })
  @IsString()
  @IsOptional()
  status: string;

  @ApiProperty({
    example: '1000',
    description: 'Service Tags for the service are 1000, 2000, 3000, 4000, 5000 for Cv Sourcing, Pre-Qualification, 360/Direct, leads, VA, web etc',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  service_tag: string;
}
