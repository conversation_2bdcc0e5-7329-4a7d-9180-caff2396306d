import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Service } from './service.entity';
import { Between, LessThan, MoreThan, Repository } from 'typeorm';
import { ServiceDto } from './dto/service.dto';

@Injectable()
export class ServiceService {
  constructor(
    @InjectRepository(Service)
    private serviceRepository: Repository<Service>,
  ) {}

  async createService(service: ServiceDto): Promise<Service> {
    try {
      // Ensure no overlapping identity number ranges
      const overlappingService = await this.serviceRepository.findOne({
        where: [
          {
            identity_number_start: Between(
              service.identity_number_start,
              service.identity_number_end,
            ),
          },
          {
            identity_number_end: Between(
              service.identity_number_start,
              service.identity_number_end,
            ),
          },
          {
            identity_number_start: LessThan(service.identity_number_start),
            identity_number_end: More<PERSON>han(service.identity_number_end),
          },
        ],
      });

      if (overlappingService) {
        throw new BadRequestException(
          'Service range overlaps with an existing service',
        );
      }

      // Check if service tag already exists
      const existingServiceTag = await this.serviceRepository.findOne({
        where: { service_tag: service.service_tag },
      });
      if (existingServiceTag) {
        throw new BadRequestException('Service tag already exists');
      }

      // Check if service name already exists
      const existingServiceName = await this.serviceRepository.findOne({
        where: { name: service.name },
      });
      if (existingServiceName) {
        throw new BadRequestException('Service with this name already exists');
      }

      // Ensure it's a new instance and explicitly insert it
      const newService = this.serviceRepository.create(service);
      await this.serviceRepository.insert(newService); // Ensures INSERT, not UPDATE

      return newService;
    } catch (error) {
      throw new BadRequestException(error.message || 'Error creating service');
    }
  }

  async updateService(
    id: number,
    name: string,
    description: string,
    identity_number_start: number,
    identity_number_end: number,
    trial_number_fixed: number,
    status: string,
  ): Promise<Service> {
    const service = await this.serviceRepository.findOne({ where: { id } });

    await this.serviceRepository.update(
      { id },
      {
        name,
        description,
        identity_number_start,
        identity_number_end,
        trial_number_fixed,
        status,
      },
    );

    return service;
  }

  async deleteService(id: number): Promise<void> {
    await this.serviceRepository.delete({ id });
  }

  async getAllServices(): Promise<Service[]> {
    return this.serviceRepository.find();
  }

  async getServiceById(id: number): Promise<Service> {
    return this.serviceRepository.findOne({ where: { id } });
  }

  async getServiceByName(name: string): Promise<Service> {
    return this.serviceRepository.findOne({ where: { name } });
  }

  async getServiceByStatus(status: string): Promise<Service[]> {
    return this.serviceRepository.find({ where: { status } });
  }

  async getServiceByRange(start: number, end: number): Promise<Service[]> {
    return this.serviceRepository.find({
      where: { identity_number_start: start, identity_number_end: end },
    });
  }

  async getServiceByTrialNumber(trial_number_fixed: number): Promise<Service> {
    return this.serviceRepository.findOne({ where: { trial_number_fixed } });
  }

  async getServiceByIdentityNumber(
    identity_number_start: number,
    identity_number_end: number,
  ): Promise<Service> {
    return this.serviceRepository.findOne({
      where: { identity_number_start, identity_number_end },
    });
  }
}
