import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoleCandidateLog, CommunicationType } from './role_candidate_log.entity';
import { CreateRoleCandidateLogDto } from './dto/create-role-candidate-log.dto';

@Injectable()
export class RoleCandidateLogService {
  constructor(
    @InjectRepository(RoleCandidateLog)
    private readonly logRepo: Repository<RoleCandidateLog>,
  ) {}

  async createLog(log: CreateRoleCandidateLogDto) {
    return this.logRepo.save(log);
  }

  async getLogsByRoleCandidate(roleCandidateId: number) {
    return this.logRepo.find({ where: { roleCandidateId }, order: { createdAt: 'DESC' } });
  }

  async getAllLogs() {
    return this.logRepo.find({ order: { createdAt: 'DESC' } });
  }

  async deleteLog(id: number) {
    return this.logRepo.delete(id);
  }
}
