import { Test, TestingModule } from '@nestjs/testing';
import { CompanyScrapperControlController } from './company_scrapper_control.controller';
import { CompanyScrapperControlService } from './company_scrapper_control.service';

describe('CompanyScrapperControlController', () => {
  let controller: CompanyScrapperControlController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CompanyScrapperControlController],
      providers: [CompanyScrapperControlService],
    }).compile();

    controller = module.get<CompanyScrapperControlController>(CompanyScrapperControlController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
