import { Controller, Post, Body } from '@nestjs/common';
import { StripeService } from './stripe.service';
import Strip<PERSON> from 'stripe';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreateCheckoutSessionDto, StripeDto } from './dto/stripe.dto';

@ApiTags('stripe')
@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('create-customer')
  @ApiOperation({ summary: 'Create a new customer' })
  createCustomer(@Body() body: { email: string; name: string }) {
    return this.stripeService.createCustomer(body.email, body.name);
  }

  @Post('create-checkout-session')
  @ApiOperation({ summary: 'Create a new checkout session' })
  createCheckoutSession(@Body() data: CreateCheckoutSessionDto) {
    return this.stripeService.createCheckoutSession(data);
  }

  @Post('create-payment-intent')
  @ApiOperation({ summary: 'Create a new payment intent' })
  createPaymentIntent(@Body() body: StripeDto) {
    const { currency, amount } = body;
    return this.stripeService.createPaymentIntent(amount, currency);
  }
}
