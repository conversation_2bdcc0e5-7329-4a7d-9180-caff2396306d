import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudinaryService } from './cloudinary.service';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';

@Controller('upload')
@ApiTags('Cloudinary')
export class CloudinaryController {
  constructor(private cloudinaryService: CloudinaryService) {}

  @Post('image')
  @ApiOperation({ summary: 'Upload an image' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('image'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded!');
    }
    // Validate file type
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'image/webp',
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only JPEG, PNG, and WebP are allowed.',
      );
    }

    // Upload to Cloudinary
    try {
      const result = await this.cloudinaryService.uploadImage(file);
      return {
        url: result.url,
        id: result.public_id,
      };
    } catch (error) {
      throw new BadRequestException('File upload failed.');
    }
  }

  @Post('delete')
  @ApiOperation({ summary: 'Delete an image' })
  async deleteFile(publicId: string) {
    const result = await this.cloudinaryService.deleteImage(publicId);
    return result;
  }

  @Post('fetch')
  @ApiOperation({ summary: 'Fetch an image' })
  async fetchFile(publicId: string) {
    const result = await this.cloudinaryService.fetchImage(publicId);
    return result;
  }
}
