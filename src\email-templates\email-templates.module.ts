import { Module } from '@nestjs/common';
import { EmailTemplatesService } from './email-templates.service';
import { EmailTemplatesController } from './email-templates.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmailTemplates } from './emailTemplates.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EmailTemplates])],
  providers: [EmailTemplatesService],
  controllers: [EmailTemplatesController],
})
export class EmailTemplatesModule {}
