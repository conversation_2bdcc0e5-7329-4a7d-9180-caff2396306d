import { Process, Processor, InjectQueue } from '@nestjs/bull';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import { TwillioService } from '../../twillio/twillio.service';

@Injectable()
@Processor(QUEUE_NAMES.WHATSAPP)
export class WhatsAppQueueProcessor implements OnModuleInit {
  private readonly logger = new Logger(WhatsAppQueueProcessor.name);

  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    private readonly twillioService: TwillioService,
    @InjectQueue(QUEUE_NAMES.WHATSAPP) private readonly whatsappQueue: Queue,
  ) {
    console.log('🚀 WHATSAPP PROCESSOR: WhatsAppQueueProcessor instantiated');
  }

  async onModuleInit() {
    this.logger.log('WhatsAppQueueProcessor initialized');
    console.log('🚀 WHATSAPP PROCESSOR: WhatsAppQueueProcessor initialized');

    try {
      // Check queue status
      const waiting = await this.whatsappQueue.getWaiting();
      const active = await this.whatsappQueue.getActive();
      console.log('🚀 WHATSAPP PROCESSOR: Queue stats:', {
        waiting: waiting.length,
        active: active.length
      });

      console.log('🚀 WHATSAPP PROCESSOR: Initialization completed successfully');

    } catch (error) {
      console.error('🚀 WHATSAPP PROCESSOR: Error during initialization:', error);
      this.logger.error('Error during initialization:', error);
    }
  }

  @Process('send-whatsapp')
  async handleSendWhatsApp(job: Job<QueueJobData>) {
    console.log('📱 WHATSAPP PROCESSOR: ==========================================');
    console.log('📱 WHATSAPP PROCESSOR: handleSendWhatsApp method called!');
    console.log('📱 WHATSAPP PROCESSOR: Job ID:', job.id);
    console.log('📱 WHATSAPP PROCESSOR: Job data:', JSON.stringify(job.data, null, 2));
    console.log('📱 WHATSAPP PROCESSOR: Job opts:', job.opts);
    console.log('📱 WHATSAPP PROCESSOR: ==========================================');

    const { candidateSequenceStatusId, candidateId, stepId, recipientPhone } = job.data;

    console.log('📱 WHATSAPP PROCESSOR: Extracted data:');
    console.log('📱 WHATSAPP PROCESSOR: - candidateSequenceStatusId:', candidateSequenceStatusId);
    console.log('📱 WHATSAPP PROCESSOR: - candidateId:', candidateId);
    console.log('📱 WHATSAPP PROCESSOR: - stepId:', stepId);
    console.log('📱 WHATSAPP PROCESSOR: - recipientPhone:', recipientPhone);

    if (!recipientPhone) {
      console.error('📱 WHATSAPP PROCESSOR: ❌ No recipient phone provided!');
      throw new Error('No recipient phone provided');
    }

    console.log('📱 WHATSAPP PROCESSOR: Processing WhatsApp job for candidate', candidateId, 'step', stepId);
    this.logger.log(`Processing WhatsApp job for candidate ${candidateId}, step ${stepId}`);

    try {
      // Update status to QUEUED
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.QUEUED,
      );

      // Send dummy WhatsApp message using Twilio (with isTemplate: true)
      const dummyMessage = `Test WhatsApp message for candidate ${candidateId}, step ${stepId}`;
      const dummyTemplateData = {
        candidate_id: candidateId.toString(),
        step_id: stepId.toString(),
        sent_at: new Date().toISOString(),
      };

      const result = await this.twillioService.sendWhatsAppMessage(
        recipientPhone,
        dummyMessage,
        undefined, // no media
        'UK', // default region
        true, // isTemplate: true - Twilio will handle it
        dummyTemplateData,
      );

      // Update status to SENT
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.SENT,
        {
          sentTo: recipientPhone,
          sentAt: new Date().toISOString(),
          messageSid: result.sid,
        },
      );

      this.logger.log(`WhatsApp message sent successfully for candidate ${candidateId}, step ${stepId}`);

      // Simulate delivery confirmation
      setTimeout(async () => {
        try {
          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.DELIVERED,
          );
          this.logger.log(`WhatsApp delivery confirmed for candidate ${candidateId}, step ${stepId}`);
        } catch (error) {
          this.logger.error(`Failed to update delivery status: ${error.message}`);
        }
      }, 3000);

    } catch (error) {
      // Enhanced error logging with categorization
      const errorContext = {
        jobId: job.id,
        candidateId,
        stepId,
        candidateSequenceStatusId,
        recipientPhone,
        errorType: error.constructor.name,
        errorMessage: error.message,
        errorStack: error.stack,
        timestamp: new Date().toISOString(),
      };

      this.logger.error(`❌ Failed to send WhatsApp message for candidate ${candidateId}:`, errorContext);
      console.log(`❌ WHATSAPP PROCESSOR: Failed to send WhatsApp message for candidate ${candidateId}:`, {
        error: error.message,
        type: error.constructor.name,
        jobId: job.id,
      });

      // Categorize error types for better handling
      let errorCategory = 'UNKNOWN';
      if (error.message.includes('Candidate sequence status') && error.message.includes('not found')) {
        errorCategory = 'DATA_INTEGRITY';
      } else if (error.message.includes('Twilio') || error.message.includes('WhatsApp')) {
        errorCategory = 'WHATSAPP_SERVICE';
      } else if (error.message.includes('phone') || error.message.includes('required')) {
        errorCategory = 'VALIDATION';
      }

      // Only update status if candidateSequenceStatusId exists
      if (candidateSequenceStatusId) {
        try {
          await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);

          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.FAILED,
            {
              error: error.message,
              errorCategory,
              errorType: error.constructor.name,
              failedAt: new Date().toISOString(),
              jobId: job.id,
            },
          );

          this.logger.log(`Updated status to FAILED for candidateSequenceStatusId ${candidateSequenceStatusId}`);
        } catch (statusError) {
          this.logger.error(`Critical: Failed to update status for candidateSequenceStatusId ${candidateSequenceStatusId}:`, {
            statusError: statusError.message,
            originalError: error.message,
            candidateId,
            stepId,
          });
        }
      } else {
        this.logger.warn(`Cannot update status - candidateSequenceStatusId is missing for candidate ${candidateId}, step ${stepId}`);
      }

      throw error;
    }
  }


}
