import { IsOptional } from 'class-validator';

export class AddPersonByScrapperDto {
  @IsOptional()
  profile_url: string;

  @IsOptional()
  full_name: string;

  @IsOptional()
  first_name: string;

  @IsOptional()
  last_name: string;

  @IsOptional()
  avator: string;

  @IsOptional()
  company_id: number;

  @IsOptional()
  headline: string;

  @IsOptional()
  SR_specied_industry: string;

  @IsOptional()
  summary: string;

  @IsOptional()
  industry: string;

  @IsOptional()
  current_title: string;

  @IsOptional()
  user_id: string;

  @IsOptional()
  address: string;

  @IsOptional()
  sector_id: string;

  @IsOptional()
  country_id: number;

  @IsOptional()
  email: string;

  @IsOptional()
  phone_number: string;

  @IsOptional()
  is_hiring_person: string;

  @IsOptional()
  scrapper_profile_name: string;
}
