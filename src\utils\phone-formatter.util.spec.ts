import { formatToE164, isValidE164, formatForWhatsApp, extractFromWhatsApp, normalizePhoneNumber, getCountryCode, formatForDisplay } from './phone-formatter.util';

describe('PhoneFormatterUtil', () => {
  describe('formatToE164', () => {
    it('should format UK mobile numbers correctly', () => {
      expect(formatToE164('07123456789')).toBe('+447123456789');
      expect(formatToE164('7123456789')).toBe('+447123456789');
      expect(formatToE164('447123456789')).toBe('+44447123456789');
    });

    it('should handle already formatted E.164 numbers', () => {
      expect(formatToE164('+447123456789')).toBe('+447123456789');
      expect(formatToE164('+1234567890')).toBe('+1234567890');
    });

    it('should handle international format with 00 prefix', () => {
      expect(formatToE164('00447123456789')).toBe('+447123456789');
      expect(formatToE164('001234567890')).toBe('+1234567890');
    });

    it('should use default country code for numbers without prefix', () => {
      expect(formatToE164('1234567890', '+1')).toBe('+11234567890');
      expect(formatToE164('1234567890')).toBe('+441234567890');
    });

    it('should handle numbers with spaces and special characters', () => {
      expect(formatToE164('07123 456 789')).toBe('+447123456789');
      expect(formatToE164('+44 (0) 7123 456789')).toBe('+4407123456789');
      expect(formatToE164('07123-456-789')).toBe('+447123456789');
    });

    it('should throw error for empty or invalid numbers', () => {
      expect(() => formatToE164('')).toThrow('Phone number is required');
      expect(() => formatToE164('123')).toThrow('Phone number too short');
    });
  });

  describe('isValidE164', () => {
    it('should validate correct E.164 numbers', () => {
      expect(isValidE164('+447123456789')).toBe(true);
      expect(isValidE164('+1234567890')).toBe(true);
      expect(isValidE164('+86123456789012')).toBe(true);
    });

    it('should reject invalid E.164 numbers', () => {
      expect(isValidE164('447123456789')).toBe(false);
      expect(isValidE164('+0123456789')).toBe(false); // starts with 0
      expect(isValidE164('+12345678901234567')).toBe(false); // too long
      expect(isValidE164('')).toBe(false);
      expect(isValidE164('+44')).toBe(false); // too short
      expect(isValidE164('+123')).toBe(false); // too short
    });
  });

  describe('formatForWhatsApp', () => {
    it('should format numbers for WhatsApp', () => {
      expect(formatForWhatsApp('07123456789')).toBe('whatsapp:+447123456789');
      expect(formatForWhatsApp('+447123456789')).toBe('whatsapp:+447123456789');
      expect(formatForWhatsApp('whatsapp:+447123456789')).toBe('whatsapp:+447123456789');
    });

    it('should handle numbers already with whatsapp prefix', () => {
      expect(formatForWhatsApp('whatsapp:07123456789')).toBe('whatsapp:+447123456789');
    });
  });

  describe('extractFromWhatsApp', () => {
    it('should extract phone number from WhatsApp format', () => {
      expect(extractFromWhatsApp('whatsapp:+447123456789')).toBe('+447123456789');
      expect(extractFromWhatsApp('+447123456789')).toBe('+447123456789');
    });
  });

  describe('normalizePhoneNumber', () => {
    it('should normalize phone numbers by removing prefixes', () => {
      expect(normalizePhoneNumber('whatsapp:+447123456789')).toBe('+447123456789');
      expect(normalizePhoneNumber('sms:+447123456789')).toBe('+447123456789');
      expect(normalizePhoneNumber('+447123456789')).toBe('+447123456789');
      expect(normalizePhoneNumber('')).toBe('');
    });
  });

  describe('getCountryCode', () => {
    it('should extract country codes correctly', () => {
      expect(getCountryCode('+447123456789')).toBe('+44');
      expect(getCountryCode('+1234567890')).toBe('+1');
      expect(getCountryCode('+86123456789')).toBe('+86');
    });

    it('should throw error for non-E.164 numbers', () => {
      expect(() => getCountryCode('447123456789')).toThrow('Phone number must be in E.164 format');
    });
  });

  describe('formatForDisplay', () => {
    it('should format UK numbers for display', () => {
      expect(formatForDisplay('+447123456789')).toBe('+44 7123 456 789');
    });

    it('should format US numbers for display', () => {
      expect(formatForDisplay('+11234567890')).toBe('+****************');
    });

    it('should handle other country codes with default format', () => {
      expect(formatForDisplay('+86123456789')).toBe('+86 1234 56789');
    });

    it('should return original if not E.164 format', () => {
      expect(formatForDisplay('447123456789')).toBe('447123456789');
    });
  });
});
