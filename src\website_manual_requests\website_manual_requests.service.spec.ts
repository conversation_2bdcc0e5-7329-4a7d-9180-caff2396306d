import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebsiteManualRequestsService } from './website_manual_requests.service';
import { WebsiteManualRequest, ManualInterestType } from './website_manual_requests.entity';
import { EmailService } from '../email/email.service';
import { S3bucketService } from '../s3bucket/s3bucket.service';
import { CreateWebsiteManualRequestDto } from './dto/create-website-manual-request.dto';

describe('WebsiteManualRequestsService', () => {
  let service: WebsiteManualRequestsService;
  let repository: Repository<WebsiteManualRequest>;
  let emailService: EmailService;
  let s3bucketService: S3bucketService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  const mockEmailService = {
    sendEmailWithAttachments: jest.fn(),
  };

  const mockS3bucketService = {
    zipFilesAsAttachment: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebsiteManualRequestsService,
        {
          provide: getRepositoryToken(WebsiteManualRequest),
          useValue: mockRepository,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: S3bucketService,
          useValue: mockS3bucketService,
        },
      ],
    }).compile();

    service = module.get<WebsiteManualRequestsService>(WebsiteManualRequestsService);
    repository = module.get<Repository<WebsiteManualRequest>>(getRepositoryToken(WebsiteManualRequest));
    emailService = module.get<EmailService>(EmailService);
    s3bucketService = module.get<S3bucketService>(S3bucketService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createManualRequest', () => {
    it('should create a manual request successfully', async () => {
      const createDto: CreateWebsiteManualRequestDto = {
        first_name: 'John',
        last_name: 'Doe',
        job_title: 'Software Engineer',
        company: 'Tech Corp',
        email: '<EMAIL>',
        manual_interest: ManualInterestType.BD_MANUAL,
        s3_url: 'https://bucket.s3.amazonaws.com/file.pdf',
      };

      const mockRequest = {
        id: 'uuid-123',
        ...createDto,
        email_sent: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockReturnValue(mockRequest);
      mockRepository.save.mockResolvedValue(mockRequest);
      mockEmailService.sendEmailWithAttachments.mockResolvedValue(undefined);
      mockRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.createManualRequest(createDto);

      expect(mockRepository.create).toHaveBeenCalledWith(createDto);
      expect(mockRepository.save).toHaveBeenCalledWith(mockRequest);
      expect(mockEmailService.sendEmailWithAttachments).toHaveBeenCalled();
      expect(result).toEqual(mockRequest);
    });

    it('should create a manual request without sending email if no URLs provided', async () => {
      const createDto: CreateWebsiteManualRequestDto = {
        first_name: 'Jane',
        last_name: 'Smith',
        job_title: 'Product Manager',
        company: 'Business Corp',
        email: '<EMAIL>',
        manual_interest: ManualInterestType.RECRUITMENT_MANUAL,
      };

      const mockRequest = {
        id: 'uuid-456',
        ...createDto,
        email_sent: false,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockRepository.create.mockReturnValue(mockRequest);
      mockRepository.save.mockResolvedValue(mockRequest);

      const result = await service.createManualRequest(createDto);

      expect(mockRepository.create).toHaveBeenCalledWith(createDto);
      expect(mockRepository.save).toHaveBeenCalledWith(mockRequest);
      expect(mockEmailService.sendEmailWithAttachments).not.toHaveBeenCalled();
      expect(result).toEqual(mockRequest);
    });
  });

  describe('getAllManualRequests', () => {
    it('should return all manual requests', async () => {
      const mockRequests = [
        { id: 'uuid-1', first_name: 'John', last_name: 'Doe' },
        { id: 'uuid-2', first_name: 'Jane', last_name: 'Smith' },
      ];

      mockRepository.find.mockResolvedValue(mockRequests);

      const result = await service.getAllManualRequests();

      expect(mockRepository.find).toHaveBeenCalledWith({
        order: { created_at: 'DESC' },
      });
      expect(result).toEqual(mockRequests);
    });
  });

  describe('getManualRequestById', () => {
    it('should return a manual request by ID', async () => {
      const mockRequest = { id: 'uuid-123', first_name: 'John', last_name: 'Doe' };
      mockRepository.findOne.mockResolvedValue(mockRequest);

      const result = await service.getManualRequestById('uuid-123');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'uuid-123' },
      });
      expect(result).toEqual(mockRequest);
    });

    it('should throw NotFoundException if request not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.getManualRequestById('non-existent')).rejects.toThrow('Manual request with ID non-existent not found');
    });
  });
});
