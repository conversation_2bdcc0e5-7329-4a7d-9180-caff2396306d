import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { ResumeService } from './resume.service';

@Controller('resumes')
export class ResumeController {
  constructor(private readonly resumeService: ResumeService) {}

  @Post()
  create(@Body() body: { userId: string; templateId: number; data: any }) {
    return this.resumeService.createResume(body.userId, body.templateId, body.data);
  }

  @Get(':userId')
  findByUser(@Param('userId') userId: string) {
    return this.resumeService.getResumesByUser(userId);
  }
}
