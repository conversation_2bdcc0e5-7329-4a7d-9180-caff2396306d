import { Module } from '@nestjs/common';
import { CompanyService } from './company.service';
import { CompanyController } from './company.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from './company.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { People } from 'src/people/people.entity';
import { Jobs } from 'src/jobs/jobs.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Company,
      PeopleAssignment,
      People,
      Jobs,
      PeopleAssignment,
    ]),
  ],
  providers: [CompanyService],
  controllers: [CompanyController],
})
export class CompanyModule {}
