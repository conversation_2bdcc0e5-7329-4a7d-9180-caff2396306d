import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { JobApplications } from './job_applications.entity';
import { Repository } from 'typeorm';
import { JobApplicationDto } from './dto/job_applications.dto';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';

@Injectable()
export class JobApplicationsService {
  constructor(
    @InjectRepository(JobApplications)
    private jobApplicationsRepository: Repository<JobApplications>,
  ) {}

  async createJobApplication(
    jobApplication: JobApplicationDto,
  ): Promise<JobApplications> {
    try {
      const newJobApplication =
        this.jobApplicationsRepository.create(jobApplication);
      const savedJobApplication =
        await this.jobApplicationsRepository.insert(newJobApplication);
      return savedJobApplication.raw[0];
    } catch (error) {
      throw new Error('Failed to create job application: ' + error.message);
    }
  }

  async getJobApplicationsByUserId(userId: string): Promise<JobApplications[]> {
    try {
      const jobApplications = await this.jobApplicationsRepository.find({
        where: { userId },
        relations: ['job'],
      });
      return jobApplications;
    } catch (error) {
      throw new Error('Failed to get job applications: ' + error.message);
    }
  }

  async getJobApplicationsByJobId(jobId: number): Promise<JobApplications[]> {
    try {
      const jobApplications = await this.jobApplicationsRepository.find({
        where: { jobId },
      });
      return jobApplications;
    } catch (error) {
      throw new Error('Failed to get job applications: ' + error.message);
    }
  }

  async updateJobApplicationStatus(
    id: number,
    status: string,
  ): Promise<JobApplications> {
    try {
      const jobApplication = await this.jobApplicationsRepository.findOne({
        where: { id },
      });
      if (!jobApplication) {
        throw new Error('Job application not found');
      }
      jobApplication.status = status;
      const updatedJobApplication =
        await this.jobApplicationsRepository.save(jobApplication);
      return updatedJobApplication;
    } catch (error) {
      throw new Error(
        'Failed to update job application status: ' + error.message,
      );
    }
  }

  async deleteJobApplication(id: number): Promise<void> {
    try {
      await this.jobApplicationsRepository.delete(id);
    } catch (error) {
      throw new Error('Failed to delete job application: ' + error.message);
    }
  }

  async getJobApplicantsByJobId(jobId: number): Promise<JobApplications[]> {
    try {
      const jobApplicants = await this.jobApplicationsRepository.find({
        where: { jobId },
      });
      return jobApplicants;
    } catch (error) {
      throw new Error('Failed to get job applicants: ' + error.message);
    }
  }

  async getAllApplicantJobStatuses(userId: string): Promise<JobApplications[]> {
    try {
      const jobApplicants = await this.jobApplicationsRepository.find({
        where: { userId },
        relations: ['user', 'user.people'],
      });
      return jobApplicants;
    } catch (error) {
      throw new Error('Failed to get job applicants: ' + error.message);
    }
  }

  async getAllJobApplicants(): Promise<{ job: Jobs; applicants: Users[] }[]> {
    try {
      const jobApplications = await this.jobApplicationsRepository.find({
        relations: ['job','user.people'],
      });

      // Group by jobId
      const groupedByJob = jobApplications.reduce(
        (acc, application) => {
          const jobId = application.job.id;
          if (!acc[jobId]) {
            acc[jobId] = { job: application.job, applicants: [] };
          }
          acc[jobId].applicants.push(application.user);
          return acc;
        },
        {} as Record<number, { job: Jobs; applicants: Users[] }>,
      );

      return Object.values(groupedByJob);
    } catch (error) {
      throw new Error('Failed to get job applicants: ' + error.message);
    }
  }
}