import { resolveMx } from 'dns/promises';

export const disposableDomains = [
  "mailinator.com",
  "yopmail.com",
  "tempmail.com",
  "guerrillamail.com",
  "temp-mail.org",
  "fakeinbox.com",
  "sharklasers.com",
  "10minutemail.com",
  "trashmail.com",
  "getairmail.com",
  "dispostable.com",
  "mailnesia.com",
  "emailondeck.com",
  "tempinbox.com",
  "throwawaymail.com",
  "tempmail.net",
  "maildrop.cc",
  "mailcatch.com",
  "mailforspam.com",
  "spambox.us",
  "spamgourmet.com",
  "spam.la",
  "spamex.com",
  "spambox.me",
  "guerrillamailblock.com",
  "guerrillamail.com",
  "mailinator2.com",
  "mailinator3.com",
  "mailinator4.com",
  "mailinator5.com",
  "mailinator6.com",
  "mailinator7.com",
  "mailinator8.com",
  "mailinator9.com",
  "mailinator10.com",
  "mailinator11.com",
  "mailinator12.com",
  "mailinator13.com",
  "mailinator14.com",
];

interface EmailValidationResult {
  isValid: boolean;
  validators: {
    regex: { valid: boolean };
    disposable: { valid: boolean };
    mx: { valid: boolean };
  };
  reasons?: string[];
}

export const validateEmail = async (email: string): Promise<EmailValidationResult> => {
  const reasons: string[] = [];
  const result: EmailValidationResult = {
    isValid: true,
    validators: {
      regex: { valid: true },
      disposable: { valid: true },
      mx: { valid: true },
    },
    reasons,
  };

  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // Validate email format
  if (!emailRegex.test(email)) {
    result.validators.regex.valid = false;
    reasons.push("Invalid email format");
  }

  // Extract domain and validate
  const domain = email.split("@")[1];
  if (domain) {
    // Check for disposable domain
    if (disposableDomains.includes(domain.toLowerCase())) {
      result.validators.disposable.valid = false;
      reasons.push("Disposable email domain");
    }

    // Check MX records
    try {
      const mxRecords = await resolveMx(domain);
      if (!mxRecords || mxRecords.length === 0) {
        result.validators.mx.valid = false;
        reasons.push("No MX records found");
      }
    } catch (error) {
      result.validators.mx.valid = false;
      reasons.push("MX record lookup failed");
    }
  }

  // Determine overall validity (MX record validation is optional)
  if (!result.validators.regex.valid || !result.validators.disposable.valid) {
    result.isValid = false;
  }

  return result;
};

export const isBusinessEmail = (email: string, websiteLink: string): boolean => {
  try {
    if (!email || !websiteLink) return false;

    const emailDomain = email.split("@")[1]?.toLowerCase();
    if (!emailDomain) return false;

    const clearWebsiteLink = websiteLink
      .replace(/^(https?:\/\/)?(www\.)?/, "")
      .replace(/\/$/, "");
    const websiteDomain = clearWebsiteLink.toLowerCase();
    if (!websiteDomain) return false;

    const emailDomainParts = emailDomain.split(".");
    const websiteDomainParts = websiteDomain.split(".");

    // Direct domain match
    if (emailDomain === websiteDomain) {
      return true;
    }

    // Subdomain matches
    if (emailDomain.endsWith("." + websiteDomain)) {
      return true;
    }

    if (websiteDomain.endsWith("." + emailDomain)) {
      return true;
    }

    // Compare main parts (excluding TLD)
    const emailMainPart = emailDomainParts.slice(0, -1).join(".");
    const websiteMainPart = websiteDomainParts.slice(0, -1).join(".");

    if (emailMainPart.length > 4 && websiteMainPart.length > 4) {
      if (
        emailMainPart.includes(websiteMainPart) ||
        websiteMainPart.includes(emailMainPart)
      ) {
        return true;
      }
    }

    // Extract and compare words
    const emailWords = extractWordsFromDomain(emailMainPart);
    const websiteWords = extractWordsFromDomain(websiteMainPart);

    const sharedWords = emailWords.filter(
      (word) =>
        word.length >= 4 && websiteWords.some((webWord) => webWord === word)
    );

    if (sharedWords.length > 0) {
      return true;
    }

    // Levenshtein distance comparison
    if (emailMainPart.length > 5 && websiteMainPart.length > 5) {
      const distance = levenshteinDistance(emailMainPart, websiteMainPart);
      const maxLength = Math.max(emailMainPart.length, websiteMainPart.length);

      if (distance <= 3 || distance / maxLength < 0.3) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Error in isBusinessEmail function:", error);
    return false;
  }
};

export const extractWordsFromDomain = (domain: string): string[] => {
  const normalized = domain
    .replace(/[-_]/g, " ")
    .replace(/([a-z])([A-Z])/g, "$1 $2")
    .replace(/([a-zA-Z])(\d+)/g, "$1 $2")
    .replace(/(\d+)([a-zA-Z])/g, "$1 $2")
    .toLowerCase();

  return normalized
    .split(" ")
    .filter((word) => word.length >= 3)
    .map((word) => word.toLowerCase());
};

export const levenshteinDistance = (str1: string, str2: string): number => {
  const track: number[][] = Array(str2.length + 1)
    .fill(null)
    .map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    track[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    track[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      track[j][i] = Math.min(
        track[j][i - 1] + 1,
        track[j - 1][i] + 1,
        track[j - 1][i - 1] + indicator
      );
    }
  }

  return track[str2.length][str1.length];
};