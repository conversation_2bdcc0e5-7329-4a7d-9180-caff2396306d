import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileManagerController } from './file-manager.controller';
import { FileManagerService } from './file-manager.service';
import { FileManager } from './file-manager.entity';
import { S3bucketModule } from '../s3bucket/s3bucket.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FileManager]),
    S3bucketModule,
  ],
  controllers: [FileManagerController],
  providers: [FileManagerService],
  exports: [FileManagerService],
})
export class FileManagerModule {}
