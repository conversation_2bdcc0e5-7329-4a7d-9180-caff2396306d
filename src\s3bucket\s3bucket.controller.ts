import {
  Controller,
  Post,
  Delete,
  Get,
  Body,
  Query,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  Res,
  HttpStatus,
  Param,
} from '@nestjs/common';
import { Response } from 'express';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { S3bucketService } from './s3bucket.service';
import {
  ApiOperation,
  ApiTags,
  ApiConsumes,
  ApiBody,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { UploadImageDto } from './dto/uploadImage.dto';
import { DeleteFilesDto } from './dto/deleteFiles.dto';
import { CreateFolderDto } from './dto/createFolder.dto';
import { ConfigService } from '@nestjs/config';

@Controller('s3bucket')
@ApiTags('S3bucket')
export class S3bucketController {
  constructor(
    private readonly s3bucketService: S3bucketService,
    private readonly configService: ConfigService,
  ) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a file to S3 bucket' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: UploadImageDto })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File): Promise<string> {
    return this.s3bucketService.uploadFile(file);
  }

  @Delete('delete')
  @ApiOperation({ summary: 'Delete a file from S3 bucket' })
  @ApiQuery({
    name: 'fileUrl',
    required: true,
    description: 'S3 file URL to delete',
  })
  async deleteFile(@Query('fileUrl') fileUrl: string): Promise<void> {
    return this.s3bucketService.deleteFile(fileUrl);
  }

  @Delete('delete-multiple')
  @ApiOperation({ summary: 'Delete multiple files from S3 bucket' })
  async deleteFiles(@Body() deleteFilesDto: DeleteFilesDto): Promise<void> {
    return this.s3bucketService.deleteFiles(deleteFilesDto.fileUrls);
  }

  @Get('list')
  @ApiOperation({ summary: 'List all files in a folder' })
  @ApiQuery({
    name: 'prefix',
    required: false,
    description: 'Folder path to list files from',
  })
  async listFiles(@Query('prefix') prefix: string = ''): Promise<string[]> {
    return this.s3bucketService.listFiles(prefix);
  }

  @Post('create-folder')
  @ApiOperation({ summary: 'Create a folder in S3 bucket' })
  async createFolder(
    @Body() createFolderDto: CreateFolderDto,
  ): Promise<string> {
    return this.s3bucketService.createFolder(createFolderDto.folderName);
  }

  @Get('downloadFile')
  @ApiOperation({ summary: 'Download a file from S3 bucket' })
  @ApiQuery({
    name: 'fileUrl',
    required: true,
    description: 'S3 file URL to download',
  })
  async download(@Res() res: Response, @Query('fileUrl') fileUrl: string) {
    const { buffer, contentType, contentDisposition } =
      await this.s3bucketService.downloadFileWithMeta(fileUrl);

    res.set({
      'Content-Type': contentType || 'application/octet-stream',
      'Content-Disposition': contentDisposition || 'attachment',
      'Content-Length': buffer.length,
    });

    res.send(buffer);
  }

  @Get('download-all-cvs')
  @ApiOperation({
    summary: 'Download multiple files as a zip archive',
    description:
      'This endpoint generates a zip archive of the specified files from S3 and allows the user to download it.',
  })
  @ApiQuery({
    name: 'fileUrls',
    required: true,
    type: [String],
    description: 'List of file URLs to be included in the zip file',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully generated and downloaded the zip file.',
    content: {
      'application/zip': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to generate the zip file.',
  })
  async downloadZip(
    @Res() res: Response,
    @Query('fileUrls') fileUrls: string[], // Query parameter for file URLs
  ): Promise<void> {
    try {
      if (!fileUrls || fileUrls.length === 0) {
        res.status(HttpStatus.BAD_REQUEST).send('No file URLs provided');
        return;
      }
      const zipFile = await this.s3bucketService.zipFilesAsAttachment(fileUrls);

      // Set the appropriate headers for downloading the file
      res.set({
        'Content-Type': zipFile.contentType,
        'Content-Disposition': `attachment; filename="${zipFile.filename}"`,
      });

      // Send the content (ZIP file) as the response
      res.send(zipFile.content);
    } catch (error) {
      res.status(500).send('Error generating zip file');
    }
  }

  // Google Drive-style endpoints
  @Get('list-folder')
  @ApiOperation({ summary: 'List files and folders in Google Drive style' })
  @ApiQuery({
    name: 'prefix',
    required: false,
    description: 'Folder path to list files from',
  })
  async listFolder(@Query('prefix') prefix: string = '') {
    const result = await this.s3bucketService.listObjectsInFolder(prefix);

    // Transform the result to Google Drive style format
    const items = [];

    // Add folders
    result.folders.forEach(folder => {
      const folderName = folder.replace(prefix, '').replace('/', '');
      if (folderName) {
        items.push({
          name: folderName,
          type: 'folder',
          isFolder: true,
          path: folder,
          size: null,
          lastModified: null,
        });
      }
    });

    // Add files
    result.files.forEach(file => {
      const fileName = file.key.replace(prefix, '');
      if (fileName && !fileName.endsWith('/')) {
        items.push({
          name: fileName,
          type: 'file',
          isFolder: false,
          path: file.key,
          size: file.size,
          lastModified: file.lastModified,
          url: `https://${this.configService.get('AWS_S3_BUCKET_NAME')}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${file.key}`,
        });
      }
    });

    return items;
  }

  @Post('upload-to-folder')
  @ApiOperation({ summary: 'Upload file to specific folder' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadToFolder(
    @UploadedFile() file: Express.Multer.File,
    @Query('folderPath') folderPath: string = '',
  ): Promise<{ url: string; key: string }> {
    try {
      console.log(`📁 Uploading file to folder: "${folderPath}"`);

      // Use the new uploadFileToFolder method that respects folder paths
      const url = await this.s3bucketService.uploadFileToFolder(file, folderPath);

      // Extract the key from the URL
      const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
      const region = this.configService.get('AWS_REGION');
      const key = url.replace(`https://${bucketName}.s3.${region}.amazonaws.com/`, '');

      console.log(`📁 File uploaded successfully to: ${key}`);
      return { url, key };
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw new Error('File upload failed');
    }
  }

  @Post('upload-folder')
  @ApiOperation({ summary: 'Upload multiple files with folder structure' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files'))
  async uploadFolder(
    @UploadedFiles() files: Express.Multer.File[],
    @Query('folderPath') folderPath: string = '',
    @Body('filePaths') filePaths: string[] = [],
  ): Promise<{ uploadedFiles: Array<{ url: string; key: string; originalPath: string }> }> {
    try {
      console.log(`📁 Uploading ${files.length} files to folder: "${folderPath}"`);

      if (!files || files.length === 0) {
        throw new Error('No files provided for upload');
      }

      const uploadedFiles = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const originalPath = filePaths[i] || file.originalname;

        // Combine the base folder path with the file's relative path
        const fullPath = folderPath ? `${folderPath}/${originalPath}` : originalPath;

        // Extract the directory part of the file path
        const pathParts = fullPath.split('/');
        const fileName = pathParts.pop();
        const fileFolder = pathParts.join('/');

        console.log(`📁 Uploading file: ${originalPath} to ${fileFolder}`);

        const url = await this.s3bucketService.uploadFileToFolder(file, fileFolder);

        const bucketName = this.configService.get('AWS_S3_BUCKET_NAME');
        const region = this.configService.get('AWS_REGION');
        const key = url.replace(`https://${bucketName}.s3.${region}.amazonaws.com/`, '');

        uploadedFiles.push({
          url,
          key,
          originalPath
        });
      }

      console.log(`📁 Successfully uploaded ${uploadedFiles.length} files`);
      return { uploadedFiles };
    } catch (error) {
      console.error('Error uploading folder to S3:', error);
      throw new Error('Folder upload failed');
    }
  }

  @Post('rename')
  @ApiOperation({ summary: 'Rename file or folder' })
  async renameItem(
    @Body() body: { oldPath: string; newName: string }
  ) {
    const { oldPath, newName } = body;
    const pathParts = oldPath.split('/');
    pathParts[pathParts.length - 1] = newName;
    const newPath = pathParts.join('/');

    await this.s3bucketService.copyObject(oldPath, newPath);
    await this.s3bucketService.deleteObjectByKey(oldPath);

    return { message: 'Item renamed successfully', newPath };
  }

  @Post('move-to-trash')
  @ApiOperation({ summary: 'Move file or folder to trash' })
  async moveToTrash(
    @Body() body: { filePath: string }
  ) {
    const { filePath } = body;

    console.log('🗑️ Backend: Moving to trash:', filePath);

    // Create trash path with timestamp
    const timestamp = Date.now();
    const trashPath = `trash/${timestamp}_${filePath.replace(/\//g, '_')}`;

    try {
      // Use the new folder-aware move method
      await this.s3bucketService.moveToTrash(filePath, trashPath);

      console.log('🗑️ Backend: Successfully moved to trash');

      return {
        message: 'Item moved to trash successfully',
        trashPath,
        originalPath: filePath,
        timestamp
      };
    } catch (error) {
      console.error('🗑️ Backend Error moving to trash:', error);
      throw new Error(`Failed to move item to trash: ${error.message}`);
    }
  }

  @Get('list-trash')
  @ApiOperation({ summary: 'List items in trash' })
  async listTrash() {
    try {
      const result = await this.s3bucketService.listObjectsInFolder('trash/');

      const trashItems = result.files.map(file => {
        const fileName = file.key.replace('trash/', '');
        const parts = fileName.split('_');
        const timestamp = parts[0];
        const originalName = parts.slice(1).join('_');

        return {
          name: originalName,
          trashPath: file.key,
          originalPath: originalName.replace(/_/g, '/'),
          size: file.size,
          deletedAt: new Date(parseInt(timestamp)),
          lastModified: file.lastModified,
          type: 'file',
          isFolder: false,
          inTrash: true,
        };
      });

      return trashItems;
    } catch (error) {
      console.error('Error listing trash:', error);
      throw new Error('Failed to list trash items');
    }
  }

  @Post('restore-from-trash')
  @ApiOperation({ summary: 'Restore file or folder from trash' })
  async restoreFromTrash(
    @Body() body: { trashPath: string; originalPath: string }
  ) {
    const { trashPath, originalPath } = body;

    try {
      // Copy back to original location
      await this.s3bucketService.copyObject(trashPath, originalPath);

      // Delete from trash
      await this.s3bucketService.deleteObjectByKey(trashPath);

      return {
        message: 'Item restored successfully',
        restoredPath: originalPath
      };
    } catch (error) {
      console.error('Error restoring from trash:', error);
      throw new Error('Failed to restore item from trash');
    }
  }

  @Delete('permanent-delete')
  @ApiOperation({ summary: 'Permanently delete item from trash' })
  async permanentDelete(
    @Body() body: { trashPath: string }
  ) {
    const { trashPath } = body;

    try {
      await this.s3bucketService.deleteObjectByKey(trashPath);

      return { message: 'Item permanently deleted' };
    } catch (error) {
      console.error('Error permanently deleting:', error);
      throw new Error('Failed to permanently delete item');
    }
  }

  @Post('empty-trash')
  @ApiOperation({ summary: 'Empty entire trash folder' })
  async emptyTrash() {
    try {
      const result = await this.s3bucketService.listObjectsInFolder('trash/');

      // Delete all items in trash
      for (const file of result.files) {
        await this.s3bucketService.deleteObjectByKey(file.key);
      }

      return {
        message: 'Trash emptied successfully',
        deletedCount: result.files.length
      };
    } catch (error) {
      console.error('Error emptying trash:', error);
      throw new Error('Failed to empty trash');
    }
  }

  @Get('global-search')
  @ApiOperation({ summary: 'Global search across all files and folders' })
  @ApiQuery({
    name: 'query',
    required: true,
    description: 'Search query string',
  })
  @ApiQuery({
    name: 'includeTrash',
    required: false,
    description: 'Include trashed items in search results',
    type: Boolean,
  })
  async globalSearch(
    @Query('query') query: string,
    @Query('includeTrash') includeTrash: boolean = false,
  ) {
    try {
      console.log(`🔍 Global search started for: "${query}"`);

      if (!query || query.trim().length < 2) {
        return { results: [], message: 'Search query must be at least 2 characters' };
      }

      const searchQuery = query.toLowerCase().trim();
      const allResults = [];
      const maxResults = 100; // Limit results to prevent overwhelming the UI

      console.log(`🔍 Searching for: "${searchQuery}"`);

      // Use optimized search with pagination to avoid timeout
      console.log('🔍 Starting optimized search...');

      try {
        // Use the existing list-folder method with pagination for better performance
        const searchResults = await this.searchInBucket(searchQuery, includeTrash, maxResults);
        console.log(`🔍 Search completed: found ${searchResults.length} results`);

        return {
          results: searchResults,
          message: searchResults.length > 0
            ? `Found ${searchResults.length} result${searchResults.length === 1 ? '' : 's'} for "${query}"`
            : `No results found for "${query}"`,
          totalResults: searchResults.length
        };
      } catch (error) {
        console.error('🔍 Search error:', error.message);
        return {
          results: [],
          message: `Search failed: ${error.message}`,
          error: true
        };
      }
    } catch (error) {
      console.error('Error in global search:', error);
      throw new Error('Failed to perform global search');
    }
  }

  // Helper method for optimized search with better performance
  private async searchInBucket(searchQuery: string, includeTrash: boolean, maxResults: number): Promise<any[]> {
    const results = [];
    const processedFolders = new Set<string>();
    console.log(`🔍 Starting optimized bucket search for: "${searchQuery}"`);

    // Split search query into individual terms for better matching
    const searchTerms = searchQuery.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    console.log(`🔍 Search terms: ${searchTerms.join(', ')}`);

    // Use more efficient search with limited pagination
    try {
      let continuationToken = undefined;
      let totalProcessed = 0;
      const maxObjectsToProcess = 2000; // Limit to prevent timeout

      do {
        // Get objects in batches using the service method
        const batchResponse = await this.s3bucketService.listObjectsPaginated('', continuationToken, 500);

        const objects = batchResponse.Contents || [];
        console.log(`🔍 Processing batch of ${objects.length} objects`);

        // Process this batch
        for (const obj of objects) {
          if (results.length >= maxResults || totalProcessed >= maxObjectsToProcess) {
            console.log(`🔍 Reached limit: ${results.length} results or ${totalProcessed} processed`);
            break;
          }

          totalProcessed++;
          const file = {
            key: obj.Key,
            size: obj.Size || 0,
            lastModified: obj.LastModified || new Date()
          };

          // Skip trash files in regular search
          if (file.key.startsWith('trash/') && !includeTrash) {
            continue;
          }

          const pathParts = file.key.split('/');
          const fileName = pathParts[pathParts.length - 1];

          // Check if file matches any search term
          const fileMatches = searchTerms.some(term =>
            fileName && fileName.toLowerCase().includes(term)
          );

          // If it's a file (has content, not just a folder marker) and matches
          if (fileName && file.size > 0 && fileMatches) {
            const location = pathParts.slice(0, -1).join('/') || 'Root';
            results.push({
              name: fileName,
              type: 'file',
              isFolder: false,
              path: file.key,
              size: file.size,
              lastModified: file.lastModified,
              location: location,
              url: `https://${this.configService.get('AWS_S3_BUCKET_NAME')}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${file.key}`,
              inTrash: false,
            });
          }

          // Extract and process folder paths
          for (let i = 1; i < pathParts.length; i++) {
            const folderPath = pathParts.slice(0, i).join('/');
            const folderName = pathParts[i - 1];

            // Check if folder matches any search term
            const folderMatches = searchTerms.some(term =>
              folderName && folderName.toLowerCase().includes(term)
            );

            if (folderName &&
                folderMatches &&
                !processedFolders.has(folderPath) &&
                !folderPath.startsWith('trash/')) {

              processedFolders.add(folderPath);
              const parentLocation = pathParts.slice(0, i - 1).join('/') || 'Root';

              results.push({
                name: folderName,
                type: 'folder',
                isFolder: true,
                path: folderPath + '/',
                size: null,
                lastModified: null,
                location: parentLocation,
                inTrash: false,
              });
            }
          }
        }

        continuationToken = batchResponse.NextContinuationToken;

        // Break if we have enough results or processed enough objects
        if (results.length >= maxResults || totalProcessed >= maxObjectsToProcess) {
          break;
        }

      } while (continuationToken);

      // Search in trash if requested
      if (includeTrash) {
        try {
          const trashFiles = await this.s3bucketService.listAllObjects('trash/');

          trashFiles.files.forEach(file => {
            if (file.key.startsWith('trash/') && file.size > 0) {
              const fileName = file.key.replace('trash/', '');
              const parts = fileName.split('_');
              const timestamp = parts[0];
              const originalName = parts.slice(1).join('_');

              if (originalName.toLowerCase().includes(searchQuery)) {
                results.push({
                  name: originalName,
                  type: 'file',
                  isFolder: false,
                  trashPath: file.key,
                  originalPath: originalName.replace(/_/g, '/'),
                  size: file.size,
                  lastModified: file.lastModified,
                  deletedAt: new Date(parseInt(timestamp)),
                  location: 'Trash',
                  inTrash: true,
                });
              }
            }
          });
        } catch (trashError) {
          console.warn('Error searching trash:', trashError);
        }
      }

      // Sort results by relevance (exact matches first, then partial matches)
      results.sort((a, b) => {
        const aExact = a.name.toLowerCase() === searchQuery;
        const bExact = b.name.toLowerCase() === searchQuery;

        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;

        // Then by name alphabetically
        return a.name.localeCompare(b.name);
      });

      return results.slice(0, maxResults);

    } catch (error) {
      console.error('🔍 Error in bucket search:', error);
      throw error;
    }
  }
}
