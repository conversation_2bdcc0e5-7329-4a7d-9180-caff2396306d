import { Module } from '@nestjs/common';
import { FocusPointService } from './focus-point.service';
import { FocusPointController } from './focus-point.controller';
import { FocusPoint } from './focusPoint.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([FocusPoint])],
  providers: [FocusPointService],
  controllers: [FocusPointController],
})
export class FocusPointModule {}
