import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { addMinutes } from 'date-fns';

import { UsersService } from './users.service';
import { Users } from './users.entity';
import { EmailService } from '../email/email.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/updateUser.dto';

describe('UsersService', () => {
  let service: UsersService;
  let usersRepository: Repository<Users>;
  let emailService: EmailService;

  const mockUser: Partial<Users> = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    full_name: '<PERSON>',
    username: 'johndo<PERSON>',
    password_hash: '$2b$10$hashedpassword',
    password_salt: 'salt123',
    role: 'USER',
    designation: 'RECRUITER',
    status: 'ACTIVE',
    email_verified: false,
    email_verification_token: 'verification-token-123',
    email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    verification_code: '123456',
    verification_code_expires: addMinutes(new Date(), 10),
  };

  const mockUsersRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    usersRepository = module.get<Repository<Users>>(getRepositoryToken(Users));
    emailService = module.get<EmailService>(EmailService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('findOne', () => {
    it('should return a user when found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.findOne('<EMAIL>');

      expect(result).toEqual(mockUser);
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
    });

    it('should return undefined when user not found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      const result = await service.findOne('<EMAIL>');

      expect(result).toBeUndefined();
    });
  });

  describe('findOneAndUpdateVerification', () => {
    it('should update verification code and return user', async () => {
      const userWithoutCode = { ...mockUser, verification_code: undefined };
      mockUsersRepository.findOne.mockResolvedValue(userWithoutCode);
      mockUsersRepository.update.mockResolvedValue({ affected: 1 });

      // Mock crypto.randomInt
      jest.spyOn(crypto, 'randomInt').mockReturnValue(123456);

      const result = await service.findOneAndUpdateVerification('<EMAIL>');

      expect(result).toEqual({
        ...userWithoutCode,
        verification_code: '123456',
      });
      expect(mockUsersRepository.update).toHaveBeenCalledWith(
        mockUser.id,
        expect.objectContaining({
          verification_code: '123456',
          verification_code_expires: expect.any(Date),
        }),
      );
    });

    it('should return undefined when user not found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      const result = await service.findOneAndUpdateVerification('<EMAIL>');

      expect(result).toBeUndefined();
      expect(mockUsersRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('createUser', () => {
    const createUserDto: CreateUserDto = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'Jane',
      last_name: 'Smith',
      username: 'janesmith',
      source: 'CRM',
      role: 'USER' as any,
      designation: 'RECRUITER' as any,
      profile_picture: 'https://example.com/profile.jpg',
      status: 'ACTIVE' as any,
    };

    it('should create a new user successfully', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null); // User doesn't exist
      mockUsersRepository.create.mockReturnValue(mockUser);
      mockUsersRepository.save.mockResolvedValue(mockUser);
      mockEmailService.sendEmail.mockResolvedValue(true);

      // Mock bcrypt
      jest.spyOn(bcrypt, 'genSalt').mockResolvedValue('salt123' as never);
      jest.spyOn(bcrypt, 'hash').mockResolvedValue('hashedpassword' as never);

      // Mock crypto
      jest.spyOn(crypto, 'randomBytes').mockReturnValue(Buffer.from('verification-token-123'));

      const result = await service.createUser(createUserDto);

      expect(result).toEqual({
        message: 'User created successfully. Please check your email to verify your account.',
        user: mockUser,
      });
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { email: createUserDto.email },
      });
      expect(mockEmailService.sendEmail).toHaveBeenCalled();
    });

    it('should throw BadRequestException if user already exists', async () => {
      mockUsersRepository.findOne.mockResolvedValue(mockUser);

      await expect(service.createUser(createUserDto)).rejects.toThrow(
        new BadRequestException('User with this email already exists'),
      );
    });

    it('should throw InternalServerErrorException on database error', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);
      mockUsersRepository.create.mockReturnValue(mockUser);
      mockUsersRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.createUser(createUserDto)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      const userWithToken = {
        ...mockUser,
        email_verified: false,
        email_verification_token: 'valid-token',
        email_verification_expires: new Date(Date.now() + 60000), // Future date
      };
      mockUsersRepository.findOne.mockResolvedValue(userWithToken);
      mockUsersRepository.update.mockResolvedValue({ affected: 1 });

      await service.verifyEmail('valid-token');

      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { email_verification_token: 'valid-token' },
      });
      expect(mockUsersRepository.update).toHaveBeenCalledWith(userWithToken.id, {
        email_verified: true,
        email_verification_token: null,
        email_verification_expires: null,
      });
    });

    it('should throw BadRequestException for invalid token', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      await expect(service.verifyEmail('invalid-token')).rejects.toThrow(
        new BadRequestException('Invalid or expired verification token'),
      );
    });

    it('should throw BadRequestException for expired token', async () => {
      const userWithExpiredToken = {
        ...mockUser,
        email_verification_expires: new Date(Date.now() - 60000), // Past date
      };
      mockUsersRepository.findOne.mockResolvedValue(userWithExpiredToken);

      await expect(service.verifyEmail('expired-token')).rejects.toThrow(
        new BadRequestException('Invalid or expired verification token'),
      );
    });
  });

  describe('updateUser', () => {
    const updateUserDto: UpdateUserDto = {
      first_name: 'Updated',
      last_name: 'Name',
      phone_number: '+1234567890',
    };

    it('should update user successfully', async () => {
      mockUsersRepository.update.mockResolvedValue({ affected: 1 });

      await service.updateUser('user-id', updateUserDto);

      expect(mockUsersRepository.update).toHaveBeenCalledWith('user-id', updateUserDto);
    });

    it('should throw InternalServerErrorException on database error', async () => {
      mockUsersRepository.update.mockRejectedValue(new Error('Database error'));

      await expect(service.updateUser('user-id', updateUserDto)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockUsersRepository.findOne.mockResolvedValue(mockUser);
      mockUsersRepository.delete.mockResolvedValue({ affected: 1 });

      await service.deleteUser('user-id');

      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user-id' },
      });
      expect(mockUsersRepository.delete).toHaveBeenCalledWith('user-id');
    });

    it('should throw NotFoundException when user not found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      await expect(service.deleteUser('nonexistent-id')).rejects.toThrow(
        new NotFoundException('User not found'),
      );
    });
  });

  describe('getAllUsersByFilters', () => {
    const mockQueryBuilder = {
      andWhere: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    };

    beforeEach(() => {
      mockUsersRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });

    it('should return users with filters applied', async () => {
      const mockUsers = [mockUser];
      mockQueryBuilder.getMany.mockResolvedValue(mockUsers);

      const result = await service.getAllUsersByFilters(
        'USER',
        'RECRUITER',
        'ACTIVE',
        0,
        10,
        'John',
      );

      expect(result).toEqual(mockUsers);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.role = :role', { role: 'USER' });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.designation = :designation', {
        designation: 'RECRUITER',
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.status = :status', {
        status: 'ACTIVE',
      });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(user.first_name LIKE :search OR user.last_name LIKE :search OR user.email LIKE :search)',
        { search: '%John%' },
      );
    });

    it('should return users without filters', async () => {
      const mockUsers = [mockUser];
      mockQueryBuilder.getMany.mockResolvedValue(mockUsers);

      const result = await service.getAllUsersByFilters();

      expect(result).toEqual(mockUsers);
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalled();
    });
  });

  describe('getUsersByDesignation', () => {
    const mockQueryBuilder = {
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    };

    beforeEach(() => {
      mockUsersRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });

    it('should return users by designation', async () => {
      const mockUsers = [mockUser];
      mockQueryBuilder.getMany.mockResolvedValue(mockUsers);

      const result = await service.getUsersByDesignation('RECRUITER');

      expect(result).toEqual(mockUsers);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.designation = :designation', {
        designation: 'RECRUITER',
      });
    });

    it('should return all users when no designation provided', async () => {
      const mockUsers = [mockUser];
      mockQueryBuilder.getMany.mockResolvedValue(mockUsers);

      const result = await service.getUsersByDesignation();

      expect(result).toEqual(mockUsers);
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      mockQueryBuilder.getMany.mockRejectedValue(new Error('Database error'));

      const result = await service.getUsersByDesignation('RECRUITER');

      expect(result).toBeUndefined();
    });
  });

  describe('findUserById', () => {
    it('should return user when found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.findUserById('user-id');

      expect(result).toEqual(mockUser);
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user-id' },
      });
    });

    it('should return null when user not found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      const result = await service.findUserById('nonexistent-id');

      expect(result).toBeNull();
    });
  });

  describe('resendVerificationEmail', () => {
    it('should resend verification email successfully', async () => {
      const userWithoutVerification = {
        ...mockUser,
        email_verified: false,
        email_verification_token: null,
      };
      mockUsersRepository.findOne.mockResolvedValue(userWithoutVerification);
      mockUsersRepository.update.mockResolvedValue({ affected: 1 });
      mockEmailService.sendEmail.mockResolvedValue(true);

      // Mock crypto
      jest.spyOn(crypto, 'randomBytes').mockReturnValue(Buffer.from('new-verification-token'));

      await service.resendVerificationEmail('<EMAIL>', 'CRM');

      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(mockUsersRepository.update).toHaveBeenCalledWith(
        userWithoutVerification.id,
        expect.objectContaining({
          email_verification_token: 'new-verification-token',
          email_verification_expires: expect.any(Date),
        }),
      );
      expect(mockEmailService.sendEmail).toHaveBeenCalled();
    });

    it('should throw BadRequestException when user not found', async () => {
      mockUsersRepository.findOne.mockResolvedValue(null);

      await expect(service.resendVerificationEmail('<EMAIL>', 'CRM')).rejects.toThrow(
        new BadRequestException('User not found'),
      );
    });

    it('should throw BadRequestException when email already verified', async () => {
      const verifiedUser = { ...mockUser, email_verified: true };
      mockUsersRepository.findOne.mockResolvedValue(verifiedUser);

      await expect(service.resendVerificationEmail('<EMAIL>', 'CRM')).rejects.toThrow(
        new BadRequestException('Email is already verified'),
      );
    });
  });
});
