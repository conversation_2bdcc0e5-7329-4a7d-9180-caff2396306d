import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleCandidateLog } from './role_candidate_log.entity';
import { RoleCandidateLogService } from './role_candidate_log.service';
import { RoleCandidateLogController } from './role_candidate_log.controller';

@Module({
  imports: [TypeOrmModule.forFeature([RoleCandidateLog])],
  providers: [RoleCandidateLogService],
  controllers: [RoleCandidateLogController],
  exports: [RoleCandidateLogService],
})
export class RoleCandidateLogModule {}
