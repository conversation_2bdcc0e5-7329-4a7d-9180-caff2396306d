import { Module } from '@nestjs/common';
import { RoleCandidatesService } from './role_candidates.service';
import { RoleCandidatesController } from './role_candidates.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleCandidate } from './role_candidates.entity';
import { Roles } from 'src/roles/roles.entity';
import { People } from 'src/people/people.entity';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Languages } from 'src/languages/langauges.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';
import { RoleCandidateLogModule } from './role_candidate_log.module';
import { RecruitmentService } from './recruitment/recruitment.service';
import { RecruitmentController } from './recruitment/recruitment.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RoleCandidate,
      Roles,
      People,
      Company,
      PersonEmail,
      PersonPhone,
      Qualifications,
      Languages,
      PersonSkill, 
    ]),
    RoleCandidateLogModule,
  ],
  providers: [RoleCandidatesService, S3bucketService, RecruitmentService],
  controllers: [RoleCandidatesController, RecruitmentController],
})
export class RoleCandidatesModule {}
