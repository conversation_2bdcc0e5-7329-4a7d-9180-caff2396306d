import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('scrapper_stats')
@Index(['date'])
@Index(['region'])
export class ScrapperStats {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'date', nullable: true })
  date: string;

  @Column({ type: 'int', nullable: true })
  total_clicks: number;

  @Column({ type: 'int', nullable: true })
  matching_criteria: number;

  @Column({ type: 'int', nullable: true })
  unmatching_criteria: number;

  @Column({ type: 'int', nullable: true })
  direct_companies: number;

  @Column({ type: 'int', nullable: true })
  snr_companies: number;

  @Column({ type: 'int', nullable: true })
  existing_companies: number;

  @Column({ type: 'int', nullable: true })
  existing_direct_companies: number;

  @Column({ type: 'int', nullable: true })
  existing_snr_companies: number;

  @Column({ type: 'int', nullable: true })
  new_companies: number;

  @Column({ type: 'int', nullable: true })
  new_direct_companies: number;

  @Column({ type: 'int', nullable: true })
  new_snr_companies: number;

  @Column({ type: 'varchar', nullable: true })
  region: string;

  @Column({ type: 'int', nullable: true })
  login_errors: number;

  @Column({ type: 'int', nullable: true })
  job_click_errors: number;

  @Column({ type: 'int', nullable: true })
  no_member_error: number;

  @Column({ nullable: true })
  scrapper_profile_name: string;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
