import { IsOptional } from 'class-validator';

export class AddJobByScrapperDto {
  @IsOptional()
  job_posting_link: string;

  @IsOptional()
  job_post_date: string;

  @IsOptional()
  job_title: string;

  @IsOptional()
  salary_experience_remote: string;

  @IsOptional()
  location: string;

  @IsOptional()
  applicants: string;

  @IsOptional()
  industry: string;

  @IsOptional()
  SR_specified_industry: string;

  @IsOptional()
  job_description: string;

  @IsOptional()
  skills_required: string;

  @IsOptional()
  company_benefits: string;

  @IsOptional()
  company_id: number;

  @IsOptional()
  sector_id: number;

  @IsOptional()
  user_id: string;

  @IsOptional()
  country_id: number;

  @IsOptional()
  scrapper_profile_name: string;
}
