import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { QUEUE_NAMES } from './queue.constants';

export interface QueueJobData {
  candidateSequenceStatusId: number;
  candidateId: number;
  stepId: number;
  sequenceId: number;
  medium: string;
  templateId?: number;
  recipientEmail?: string;
  recipientPhone?: string;
  recipientLinkedIn?: string;
  subject?: string;
  body?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class QueueService implements OnModuleInit {
  private readonly logger = new Logger(QueueService.name);
  private redisConnectionStatus = 'unknown';

  constructor(
    @InjectQueue(QUEUE_NAMES.EMAIL) private emailQueue: Queue,
    @InjectQueue(QUEUE_NAMES.WHATSAPP) private whatsappQueue: Queue,
    @InjectQueue(QUEUE_NAMES.SMS) private smsQueue: Queue,
    @InjectQueue(QUEUE_NAMES.CALL) private callQueue: Queue,
    @InjectQueue(QUEUE_NAMES.LINKEDIN) private linkedinQueue: Queue,
  ) {}

  async onModuleInit() {
    await this.checkRedisConnection();
  }

  private async checkRedisConnection(): Promise<void> {
    try {
      // Test Redis connection by trying to get stats from one queue
      await this.emailQueue.getWaiting();
      this.redisConnectionStatus = 'connected';
      this.logger.log('Redis connection established successfully');



    } catch (error) {
      this.redisConnectionStatus = 'disconnected';
      this.logger.error('Redis connection failed:', error.message);
      this.logger.warn('Queue functionality will be limited without Redis connection');
    }
  }

  async addJobToQueue(medium: string, jobData: QueueJobData, delay?: number): Promise<void> {
    console.log(`📤 QUEUE-SERVICE: addJobToQueue called for medium: ${medium}`);
    console.log(`📤 QUEUE-SERVICE: Job data:`, JSON.stringify(jobData, null, 2));
    console.log(`📤 QUEUE-SERVICE: Delay: ${delay || 0}ms`);
    console.log(`📤 QUEUE-SERVICE: Redis connection status: ${this.redisConnectionStatus}`);

    // Check Redis connection status first
    if (this.redisConnectionStatus !== 'connected') {
      console.error(`📤 QUEUE-SERVICE: ❌ Cannot add job to ${medium} queue: Redis connection not available`);
      this.logger.warn(`Cannot add job to ${medium} queue: Redis connection not available`);
      // In development, we can log the job instead of failing
      this.logger.log(`Would add job to ${medium} queue:`, JSON.stringify(jobData, null, 2));
      return;
    }

    const options = {
      delay: delay || 0,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
      removeOnFail: 10,     // Keep last 10 failed jobs for debugging
    };

    console.log(`📤 QUEUE-SERVICE: Job options:`, options);

    try {
      console.log(`📤 QUEUE-SERVICE: Adding job to ${medium.toUpperCase()} queue...`);

      let job: any;
      switch (medium.toUpperCase()) {
        case 'EMAIL':
          console.log(`📤 QUEUE-SERVICE: Adding to email queue...`);
          job = await this.emailQueue.add('send-email', jobData, options);
          console.log(`📤 QUEUE-SERVICE: ✅ Email job added with ID: ${job.id}`);
          break;

        case 'WHATSAPP':
          console.log(`📤 QUEUE-SERVICE: Adding to WhatsApp queue...`);
          job = await this.whatsappQueue.add('send-whatsapp', jobData, options);
          console.log(`📤 QUEUE-SERVICE: ✅ WhatsApp job added with ID: ${job.id}`);
          break;

        case 'SMS':
          console.log(`📤 QUEUE-SERVICE: Adding to SMS queue...`);
          job = await this.smsQueue.add('send-sms', jobData, options);
          console.log(`📤 QUEUE-SERVICE: ✅ SMS job added with ID: ${job.id}`);
          break;

        case 'CALL':
          console.log(`📤 QUEUE-SERVICE: Adding to call queue...`);
          job = await this.callQueue.add('make-call', jobData, options);
          console.log(`📤 QUEUE-SERVICE: ✅ Call job added with ID: ${job.id}`);
          break;

        case 'LINKEDIN':
          console.log(`📤 QUEUE-SERVICE: Adding to LinkedIn queue...`);
          job = await this.linkedinQueue.add('send-linkedin', jobData, options);
          console.log(`📤 QUEUE-SERVICE: ✅ LinkedIn job added with ID: ${job.id}`);
          break;

        default:
          console.error(`📤 QUEUE-SERVICE: ❌ Unsupported medium: ${medium}`);
          throw new Error(`Unsupported medium: ${medium}`);
      }

      console.log(`📤 QUEUE-SERVICE: ✅ Successfully added job to ${medium} queue`);
      this.logger.log(`Successfully added job to ${medium} queue`);
    } catch (error) {
      console.error(`📤 QUEUE-SERVICE: ❌ Failed to add job to ${medium} queue:`, error.message);
      console.error(`📤 QUEUE-SERVICE: ❌ Error stack:`, error.stack);
      this.logger.error(`Failed to add job to ${medium} queue: ${error.message}`);
      throw error;
    }
  }

  async addBulkJobs(medium: string, jobs: QueueJobData[]): Promise<void> {
    this.logger.log(`Adding ${jobs.length} bulk jobs to ${medium} queue`);

    // Validate inputs
    if (!jobs || jobs.length === 0) {
      throw new Error('No jobs provided for bulk addition');
    }

    if (this.redisConnectionStatus !== 'connected') {
      const error = `Cannot add bulk jobs to ${medium} queue: Redis connection not available`;
      this.logger.error(error);
      throw new Error(error);
    }

    try {
      const jobsWithOptions = jobs.map((jobData) => ({
        name: `send-${medium.toLowerCase()}`,
        data: jobData,
        opts: {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10, // Keep last 10 completed jobs for monitoring
          removeOnFail: 10,     // Keep last 10 failed jobs for debugging
        },
      }));

      switch (medium.toUpperCase()) {
        case 'EMAIL':
          await this.emailQueue.addBulk(jobsWithOptions);
          break;
        case 'WHATSAPP':
          await this.whatsappQueue.addBulk(jobsWithOptions);
          break;
        case 'SMS':
          await this.smsQueue.addBulk(jobsWithOptions);
          break;
        case 'CALL':
          await this.callQueue.addBulk(jobsWithOptions);
          break;
        case 'LINKEDIN':
          await this.linkedinQueue.addBulk(jobsWithOptions);
          break;
        default:
          throw new Error(`Unsupported medium: ${medium}`);
      }

      this.logger.log(`Successfully added ${jobs.length} bulk jobs to ${medium} queue`);
    } catch (error) {
      this.logger.error(`Failed to add bulk jobs to ${medium} queue:`, {
        error: error.message,
        medium,
        jobCount: jobs.length,
        redisStatus: this.redisConnectionStatus,
      });
      throw error;
    }
  }

  async getQueueStats(queueName: string): Promise<any> {
    // Check Redis connection status first
    if (this.redisConnectionStatus !== 'connected') {
      return {
        name: queueName,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        total: 0,
        status: 'redis_disconnected',
        message: 'Redis connection not available. Queue stats unavailable.'
      };
    }

    let queue: Queue;

    // Normalize queue name by removing '-queue' suffix if present
    const normalizedQueueName = queueName.replace('-queue', '').toUpperCase();

    switch (normalizedQueueName) {
      case 'EMAIL':
        queue = this.emailQueue;
        break;
      case 'WHATSAPP':
        queue = this.whatsappQueue;
        break;
      case 'SMS':
        queue = this.smsQueue;
        break;
      case 'CALL':
        queue = this.callQueue;
        break;
      case 'LINKEDIN':
        queue = this.linkedinQueue;
        break;
      default:
        throw new Error(`Unknown queue: ${queueName}`);
    }

    try {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed(),
      ]);

      return {
        name: queueName,
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        total: waiting.length + active.length + completed.length + failed.length + delayed.length,
        status: 'connected'
      };
    } catch (error) {
      this.logger.error(`Failed to get stats for queue ${queueName}: ${error.message}`);
      return {
        name: queueName,
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        delayed: 0,
        total: 0,
        status: 'error',
        error: error.message
      };
    }
  }

  async pauseQueue(queueName: string): Promise<void> {
    const queue = this.getQueueByName(queueName);
    await queue.pause();
  }

  async resumeQueue(queueName: string): Promise<void> {
    const queue = this.getQueueByName(queueName);
    await queue.resume();
  }

  /**
   * Clear all jobs from a specific queue
   */
  async clearQueue(queueName: string): Promise<{ cleared: number }> {
    const queue = this.getQueueByName(queueName);

    // Get all job types
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    let totalCleared = 0;

    // Remove all jobs
    for (const job of [...waiting, ...active, ...completed, ...failed, ...delayed]) {
      try {
        await job.remove();
        totalCleared++;
      } catch (error) {
        this.logger.warn(`Failed to remove job ${job.id}: ${error.message}`);
      }
    }

    this.logger.log(`Cleared ${totalCleared} jobs from queue ${queueName}`);
    return { cleared: totalCleared };
  }

  /**
   * Clear all jobs from all queues
   */
  async clearAllQueues(): Promise<{ [queueName: string]: { cleared: number } }> {
    const results = {};
    const queueNames = Object.values(QUEUE_NAMES);

    for (const queueName of queueNames) {
      try {
        const result = await this.clearQueue(queueName);
        results[queueName] = result;
      } catch (error) {
        this.logger.error(`Failed to clear queue ${queueName}: ${error.message}`);
        results[queueName] = { cleared: 0, error: error.message };
      }
    }

    return results;
  }

  private getQueueByName(queueName: string): Queue {
    // Normalize queue name by removing '-queue' suffix if present
    const normalizedQueueName = queueName.replace('-queue', '').toUpperCase();

    switch (normalizedQueueName) {
      case 'EMAIL':
        return this.emailQueue;
      case 'WHATSAPP':
        return this.whatsappQueue;
      case 'SMS':
        return this.smsQueue;
      case 'CALL':
        return this.callQueue;
      case 'LINKEDIN':
        return this.linkedinQueue;
      default:
        throw new Error(`Unknown queue: ${queueName}`);
    }
  }

  /**
   * Get all available queue names
   */
  getAvailableQueues(): string[] {
    return Object.values(QUEUE_NAMES);
  }

  /**
   * Check if a queue exists
   */
  isValidQueue(queueName: string): boolean {
    const normalizedQueueName = queueName.replace('-queue', '').toUpperCase();
    return ['EMAIL', 'WHATSAPP', 'SMS', 'CALL', 'LINKEDIN'].includes(normalizedQueueName);
  }

  /**
   * Get Redis connection status
   */
  getRedisConnectionStatus(): string {
    return this.redisConnectionStatus;
  }

  /**
   * Get comprehensive queue system health
   */
  async getSystemHealth(): Promise<any> {
    const health = {
      redis: {
        status: this.redisConnectionStatus,
        connected: this.redisConnectionStatus === 'connected'
      },
      queues: {},
      overall: 'unknown'
    };

    if (this.redisConnectionStatus === 'connected') {
      const queueNames = Object.values(QUEUE_NAMES);
      let healthyQueues = 0;

      for (const queueName of queueNames) {
        try {
          const stats = await this.getQueueStats(queueName);
          health.queues[queueName] = {
            status: 'healthy',
            ...stats
          };
          healthyQueues++;
        } catch (error) {
          health.queues[queueName] = {
            status: 'unhealthy',
            error: error.message
          };
        }
      }

      health.overall = healthyQueues === queueNames.length ? 'healthy' : 'degraded';
    } else {
      health.overall = 'unhealthy';
      const queueNames = Object.values(QUEUE_NAMES);
      for (const queueName of queueNames) {
        health.queues[queueName] = {
          status: 'unavailable',
          error: 'Redis connection not available'
        };
      }
    }

    return health;
  }

  /**
   * Get detailed error statistics for monitoring and debugging
   */
  async getErrorStatistics(): Promise<any> {
    const errorStats = {
      timestamp: new Date().toISOString(),
      queues: {},
      summary: {
        totalFailed: 0,
        totalRetries: 0,
        errorCategories: {},
      }
    };

    if (this.redisConnectionStatus !== 'connected') {
      return {
        ...errorStats,
        error: 'Redis connection not available'
      };
    }

    const queueNames = Object.values(QUEUE_NAMES);

    for (const queueName of queueNames) {
      try {
        const queue = this.getQueueByName(queueName);
        const failed = await queue.getFailed();

        const queueErrorStats = {
          totalFailed: failed.length,
          errors: failed.map(job => ({
            id: job.id,
            data: job.data,
            failedReason: job.failedReason,
            processedOn: job.processedOn,
            finishedOn: job.finishedOn,
            attemptsMade: job.attemptsMade,
            opts: job.opts,
          }))
        };

        errorStats.queues[queueName] = queueErrorStats;
        errorStats.summary.totalFailed += failed.length;

        // Categorize errors
        failed.forEach(job => {
          const reason = job.failedReason || 'Unknown';
          if (!errorStats.summary.errorCategories[reason]) {
            errorStats.summary.errorCategories[reason] = 0;
          }
          errorStats.summary.errorCategories[reason]++;
        });

      } catch (error) {
        this.logger.error(`Failed to get error stats for queue ${queueName}: ${error.message}`);
        errorStats.queues[queueName] = {
          error: error.message
        };
      }
    }

    return errorStats;
  }

  /**
   * Retry all failed jobs in a specific queue
   */
  async retryFailedJobs(queueName: string): Promise<{ retried: number }> {
    this.logger.log(`Retrying failed jobs in queue ${queueName}`);

    try {
      const queue = this.getQueueByName(queueName);
      const failed = await queue.getFailed();

      let retriedCount = 0;
      for (const job of failed) {
        try {
          await job.retry();
          retriedCount++;
        } catch (error) {
          this.logger.warn(`Failed to retry job ${job.id}: ${error.message}`);
        }
      }

      this.logger.log(`Retried ${retriedCount} failed jobs in queue ${queueName}`);
      return { retried: retriedCount };
    } catch (error) {
      this.logger.error(`Failed to retry jobs in queue ${queueName}: ${error.message}`);
      throw error;
    }
  }

}
