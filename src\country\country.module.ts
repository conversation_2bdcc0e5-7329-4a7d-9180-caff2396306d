import { Module } from '@nestjs/common';
import { CountryService } from './country.service';
import { CountryController } from './country.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Country } from './country.entity';
import { Sector } from 'src/sector/sector.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Country, Sector])],
  providers: [CountryService],
  controllers: [CountryController],
})
export class CountryModule {}
