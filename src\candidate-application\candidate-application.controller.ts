import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { CandidateApplicationService } from './candidate-application.service';
import { CandidateApplicationDto } from './dto/candidate-application.dto';

@ApiTags('Candidate Applications')
@Controller('candidate-applications')
export class CandidateApplicationController {
  constructor(
    private readonly candidateApplicationService: CandidateApplicationService,
  ) {}

  @Post('submit')
  @UseInterceptors(FileInterceptor('resume_file'))
  @ApiOperation({
    summary: 'Submit a candidate application',
    description: 'Submit a candidate application with optional resume file. Automatically sends <NAME_EMAIL> and <EMAIL>'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Candidate application data with optional resume file',
    schema: {
      type: 'object',
      properties: {
        first_name: {
          type: 'string',
          description: 'First name of the candidate',
          example: '<PERSON>',
        },
        last_name: {
          type: 'string',
          description: 'Last name of the candidate',
          example: 'Doe',
        },
        contact: {
          type: 'string',
          description: 'Contact email of the candidate',
          example: '<EMAIL>',
        },
        linkedin_profile: {
          type: 'string',
          description: 'LinkedIn profile URL (optional)',
          example: 'https://linkedin.com/in/johndoe',
        //   required: false,
        },
        job_location: {
          type: 'string',
          description: 'Preferred job location',
          example: 'London, UK',
        },
        desired_job_role: {
          type: 'string',
          description: 'Desired job role or position',
          example: 'Software Engineer',
        },
        years_of_experience: {
          type: 'string',
          description: 'Years of professional experience',
          example: '5 years',
        },
        availability: {
          type: 'string',
          description: 'Availability to start work',
          example: 'Immediately',
        },
        reason_for_job_change: {
          type: 'string',
          description: 'Reason for job change',
          example: 'Career growth and new challenges',
        },
        resume_file: {
          type: 'string',
          format: 'binary',
          description: 'Resume file (PDF, DOC, DOCX)',
        //   required: false,
        },
      },
      required: [
        'first_name',
        'last_name',
        'contact',
        'job_location',
        'desired_job_role',
        'years_of_experience',
        'availability',
        'reason_for_job_change'
      ],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Application submitted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Application submitted successfully',
        },
        applicationId: {
          type: 'string',
          example: 'APP_1672531200000_abc123def',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data or file format',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error - Failed to submit application',
  })
  async submitApplication(
    @Body() applicationData: CandidateApplicationDto,
    @UploadedFile() resumeFile?: Express.Multer.File,
  ): Promise<{ message: string; applicationId: string }> {
    try {
      // Validate file if provided
      if (resumeFile) {
        this.validateResumeFile(resumeFile);
      }

      return await this.candidateApplicationService.submitApplication(
        applicationData,
        resumeFile,
      );
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException({
        message: 'Failed to submit application',
        error: error.message,
      });
    }
  }

  private validateResumeFile(file: Express.Multer.File): void {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new BadRequestException('Resume file size must be less than 10MB');
    }

    // Check file type
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Resume file must be in PDF, DOC, or DOCX format',
      );
    }

    // Check file extension
    const allowedExtensions = ['.pdf', '.doc', '.docx'];
    const fileExtension = file.originalname.toLowerCase().substring(
      file.originalname.lastIndexOf('.'),
    );

    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestException(
        'Resume file must have .pdf, .doc, or .docx extension',
      );
    }
  }
}