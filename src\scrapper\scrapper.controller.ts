import { Body, Controller, Get, Post, Put, Query } from '@nestjs/common';
import { ScrapperService } from './scrapper.service';
import {
  CreateCompanyByScrapperDto,
  UpdateCompanyByScrapperDto,
} from './dto/createCompanyByScrapper.dto';
import { AddPersonByScrapperDto } from './dto/addPersonByScrapper.dto';
import { AddJobByScrapperDto } from './dto/addJobByScrapper.dto';
import { ScrapperStatsDto } from './dto/scrapperStats.dto';
import { GetDailyScrapperReportDto } from './dto/getDailyScrapper.dto';
import { GetComanyControlDto } from './dto/getComanyControl.dto';

@Controller('scrapper')
export class ScrapperController {
  constructor(private readonly scrapperService: ScrapperService) {}

  @Post('addCompanyByScrapper')
  async addCompanyByScrapper(@Body() data: CreateCompanyByScrapperDto) {
    return this.scrapperService.createCompany(data);
  }

  @Post('updateCompanyByScrapper')
  async updateCompanyByScrapper(@Body() data: UpdateCompanyByScrapperDto) {
    return this.scrapperService.updateCompany(data);
  }

  @Post('addPersonByScrapper')
  async addPersonByScrapper(@Body() data: AddPersonByScrapperDto) {
    return this.scrapperService.addPersonByScrapper(data);
  }

  @Post('addJobPostByScrapper')
  async addJobPostByScrapper(@Body() data: AddJobByScrapperDto) {
    return this.scrapperService.addJobByScrapper(data);
  }

  @Post('addPersonOnly')
  async addPersonOnly(@Body() data: AddPersonByScrapperDto) {
    return this.scrapperService.addPersonOnly(data);
  }

  @Get('getCompanyLinkWithRegion')
  async getCompanyLinkWithRegion() {
    return this.scrapperService.getCompanyLinkWithRegion();
  }

  @Post('createScrapperDailyReport')
  async createScrapperDailyReport(@Body() data: ScrapperStatsDto) {
    return this.scrapperService.createScrapperDailyReport(data);
  }

  @Get('getDailyScrapperReport')
  async getDailyScrapperReport(
    @Query() queryParams: GetDailyScrapperReportDto,
  ) {
    return this.scrapperService.getDailyScrapperReport(queryParams);
  }
}
