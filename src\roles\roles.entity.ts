import { Calendar } from 'src/calendar/calendar.entity';
import { FocusPoint } from 'src/focus-point/focusPoint.entity';
import { People } from 'src/people/people.entity';
import { RoleHistory } from 'src/role-history/role-history.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { RoleLogs } from 'src/role_logs/role_logs.entity';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('roles')
export class Roles {
  @PrimaryGeneratedColumn()
  id: number;

  // client required fields
  @Column({ type: 'enum', enum: ['REGULAR', 'TRIAL', 'FIX'], nullable: true })
  @Index()
  category: string;

  //IF fix , then add start and end date
  @Index()
  @Column({ type: 'date', nullable: true })
  start_date: Date;

  @Index()
  @Column({ type: 'date', nullable: true })
  end_date: Date;

  @Column({ type: 'int', nullable: true })
  candidates_required: number;

  @Column({ type: 'boolean', nullable: true })
  is_credit: boolean;

  @Index()
  @Column({ type: 'int', nullable: true })
  role_number: number;

  @Index()
  @Column({ type: 'varchar', length: '255', nullable: true })
  title: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  postal_code: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  client_number: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  country: string;

  @Column({ type: 'enum', enum: ['LOW', 'MEDIUM', 'HIGH'], nullable: true })
  priority: string;

  //   SALARY RANGE
  @Column({ type: 'enum', enum: ['FIXED', 'VARIABLE'], nullable: true })
  salary_payment_type: string;

  // Salary fields are stored as string (varchar) for flexibility
  @Column({ type: 'varchar', nullable: true })
  @Index()
  salary_min: string;

  @Column({ type: 'varchar', nullable: true })
  @Index()
  salary_max: string;

  @Column({ type: 'varchar', nullable: true })
  salary_fixed: string;

  @Column({ type: 'enum', enum: ['USD', 'GBP'], nullable: true })
  salary_currency: string;

  @Column({
    type: 'enum',
    enum: [
      'HOURLY',
      'DAILY',
      'WEEKLY',
      'FORTNIGHT',
      'MONTHLY',
      'BIANNUALLY',
      'ANNUALY',
    ],
    nullable: true,
  })
  @Index()
  salary_type: string;

  @Column({ type: 'varchar', nullable: true })
  @Index()
  current_status: string;

  // locations for the role
  @Column({ type: 'varchar', length: '255', nullable: true })
  @Index()
  locations: string;

  // locations for the role
  @Column({
    type: 'enum',
    enum: [
      'LESS_THAN_MONTH',
      '1MONTH',
      '3MONTHS',
      '6MONTHS',
      '9MONTHS',
      '12MONTHS',
      '15MONTHS',
      '18MONTHS',
      '24MONTHS',
      '36MONTHS',
      '48MONTHS',
      '60MONTHS',
      'MORE_THAN_60MONTHS',
    ],
    nullable: true,
  })
  @Index()
  months_back: string;

  @Column({ type: 'text', nullable: true })
  @Index()
  months_back_comment: string;
  // locations for the role
  @Column({ type: 'varchar', length: '255', nullable: true })
  @Index()
  industry: string;
  // relavant titles for the role
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  relevant_titles: string[];

  @Column({ type: 'varchar', length: '255', nullable: true })
  @Index()
  radius_miles: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  attachments: string;

  @Column({ type: 'boolean', nullable: true })
  @Index()
  willing_to_relocate: boolean;

  // open to work
  @Column({ type: 'varchar', length: '255', nullable: true })
  @Index()
  open_to_work: string;

  // essentialQualifications
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  essential_qualifications: string[];

  // essentialRequirements
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  essential_requirements: string[];

  // desireableOrPreferredRequirements
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  desireable_or_preferred_requirements: string[];

  // companiesOfInterest
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  companies_of_interest: string[];

  // majorReasonForRejectedCVs
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  major_reason_for_rejected_cvs: string[];

  // whoDoWeWant
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  who_do_we_want: string[];

  // whoDoWeNotWant
  @Column({ type: 'simple-array', nullable: true })
  @Index()
  who_do_we_not_want: string[];

  @Column({
    type: 'enum',
    enum: [
      'CONTRACT',
      'PERMANENT',
      'TEMPORARY',
      'FREELANCE',
      'PART_TIME',
      'FULL_TIME',
      'INTERNSHIP',
      'APPRENTICESHIP',
      'VOLUNTEER',
      'REMOTE',
      'HYBRID',
      'ON_SITE',
      'OTHER',
    ],
    nullable: true,
  })
  @Index()
  employement_type: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  @Index()
  role_industry: string;

  // manage source if the role is from website or CRM. manage with enums
  @Column({
    type: 'enum',
    enum: ['WEBSITE', 'CRM', 'OTHER'],
    nullable: true,
  })
  @Index()
  source: string;

  @ManyToOne(() => Users, (user) => user.people, { nullable: true })
  user: Users;

  @Column({ nullable: true })
  @Index()
  userId: string;

  @ManyToOne(() => People, (person) => person.roles, { nullable: true })
  person: People;

  @Column({ nullable: true })
  personId: string;

  @ManyToOne(() => Users, (user) => user.acmUser, { nullable: true })
  acmUser: Users;

  @Column({ nullable: true })
  @Index()
  acmUserId: string;

  @ManyToOne(() => Users, (user) => user.bdUser, { nullable: true })
  bdUser: Users;

  @Column({ nullable: true })
  @Index()
  bdUserId: string;

  @ManyToOne(() => Service, (service) => service.roles, { nullable: true })
  service: Service;

  @Column({ nullable: true })
  @Index()
  serviceId: number;

  @OneToMany(() => FocusPoint, (focus_points) => focus_points.role, {
    nullable: true,
  })
  focus_points: FocusPoint[];

  @OneToMany(() => RoleLogs, (roleLogs) => roleLogs.role, {
    nullable: true,
  })
  roleLogs: RoleLogs[];

  @OneToMany(() => RoleCandidate, (role_candidates) => role_candidates.role, {
    nullable: true,
  })
  role_candidates: RoleCandidate[];

  @OneToMany(() => Calendar, (role) => role.role, {
    nullable: true,
  })
  calendars: Calendar[];

  @OneToMany(() => RoleHistory, (roleHistory) => roleHistory.role)
  roleHistory: RoleHistory[];

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
