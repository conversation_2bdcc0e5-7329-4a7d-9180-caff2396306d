import { Injectable } from '@nestjs/common';
import { Sector } from './sector.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class SectorService {
  constructor(
    @InjectRepository(Sector)
    private sectorRepository: Repository<Sector>,
  ) {}

    async createSector(name: string): Promise<void> {
        await this.sectorRepository.insert({
        name,
        });
    }

    async updateSector(id: number, name: string): Promise<Sector> {
        const sector = await this.sectorRepository.findOne({ where: { id } });

        await this.sectorRepository.update(
        { id },
        {
            name,
        },
        );

        return sector;
    }

    async deleteSector(id: number): Promise<void> {
        await this.sectorRepository.delete({ id });
    }

    async findAll(): Promise<Sector[]> {
        return this.sectorRepository.find();
    }

    async findOne(id: number): Promise<Sector> {
        return this.sectorRepository.findOne({ where: { id } });
    }

    async findByName(name: string): Promise<Sector> {
        return this.sectorRepository.findOne({ where: { name } });
    }

    async findByNameAndIdNot(name: string, id: number): Promise<Sector> {
        return this.sectorRepository.findOne({ where: { name, id: id } });
    }
}
