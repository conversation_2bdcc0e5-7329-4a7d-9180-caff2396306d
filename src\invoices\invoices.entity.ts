import { People } from 'src/people/people.entity';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

export enum InvoiceCurrency {
  usd = 'usd',
  gbp = 'gbp',
}

@Entity()
export class Invoices {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  total_roles: number;

  @Column({
    type: 'int',
    nullable: true,
  })
  amount: number;

  @Column({
    type: 'enum',
    enum: [
      'PAID',
      'UNPAID',
      'INVOICED',
      'NO_RESPONSE',
      'ISSUE',
      'CANCELLED',
      'REFUNDED',
      'EXPIRED',
      'DISPUTED',
      'PENDING',
      'FAILED',
      'COMPLETED',
      'PROCESSING',
      'CHARGEBACK',
    ],
    nullable: false,
    default: 'UNPAID',
  })
  status: string;

  @Column({
    type: 'enum',
    enum: InvoiceCurrency,
    nullable: false,
    default: InvoiceCurrency.usd,
  })
  currency: InvoiceCurrency;

  @Column({ nullable: true })
  session_id: string;

  @Column({ nullable: true })
  session_url: string;

  @Column({
    type: 'int',
    nullable: false,
  })
  invoice_number: number;

  @Column({
    type: 'date',
    nullable: false,
  })
  invoice_date: Date;

  @Column({
    type: 'date',
    nullable: false,
  })
  due_date: Date;

  // invoice file if any
  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  invoice_file: string;

  @ManyToOne(() => Users, (user) => user.invoices, {
    onDelete: 'CASCADE',
  })
  user: Users;

  @Column({
    nullable: false,
  })
  userId: string;

  @ManyToOne(() => People, (person) => person.invoices, {
    onDelete: 'CASCADE',
  })
  person: People;

  @Column({
    type: 'int',
    nullable: false,
  })
  personId: number;

  @ManyToOne(() => Service, (service) => service.invoices, {
    onDelete: 'CASCADE',
  })
  service: Service;

  @Column({
    type: 'int',
    nullable: false,
  })
  serviceId: number;
}
