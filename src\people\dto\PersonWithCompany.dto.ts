import { Type } from 'class-transformer';
import { IsEmail, IsString, ValidateNested } from 'class-validator';
import { PeopleDto } from './createPeople.dto';

export class CreatePersonWithCompanyDto {
  @ValidateNested()
  @Type(() => PeopleDto)
  person: PeopleDto;

  @IsString()
  company_name: string;

  @IsString()
  company_link: string;

  @IsEmail()
  email: string;

  @IsString()
  phone_number: string;

  @IsString()
  company_size: string; // format: "10-50"
}
