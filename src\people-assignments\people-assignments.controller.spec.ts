import { Test, TestingModule } from '@nestjs/testing';
import { PeopleAssignmentsController } from './people-assignments.controller';
import { PeopleAssignmentsService } from './people-assignments.service';

describe('PeopleAssignmentsController', () => {
  let controller: PeopleAssignmentsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PeopleAssignmentsController],
      providers: [PeopleAssignmentsService],
    }).compile();

    controller = module.get<PeopleAssignmentsController>(PeopleAssignmentsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
