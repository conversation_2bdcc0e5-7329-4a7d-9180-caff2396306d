import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('email_templates')
export class EmailTemplates {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  subject: string;

  @Column({ nullable: true })
  body: string;

  @Column({
    type: 'enum',
    enum: [
      'CLIENT_WELCOME',
      'JOB_ALERTS',
      'RESUME_TEMPLATES',
      'SKILLS',
      'EXPERIENCE',
      'QUALIFICATIONS',
      'CLIENT_INTRODUCTION_TO_ACM',
      'SEND_CANDIDATE_TO_CLIENT',
      'MARKETING_EMAIL',
      'INVOICE',
      'OTHER',
    ],
    nullable: false,
  })
  type: string;

  @Column({ type: 'enum', enum: ['CLIENT', 'CANDIDATE'], nullable: false })
  sub_type: string;

  @ManyToOne(() => Users, (user) => user.emailTemplates, { nullable: true })
  user: Users;

  @Column({ nullable: true })
  userId: string;

  @OneToMany(
    () => SequenceSteps,
    (sequenceSteps) => sequenceSteps.emailTemplate,
    { nullable: true },
  )
  sequenceSteps: SequenceSteps[];
}
