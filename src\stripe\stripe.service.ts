import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';
import { CreateCheckoutSessionDto } from './dto/stripe.dto';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(private configService: ConfigService) {
    this.stripe = new Stripe(
      this.configService.get<string>('STRIPE_SECRET_KEY'),
      // {
      //   apiVersion: '2024-06-20',
      // },
    );
  }

  // console secrete key

  async createCheckoutSession(data: CreateCheckoutSessionDto) {
    console.log(this.configService.get<string>('STRIPE_SECRET_KEY'));
    console.log(data);
    const checkOutSession = await this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: data.currency.toLowerCase(),
            product_data: {
              name: data.service,
              metadata: {
                prospect: data.prospect,
                invoice_number: data.invoiceNumber,
                invoice_date: data.invoiceDate,
                due_date: data.dueDate,
              },
              description: `Invoice #: ${data.invoiceNumber}   •   Prospect: ${data.prospect}   •   Due: ${data.dueDate}`,
            },
            unit_amount: data.amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: data.successUrl,
      cancel_url: data.cancelUrl,
      metadata: {
        prospect: data.prospect,
        invoice_number: data.invoiceNumber,
        invoice_date: data.invoiceDate,
        due_date: data.dueDate,
      },
    });

    const session = {
      id: checkOutSession.id,
      url: checkOutSession.url,
    };

    return session;
  }

  async createPaymentIntent(
    amount: number,
    currency: string,
  ): Promise<Stripe.PaymentIntent> {
    return this.stripe.paymentIntents.create({
      amount,
      currency,
    });
  }

  async createCustomer(email: string, name: string): Promise<Stripe.Customer> {
    console.log(this.configService.get<string>('STRIPE_SECRET_KEY'));
    return this.stripe.customers.create({
      email,
      name,
    });
  }

  async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    return this.stripe.setupIntents.create({
      customer: customerId,
    });
  }

  async createSubscription(
    customerId: string,
    priceId: string,
  ): Promise<Stripe.Subscription> {
    return this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
    });
  }

  async retrieveSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    return this.stripe.subscriptions.retrieve(subscriptionId);
  }

  async cancelSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    return this.stripe.subscriptions.cancel(subscriptionId);
  }
}
