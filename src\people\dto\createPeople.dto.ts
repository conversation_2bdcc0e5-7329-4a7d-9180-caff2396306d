import { ApiProperty } from '@nestjs/swagger';
import {
  PersonSource,
  PersonStatusType,
  PersonType,
  ProspectStatus,
  SubscriptionType,
} from './people.enums';
import { IsOptional } from 'class-validator';

export class PeopleDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  first_name: string;

  @ApiProperty()
  last_name: string;

  @ApiProperty()
  full_name: string;

  @ApiProperty()
  current_title: string;

  @ApiProperty({ required: false })
  profile_img?: string;

  @ApiProperty({ required: false })
  headline?: string;

  @ApiProperty({ required: false })
  profile_url?: string;

  @ApiProperty({ required: false })
  location?: string;

  @ApiProperty({ required: false })
  SR_specied_industry?: string;

  @ApiProperty({ required: false })
  summary?: string;

  @ApiProperty({ enum: PersonType, default: PersonType.OTHER })
  person_type: PersonType;

  @ApiProperty()
  is_hiring: boolean;

  @ApiProperty({ enum: PersonSource, default: PersonSource.OTHER })
  profile_source: PersonSource;

  @ApiProperty({ required: false })
  profile_source_link?: string;

  @ApiProperty({ required: false })
  cv_text?: string;

  @ApiProperty({ required: false })
  client_number?: string;

  @ApiProperty({ enum: SubscriptionType, default: SubscriptionType.FREE })
  subscription_type: SubscriptionType;

  @ApiProperty({ required: false })
  subscription_start_date?: Date;

  @ApiProperty({ required: false })
  subscription_end_date?: Date;

  @ApiProperty({ required: false })
  reminder_date?: Date;

  @ApiProperty({ required: false })
  amount_paid?: number;

  @ApiProperty({ required: false })
  payment_date?: Date;

  @ApiProperty({ required: false })
  credits?: number;

  @ApiProperty({ required: false })
  credits_per_day?: number;

  @ApiProperty({ required: false })
  credits_used?: number;

  @ApiProperty({ enum: PersonStatusType, default: PersonStatusType.ACTIVE })
  client_status: PersonStatusType;

  @ApiProperty({ enum: ProspectStatus, required: false })
  prospect_status?: ProspectStatus;

  @ApiProperty({ required: false })
  prospect_status_date?: Date;

  @ApiProperty({ required: false })
  prospect_status_comment?: string;

  @ApiProperty({ required: false })
  companyId?: number;

  @ApiProperty({ required: false })
  countryId?: number;

  @ApiProperty({ required: false })
  sectorId?: number;

  @ApiProperty({ required: false })
  serviceId?: number;

  @ApiProperty({
    required: false,
    example:
      'Only needed from scrappers getting linkedin profiles data. Not needed in CRM or Website. Just leave it empty if you are on CRM or Website.',
  })
  role_candidate_id?: number; // role candidate id from the role management system

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  @ApiProperty({ required: false })
  userId?: string;

  @ApiProperty({ required: false })
  acmUserId?: string;

  @ApiProperty({ required: false })
  bdUserId?: string;

  // add personskills, qualifications, experience

  @ApiProperty({ required: false })
  skills?: any[];

  @ApiProperty({ required: false })
  languages?: any[];
  @ApiProperty({ required: false })
  certifications?: any[];

  @ApiProperty({ required: false })
  qualifications?: any[];

  @ApiProperty({ required: false })
  experience?: any[];

  @ApiProperty({ required: false })
  emails?: any[];

  @ApiProperty({ required: false })
  phones?: any[];

  @ApiProperty({ required: false })
  roles?: any[];

  @ApiProperty({ required: false })
  job_applications?: any[];

  @ApiProperty({ required: false })
  job_alerts?: any[];

  @ApiProperty({ required: false })
  projects?: any[];
}

export class GetAllPersonsDto {
  @IsOptional()
  page: string;
  @IsOptional()
  pageSize: string;
  @IsOptional()
  search: string;
  @IsOptional()
  country_id: string;
  @IsOptional()
  sector_id: string;
  @IsOptional()
  findHiringPersons: string;
  @IsOptional()
  findWithEmails: string;
  @IsOptional()
  findWithoutEmails: string;
  @IsOptional()
  selectedCountry: string;
  @IsOptional()
  selectedSector: string;
  @IsOptional()
  industries: string[];
  @IsOptional()
  startDate: string;
  @IsOptional()
  endDate: string;
}
