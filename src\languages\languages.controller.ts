import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { LanguagesService } from './languages.service';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { LanguageDto, UpdateLanguagesDto } from './dto/languages.dto';

@ApiTags('Languages')
@Controller('languages')
export class LanguagesController {
  constructor(private readonly languagesService: LanguagesService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new language' })
  @ApiBody({ type: LanguageDto })
  async createLanguage(@Body() language: LanguageDto) {
    return this.languagesService.createLanguage(language);
  }

  @Put('update')
  @ApiOperation({ summary: 'Update a language' })
  @ApiBody({ type: UpdateLanguagesDto })
  async updateLanguage(
    @Body() language: UpdateLanguagesDto,
  ) {
    return this.languagesService.updateLanguage(language);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a language' })
  async deleteLanguage(@Param('id') id: number) {
    return this.languagesService.deleteLanguage(id);
  }

  @Get('list')
  @ApiOperation({ summary: 'List all languages' })
  async listLanguages() {
    return this.languagesService.findAllLanguages();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a language by ID' })
  async getLanguageById(@Param('id') id: number) {
    return this.languagesService.findLanguageById(id);
  }
}
