import { Calendar } from 'src/calendar/calendar.entity';
import { Company } from 'src/company/company.entity';
import { EmailTemplates } from 'src/email-templates/emailTemplates.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { FocusPoint } from 'src/focus-point/focusPoint.entity';
import { Invoices } from 'src/invoices/invoices.entity';
import { JobApplications } from 'src/job_applications/job_applications.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { People } from 'src/people/people.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { RoleLogs } from 'src/role_logs/role_logs.entity';
import { Roles } from 'src/roles/roles.entity';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { FileManager } from 'src/file-manager/file-manager.entity';
import {
  Enti<PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Users {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    default: null,
    nullable: true,
  })
  full_name: string;

  @Column({
    default: null,
    nullable: true,
  })
  first_name: string;

  @Column({
    default: null,
    nullable: true,
  })
  last_name: string;

  @Column({
    default: ' ',
    nullable: false,
  })
  username: string;

  @Column({
    default: null,
    nullable: true,
    type: 'enum',
    enum: ['ONSITE', 'REMOTE', 'HYBRID'],
  })
  working_mode: string;

  // define cnic in XXXXX-XXXXXXX-X format
  @Column({
    unique: true,
    default: null,
    nullable: true,
  })
  cnic: string;

  @Column({
    unique: true,
    default: null,
    nullable: false,
  })
  email: string;

  @Column({
    default: null,
    nullable: true,
  })
  birth_date: Date;

  @Column({
    default: null,
    nullable: true,
  })
  country_code: string;

  @Column({
    default: null,
    nullable: true,
  })
  phone_number: string;

  @Column({
    default: null,
    nullable: true,
  })
  display_phone_number: string;

  @Column({ nullable: true })
  profile_picture: string;

  @Column({
    default: ' ',
    nullable: false,
  })
  password_original: string;

  @Column({
    default: ' ',
    nullable: false,
  })
  password_salt: string;

  @Column({
    default: ' ',
    nullable: false,
  })
  password_hash: string;

  @Column({
    default: null,
    nullable: true,
  })
  password_reset_token: string;

  @Column({
    default: null,
    nullable: true,
  })
  password_reset_expires: Date;

  @Column({
    default: null,
    nullable: true,
  })
  email_verification_token: string;

  @Column({
    default: null,
    nullable: true,
  })
  email_verified: boolean;

  @Column({
    default: null,
    nullable: true,
  })
  email_verification_expires: Date;

  @Column({
    default: null,
    nullable: true,
  })
  phone_verification_token: string;

  @Column({
    default: null,
    nullable: true,
  })
  phone_verified: boolean;

  @Column({
    default: null,
    nullable: true,
  })
  phone_verification_expires: Date;

  @Column({
    default: null,
    nullable: true,
  })
  last_login: Date;

  @Column({
    default: null,
    nullable: true,
  })
  last_login_ip: string;

  @Column({
    type: 'enum',
    enum: [
      'ACTIVE',
      'INACTIVE',
      'BLOCKED',
      'DELETED',
      'PENDING',
      'SUSPENDED',
      'REJECTED',
      'APPROVED',
      'UNAPPROVED',
      'UNVERIFIED',
      'VERIFIED',
    ],
    default: 'ACTIVE',
    nullable: false,
  })
  status: string;

  @Column({
    type: 'enum',
    enum: ['USER', 'ADMIN', 'SUPER_ADMIN'],
    default: null,
    nullable: true,
  })
  role: string;

  @Column({
    type: 'enum',
    enum: [
      'RPO',
      'RECRUITER_WEB',
      'EMPLOYER',
      'CANDIDATE',
      'LEADEXPERT',
      'PROJECT_HEAD',
      'PROJECT_MANAGER',
      'PROJECT_MEMBER',
      'BUSINESS_DEVELOPMENT_EXECUTIVE',
      'BUSINESS_DEVELOPMENT_MANAGER',
      'ACCOUNT_MANAGER',
      'ACCOUNT_EXECUTIVE',
      'ACCOUNTANT',
      'FINANCE_MANAGER',
      'FINANCE_EXECUTIVE',
      'HR_MANAGER',
      'HR_EXECUTIVE',
      'HR_RECRUITER',
      'RECRUITER_CRM',
      'RESOURCER_CRM',
      'PRE_QUALIFICATION',
    ],
    default: null,
    nullable: true,
  })
  designation: string;

  @Column({
    default: null,
    nullable: true,
  })
  verification_code: string;

  @Column({
    default: null,
    nullable: true,
  })
  verification_code_expires: Date;

  @Column({
    default: false,
    nullable: false,
  })
  verification_code_used: boolean;

  @Column({
    default: null,
    nullable: true,
  })
  verification_code_used_at: Date;

  @OneToMany(() => Company, (company) => company.user)
  companies: Company[];

  @OneToMany(() => Jobs, (jobs) => jobs.user)
  jobs: Jobs[];

  @OneToMany(() => People, (people) => people.user)
  people: People[];

  @OneToMany(() => PersonEmail, (personEmail) => personEmail.user)
  emails: PersonEmail[];

  @OneToMany(() => People, (people) => people.acmUser)
  acmUser: People[];

  @OneToMany(() => People, (people) => people.bdUser)
  bdUser: People[];

  @OneToMany(() => PeopleAssignment, (peopleAssignment) => peopleAssignment.leadUser)
  leadUser: PeopleAssignment[];

  @OneToMany(() => Roles, (roles) => roles.user)
  roles: Roles[];

  @OneToMany(() => Invoices, (invoices) => invoices.user)
  invoices: Invoices[];

  @OneToMany(() => Calendar, (calendar) => calendar.user)
  calendar: Calendar[];

  @OneToMany(() => EmailTemplates, (emailTemplates) => emailTemplates.user)
  emailTemplates: EmailTemplates[];

  @OneToMany(() => FocusPoint, (focus_points) => focus_points.user)
  focus_points: FocusPoint[];

  @OneToMany(() => RoleLogs, (roleLogs) => roleLogs.user)
  roleLogs: RoleLogs[];

  @OneToMany(() => RoleSequence, (roleSequence) => roleSequence.user)
  roleSequence: RoleSequence[];

  @OneToMany(() => JobApplications, (job_applications) => job_applications.user)
  job_applications: JobApplications[];

  @OneToMany(() => RoleCandidate, (role_candidates) => role_candidates.user, {
    cascade: true,
  })
  role_candidates: RoleCandidate[]; // OneToMany relationship with RoleCvs entity

  @OneToMany(() => MailBox, (mailBoxes) => mailBoxes.assignee, {
    cascade: true,
  })
  mailBoxes: MailBox[]; // OneToMany relationship with RoleCvs entity

  @OneToMany(() => FileManager, (fileManager) => fileManager.user, {
    cascade: true,
  })
  fileManagerItems: FileManager[]; // OneToMany relationship with FileManager entity

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
