import {
  IsNotEmpty,
  IsNumber,
  IsString,
  IsOptional,
  IsArray,
  IsEmail,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCompanyDto {
  @ApiProperty({
    description: 'The public id of a company',
    example: 'ultimate-outsourcing-services',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  public_id: string;

  @ApiProperty({
    description: 'The company ID',
    type: String,
    example: '3218264',
    required: false,
  })
  @IsString()
  @IsOptional()
  company_id: string;

  @ApiProperty({
    description: 'The name of the company',
    type: String,
    example: 'Ultimate Outsourcing LTD',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'The profile URL of the company',
    type: String,
    example: 'https://www.linkedin.com/company/ultimate-outsourcing-services/',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  profile_url: string;

  @ApiProperty({
    description: 'The encoded profile URL of the company',
    type: String,
    required: false,
    example: 'https://www.linkedin.com/company/3218264/',
  })
  @IsString()
  @IsOptional()
  profile_url_encoded: string;

  @ApiProperty({
    description: 'The company logo URL',
    type: String,
    required: false,
    example:
      'https://media.licdn.com/dms/image/v2/D560BAQF-7bKrR_0aFA/company-logo_200_200/company-logo_200_200/0/1729525068868/ultimate_outsourcing_services_logo?e=1749686400&v=beta&t=9Z97_vGML89cFZ2nQaWjJ-mbIVApyivVgoMl-BjRWBY',
  })
  @IsString()
  @IsOptional()
  logo?: string;

  @ApiProperty({
    description: 'The cover photo URL of the company',
    type: String,
    required: false,
    example:
      'https://media.licdn.com/dms/image/v2/D560BAQF-7bKrR_0aFA/company-logo_200_200/company-logo_200_200/0/1729525068868/ultimate_outsourcing_services_logo?e=1749686400&v=beta&t=9Z97_vGML89cFZ2nQaWjJ-mbIVApyivVgoMl-BjRWBY',
  })
  @IsString()
  @IsOptional()
  cover_photo?: string;

  @ApiProperty({
    description: 'The company website URL',
    type: String,
    required: false,
    example: 'https://ultimateoutsourcing.co.uk/',
  })
  @IsString()
  @IsOptional()
  website?: string;

  @ApiProperty({
    description: 'The company tagline',
    type: String,
    required: false,
    example: 'All Your Recruitment Worries Outsourced',
  })
  @IsString()
  @IsOptional()
  tagline?: string;

  @ApiProperty({
    description: 'The company address',
    type: String,
    required: false,
    example: 'London',
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: 'Total number of staff in the company',
    type: Number,
    required: false,
    example: '11',
  })
  @IsNumber()
  @IsOptional()
  staff_count?: number;

  @ApiProperty({
    description: 'Staff count range start',
    type: Number,
    required: false,
    example: 11,
  })
  @IsNumber()
  @IsOptional()
  staff_count_range_start?: number;

  @ApiProperty({
    description: 'Staff count range end',
    type: Number,
    required: false,
    example: 50,
  })
  @IsNumber()
  @IsOptional()
  staff_count_range_end?: number;

  @ApiProperty({
    description: 'Number of followers the company has',
    type: Number,
    required: false,
    example: 100,
  })
  @IsNumber()
  @IsOptional()
  followers_count?: number;

  @ApiProperty({
    description: 'Detailed description of the company',
    type: String,
    required: false,
    example: 'Company about info',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Year the company was founded',
    type: String,
    required: false,
    example: '2014',
  })
  @IsString()
  @IsOptional()
  founded?: string;

  @ApiProperty({
    description: 'The industry the company belongs to',
    type: String,
    required: false,
    example: 'Staffing and Recruiting',
  })
  @IsString()
  @IsOptional()
  industry?: string;

  @ApiProperty({
    description: 'The country of company headquarter',
    type: String,
    required: false,
    example: 'UK',
  })
  @IsString()
  @IsOptional()
  headquarter_country?: string;

  @ApiProperty({
    description: 'The city of company headquarter',
    type: String,
    required: false,
    example: 'London',
  })
  @IsString()
  @IsOptional()
  headquarter_city?: string;

  @ApiProperty({
    description: 'The geographic area of company headquarter',
    type: String,
    required: false,
    example: 'Street 1',
  })
  @IsString()
  @IsOptional()
  headquarter_geographic_area?: string;

  @ApiProperty({
    description: 'The line 1 address of company headquarter',
    type: String,
    required: false,
    example: 'Here will be line 1 address',
  })
  @IsString()
  @IsOptional()
  headquarter_line1?: string;

  @ApiProperty({
    description: 'The line 2 address of company headquarter',
    type: String,
    required: false,
    example: 'Here will be line 2 address',
  })
  @IsString()
  @IsOptional()
  headquarter_line2?: string;

  @ApiProperty({
    description: 'The postal code of company headquarter',
    type: String,
    required: false,
    example: 'SW1A 1AA',
  })
  @IsString()
  @IsOptional()
  headquarter_postal_code?: string;

  @ApiProperty({
    description: 'Specialities of the company (comma-separated values)',
    type: [String],
    required: false,
    example: [
      'Recruitment, Candidate Sourcing',
      'CV Sourcing',
      'Pre-Qualification',
      '360 Recruitment',
      'Dedicated Resourcer',
      'head hunter',
    ],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  specialities?: string[];

  @ApiProperty({
    description: 'Employee benefits (comma-separated values)',
    type: [String],
    required: false,
    example: ['Health Insurance', 'Paid Time Off', 'Flexible Working Hours'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  employee_benefits?: string[];

  @ApiProperty({
    description: 'Company official email',
    type: String,
    required: false,
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  company_email?: string;

  @ApiProperty({
    description: 'Company phone number',
    type: String,
    required: false,
    example: '+44 125 12446412',
  })
  @IsString()
  @IsOptional()
  company_phone?: string;

  @ApiProperty({
    description: 'Region where the company is located',
    type: String,
    required: false,
    example: 'United Kingdom',
  })
  @IsString()
  @IsOptional()
  region?: string;

  @ApiProperty({
    description: 'Scraper level for data collection priority',
    type: Number,
    required: false,
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  scrapper_level?: number;

  @ApiProperty({
    description: 'The status of scrapper',
    type: Boolean,
    required: false,
    example: 'true',
  })
  @IsBoolean()
  @IsOptional()
  is_scrapped_fully?: boolean;

  @ApiProperty({
    description: 'The user who owns the company',
    type: String,
    required: false,
    example: '',
  })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'The sector the company belongs to',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  sectorId?: number;

  @ApiProperty({
    description: 'The country the company is based in',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  countryId?: number;

  @ApiProperty({
    description: 'The primary people associated with the company',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  peopleId?: number;

  @ApiProperty({
    description: 'The jobs associated with the company',
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  jobsId?: number;
}

export class AddCompanyWithLinksDto {
  @ApiProperty({
    description: 'Links associated with the company',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  links: string[];
}
