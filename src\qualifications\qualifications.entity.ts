import { People } from 'src/people/people.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity('qualifications')
export class Qualifications {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  title: string;

  @Column({
    type: 'date',
    nullable: true,
  })
  start_date: Date;

  @Column({
    type: 'date',
    nullable: true,
  })
  end_date: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  institution: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  duration: string;

  @Column({
    type: 'enum',
    enum: ['CERTIFICATE', 'DIPLOMA', 'DEGREE'],
    default: 'DEGREE',
    nullable: true,
  })
  qualification_type: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  description: string;

  @Column({
    type: 'boolean',
    default: false,
    nullable: true,
  })
  is_verified: boolean;

  @Column({
    type: 'boolean',
    default: false,
    nullable: true,
  })
  is_current: boolean;

  @Column({
    type: 'date',
    nullable: true,
  })
  date_expiry: Date;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  issued_by: string;

  @ManyToOne(() => People, (person) => person.qualifications, {
    onDelete: 'CASCADE',
  })
  person: People;

  @Column({
    type: 'int',
    nullable: false,
  })
  personId: number;
}
