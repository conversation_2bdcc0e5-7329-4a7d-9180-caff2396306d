import { Module } from '@nestjs/common';
import { QualificationsService } from './qualifications.service';
import { QualificationsController } from './qualifications.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Qualifications } from './qualifications.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Qualifications])],
  providers: [QualificationsService],
  controllers: [QualificationsController],
})
export class QualificationsModule {}
