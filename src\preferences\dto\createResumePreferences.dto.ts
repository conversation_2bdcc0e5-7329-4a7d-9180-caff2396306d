import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';

export class JobPreferencesDTO {
  @ApiProperty({
    example: 'Software Engineer',
    description: 'Preferred job title',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiProperty({
    example: 'New York, USA',
    description: 'Preferred job location',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location?: string;

  @ApiProperty({
    example: 'Full-Time',
    description: 'Preferred job type',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_type?: string;

  @ApiProperty({
    example: 'IT',
    description: 'Preferred industry',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_industry?: string;

  @ApiProperty({
    example: '3-5 years',
    description: 'Preferred job experience',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_experience?: string;

  @ApiProperty({
    example: '$80,000',
    description: 'Preferred job salary',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_salary?: string;

  @ApiProperty({
    example: 'Java, React, AWS',
    description: 'Preferred job skills (comma-separated)',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_skills?: string;

  @ApiProperty({
    example: 60000,
    description: 'Salary range start',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  salary_range_start?: number;

  @ApiProperty({
    example: 100000,
    description: 'Salary range end',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  salary_range_end?: number;

  @ApiProperty({
    example: 'USD',
    description: 'Salary currency',
    required: false,
  })
  @IsOptional()
  @IsString()
  salary_currency?: string;

  @ApiProperty({
    example: 'ANNUALLY',
    description: 'Salary period',
    enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'ANNUALLY'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['DAILY', 'WEEKLY', 'MONTHLY', 'ANNUALLY'])
  salary_period?: string;

  @ApiProperty({
    example: '10001',
    description: 'Job location ZIP code',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location_zip?: string;

  @ApiProperty({
    example: 'USA',
    description: 'Job location country',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location_country?: string;

  @ApiProperty({
    example: 'New York',
    description: 'Job location city',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location_city?: string;

  @ApiProperty({
    example: 'NY',
    description: 'Job location state',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location_state?: string;

  @ApiProperty({
    example: '1234 Elm Street',
    description: 'Job location address',
    required: false,
  })
  @IsOptional()
  @IsString()
  job_location_address?: string;

  @ApiProperty({
    example: 'IMMEDIATE',
    description: 'Availability',
    required: false,
  })
  @IsOptional()
  availability?: string;

  @ApiProperty({
    example: 'RELOCATE',
    description: 'Reason for leaving',
    required: false,
  })
  @IsOptional()
  reason_for_leaving?: string;
}
