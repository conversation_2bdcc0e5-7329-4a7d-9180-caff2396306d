import { Type } from 'class-transformer';
import { IsOptional, IsString, IsBoolean, IsNumber } from 'class-validator';

export class GetAssignedPersonsDto {
  @IsString()
  userId: string;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  havingNoContactInfo?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  needReplacement?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  noEmailFound?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  editEmailInfo?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  findHiringPersons?: boolean;

  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  findBounceBacks?: boolean;

  @IsOptional()
  @IsString()
  searchString?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  pageSize?: number;
}

export class AssignPersonsToUserDto {
  @IsOptional()
  userId?: string;

  @IsOptional()
  page?: string;

  @IsOptional()
  pageSize?: string;
}
