import { Modu<PERSON> } from '@nestjs/common';
import { EmailService } from './email.service';
import { EmailController } from './email.controller';
import { S3bucketModule } from 'src/s3bucket/s3bucket.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailBox } from 'src/mail-box/mailBox.entity';

@Module({
  imports: [S3bucketModule,
    TypeOrmModule.forFeature([MailBox])
  ],
  providers: [EmailService],
  controllers: [EmailController],
  exports: [EmailService],
})
export class EmailModule {}
