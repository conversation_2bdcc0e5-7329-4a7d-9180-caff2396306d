import { Column, PrimaryGeneratedColumn } from 'typeorm';

export class RoleSequence {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({
    type: 'enum',
    enum: ['EMAIL', 'SMS', 'WHATSAPP', 'CALL', 'CONNECTION_REQUEST', 'INMAIL'],
    default: 'active',
  })
  channel: string;

  @Column({
    type: 'enum',
    enum: ['OUTREACH', 'FOLLOW_UP', 'CLOSURE', 'RESPONSE'],
    nullable: true,
  })
  communication_type: string;

  @Column({ type: 'int', nullable: true })
  sequence_order: number;

  @Column({ type: 'varchar', nullable: true })
  subject: string;

  @Column({ type: 'text', nullable: true })
  body: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  // id
}
