import { Body, Controller, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { SkillsService } from './skills.service';
import { PersonSkillDto } from './dto/person-skill.dto';
import { UpdatepersonSkillsDto } from './dto/updatePersonSkills.dto';

@ApiTags('skills')
@Controller('skills')
export class SkillsController {
  constructor(private readonly skillsService: SkillsService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new skill' })
  @ApiBody({ type: PersonSkillDto })
  async createSkill(@Body() skill: PersonSkillDto) {
    return this.skillsService.createSkill(skill);
  }

  @Put('update')
  @ApiOperation({ summary: 'Update an existing skill' })
  @ApiBody({ type: UpdatepersonSkillsDto })
  async updateSkill(@Body() skill: UpdatepersonSkillsDto) {
    return this.skillsService.updateSkill(skill);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a skill' })
  async deleteSkill(@Param('id') id: number) {
    return this.skillsService.deleteSkill(id);
  }

  @Get('list')
  @ApiOperation({ summary: 'List all skills' })
  async listSkills() {
    return this.skillsService.getAllSkills();
  }
}
