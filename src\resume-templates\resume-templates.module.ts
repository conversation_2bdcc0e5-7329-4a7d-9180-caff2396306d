import { Module } from '@nestjs/common';
import { ResumeTemplateController } from './resume-templates.controller';
import { ResumeTemplateService } from './resume-templates.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ResumeTemplate } from './resume-template.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ResumeTemplate])],
  providers: [ResumeTemplateService],
  controllers: [ResumeTemplateController],
})
export class ResumeTemplatesModule {}
