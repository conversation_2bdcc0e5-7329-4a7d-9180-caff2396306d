import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  HttpStatus,
  HttpException,
  ParseU<PERSON><PERSON>ipe,
  Patch
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { WebsiteManualRequestsService } from './website_manual_requests.service';
import { CreateWebsiteManualRequestDto } from './dto/create-website-manual-request.dto';
import { WebsiteManualRequestResponseDto } from './dto/website-manual-request-response.dto';
import { WebsiteManualRequest } from './website_manual_requests.entity';

@ApiTags('Website Manual Requests')
@Controller('website-manual-requests')
export class WebsiteManualRequestsController {
  constructor(
    private readonly websiteManualRequestsService: WebsiteManualRequestsService,
  ) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new website manual request',
    description: 'Creates a new manual request and automatically sends an email with file attachments if URLs are provided'
  })
  @ApiBody({ type: CreateWebsiteManualRequestDto })
  @ApiResponse({
    status: 201,
    description: 'Manual request created successfully',
    type: WebsiteManualRequestResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async createManualRequest(
    @Body() createDto: CreateWebsiteManualRequestDto,
  ): Promise<WebsiteManualRequest> {
    try {
      return await this.websiteManualRequestsService.createManualRequest(createDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create manual request',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all website manual requests',
    description: 'Retrieves all manual requests ordered by creation date (newest first)'
  })
  @ApiResponse({
    status: 200,
    description: 'Manual requests retrieved successfully',
    type: [WebsiteManualRequestResponseDto],
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAllManualRequests(): Promise<WebsiteManualRequest[]> {
    try {
      return await this.websiteManualRequestsService.getAllManualRequests();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch manual requests',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a website manual request by ID',
    description: 'Retrieves a specific manual request by its UUID'
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the manual request',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Manual request retrieved successfully',
    type: WebsiteManualRequestResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Manual request not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getManualRequestById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<WebsiteManualRequest> {
    try {
      return await this.websiteManualRequestsService.getManualRequestById(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch manual request',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id/resend-email')
  @ApiOperation({
    summary: 'Resend email for a manual request',
    description: 'Resends the email with file attachments for a specific manual request'
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the manual request',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Email resent successfully',
    type: WebsiteManualRequestResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Manual request not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to resend email',
  })
  async resendEmail(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<WebsiteManualRequest> {
    try {
      return await this.websiteManualRequestsService.resendEmail(id);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to resend email',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('failed/emails')
  @ApiOperation({
    summary: 'Get manual requests with failed emails',
    description: 'Retrieves all manual requests where email sending failed'
  })
  @ApiResponse({
    status: 200,
    description: 'Failed email requests retrieved successfully',
    type: [WebsiteManualRequestResponseDto],
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getFailedEmailRequests(): Promise<WebsiteManualRequest[]> {
    try {
      return await this.websiteManualRequestsService.getFailedEmailRequests();
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch failed email requests',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
