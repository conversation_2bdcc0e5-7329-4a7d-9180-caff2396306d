import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { QualificationsService } from './qualifications.service';
import { QualificationsDto } from './dto/qualifications.dto';

@ApiTags('Qualifications')
@Controller('qualifications')
export class QualificationsController {
  constructor(private readonly qualificationsService: QualificationsService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a qualification' })
  async create(@Body() qualifications: QualificationsDto): Promise<any> {
    return this.qualificationsService.create(qualifications);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all qualifications' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', required: false, type: Number })
  @ApiQuery({ name: 'searchString', required: false, type: String })
  async getAll(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('searchString') searchString: string,
  ): Promise<any> {
    return this.qualificationsService.findAll(page, pageSize, searchString);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a qualification by ID' })
  async getOne(@Param('id') id: number): Promise<any> {
    return this.qualificationsService.findOne(id);
  }
  @Put(':id')
  @ApiOperation({ summary: 'Update a qualification by ID' })
  async update(
    @Param('id') id: number,
    @Body() qualifications: QualificationsDto,
  ): Promise<any> {
    return this.qualificationsService.update(id, qualifications);
  }
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a qualification by ID' })
  async delete(@Param('id') id: number): Promise<any> {
    return this.qualificationsService.remove(id);
  }
}
