import {
    Column,
    <PERSON>tity,
    ManyToOne,
    PrimaryGeneratedColumn,
  } from 'typeorm';
  import { People } from 'src/people/people.entity';
  
  @Entity()
  export class PersonPhone {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({
      unique: true,
      nullable: false,
      type: 'varchar',
      length: 25,
      default: null,
    })
    phone_number: string;

    @Column({
      type: 'enum',
      enum: ['PERSONAL', 'BUSINESS', 'OTHER'],
      default: 'PERSONAL',
    })
    phone_type: string;

    @ManyToOne(() => People, (person) => person.phones, { onDelete: 'CASCADE' })
    person: People;

    @Column({ nullable: true })
    personId: number;
  }