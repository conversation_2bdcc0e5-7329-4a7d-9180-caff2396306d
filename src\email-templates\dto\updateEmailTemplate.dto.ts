import { ApiProperty, PartialType } from '@nestjs/swagger';
import { EmailTemplatesDto } from './emailTemplates.dto';
import { IsUUID } from 'class-validator';

export class UpdateEmailTemplateDto extends PartialType(EmailTemplatesDto) {
  @ApiProperty({
    description: 'Unique identifier for the email template',
    type: String,
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
    required: true,
  })
  @IsUUID()
  id: string;
}
