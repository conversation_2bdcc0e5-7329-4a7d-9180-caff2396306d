import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

import { CreateUserDto } from './create-user.dto';
import { LoginDto } from './login.dto';
import { VerifyDto } from './verify.dto';
import { UpdateUserDto } from './updateUser.dto';
import { ForgetPasswordDto } from './forgetPassword.dto';
import { ResetPasswordTokenDto } from './resetPasswordToken.dto';

describe('User DTOs', () => {
  describe('CreateUserDto', () => {
    it('should validate a valid CreateUserDto', async () => {
      const validDto = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        username: 'johndo<PERSON>',
        source: 'CRM',
        role: 'USER',
        designation: 'RECRUITER',
        profile_picture: 'https://example.com/profile.jpg',
        status: 'ACTIVE',
      };

      const dto = plainToClass(CreateUserDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should fail validation for invalid email', async () => {
      const invalidDto = {
        email: 'invalid-email',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        source: 'CRM',
        profile_picture: 'https://example.com/profile.jpg',
      };

      const dto = plainToClass(CreateUserDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'email')).toBe(true);
    });

    it('should fail validation for missing required fields', async () => {
      const incompleteDto = {
        email: '<EMAIL>',
        // Missing password, first_name, etc.
      };

      const dto = plainToClass(CreateUserDto, incompleteDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate enum values correctly', async () => {
      const dtoWithInvalidEnum = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        source: 'CRM',
        role: 'INVALID_ROLE',
        designation: 'INVALID_DESIGNATION',
        working_mode: 'INVALID_MODE',
        status: 'INVALID_STATUS',
        profile_picture: 'https://example.com/profile.jpg',
      };

      const dto = plainToClass(CreateUserDto, dtoWithInvalidEnum);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate optional fields correctly', async () => {
      const dtoWithOptionalFields = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        source: 'CRM',
        profile_picture: 'https://example.com/profile.jpg',
        cnic: '12345-1234567-1',
        phone_number: '+1234567890',
        birth_date: '1990-01-01',
      };

      const dto = plainToClass(CreateUserDto, dtoWithOptionalFields);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });
  });

  describe('LoginDto', () => {
    it('should validate a valid LoginDto', async () => {
      const validDto = {
        email: '<EMAIL>',
        password: 'password123',
        source: 'crm',
        designation: 'RECRUITER',
      };

      const dto = plainToClass(LoginDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should fail validation for invalid email', async () => {
      const invalidDto = {
        email: 'invalid-email',
        password: 'password123',
        source: 'crm',
        designation: 'RECRUITER',
      };

      const dto = plainToClass(LoginDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'email')).toBe(true);
    });

    it('should fail validation for missing required fields', async () => {
      const incompleteDto = {
        email: '<EMAIL>',
        // Missing password
      };

      const dto = plainToClass(LoginDto, incompleteDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('VerifyDto', () => {
    it('should validate a valid VerifyDto', async () => {
      const validDto = {
        email: '<EMAIL>',
        verification_code: '123456',
      };

      const dto = plainToClass(VerifyDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should fail validation for invalid email', async () => {
      const invalidDto = {
        email: 'invalid-email',
        verification_code: '123456',
      };

      const dto = plainToClass(VerifyDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'email')).toBe(true);
    });

    it('should fail validation for missing verification code', async () => {
      const incompleteDto = {
        email: '<EMAIL>',
        // Missing verification_code
      };

      const dto = plainToClass(VerifyDto, incompleteDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('UpdateUserDto', () => {
    it('should validate a valid UpdateUserDto', async () => {
      const validDto = {
        first_name: 'Updated',
        last_name: 'Name',
        phone_number: '+1234567890',
        birth_date: '1990-01-01',
      };

      const dto = plainToClass(UpdateUserDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should allow partial updates', async () => {
      const partialDto = {
        first_name: 'Updated',
        // Only updating first name
      };

      const dto = plainToClass(UpdateUserDto, partialDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should validate email format if provided', async () => {
      const dtoWithInvalidEmail = {
        email: 'invalid-email',
        first_name: 'Updated',
      };

      const dto = plainToClass(UpdateUserDto, dtoWithInvalidEmail);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'email')).toBe(true);
    });
  });

  describe('ForgetPasswordDto', () => {
    it('should validate a valid ForgetPasswordDto', async () => {
      const validDto = {
        email: '<EMAIL>',
      };

      const dto = plainToClass(ForgetPasswordDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should fail validation for invalid email', async () => {
      const invalidDto = {
        email: 'invalid-email',
      };

      const dto = plainToClass(ForgetPasswordDto, invalidDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'email')).toBe(true);
    });

    it('should fail validation for missing email', async () => {
      const emptyDto = {};

      const dto = plainToClass(ForgetPasswordDto, emptyDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('ResetPasswordTokenDto', () => {
    it('should validate a valid ResetPasswordTokenDto', async () => {
      const validDto = {
        token: 'reset-token-123',
        newPassword: 'newpassword123',
      };

      const dto = plainToClass(ResetPasswordTokenDto, validDto);
      const errors = await validate(dto);

      expect(errors.length).toBe(0);
    });

    it('should fail validation for missing token', async () => {
      const incompleteDto = {
        newPassword: 'newpassword123',
        // Missing token
      };

      const dto = plainToClass(ResetPasswordTokenDto, incompleteDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });

    it('should fail validation for missing password', async () => {
      const incompleteDto = {
        token: 'reset-token-123',
        // Missing newPassword
      };

      const dto = plainToClass(ResetPasswordTokenDto, incompleteDto);
      const errors = await validate(dto);

      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate password strength if configured', async () => {
      const dtoWithWeakPassword = {
        token: 'reset-token-123',
        newPassword: '123', // Weak password
      };

      const dto = plainToClass(ResetPasswordTokenDto, dtoWithWeakPassword);
      const errors = await validate(dto);

      // This would depend on password validation rules in the DTO
      expect(dto).toBeDefined();
    });
  });

  describe('DTO Transformation', () => {
    it('should transform plain objects to DTO instances', () => {
      const plainObject = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        source: 'CRM',
        profile_picture: 'https://example.com/profile.jpg',
      };

      const dto = plainToClass(CreateUserDto, plainObject);

      expect(dto).toBeInstanceOf(CreateUserDto);
      expect(dto.email).toBe('<EMAIL>');
      expect(dto.first_name).toBe('John');
    });

    it('should handle nested objects and arrays', () => {
      const complexObject = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        username: 'johndoe',
        source: 'CRM',
        profile_picture: 'https://example.com/profile.jpg',
        metadata: {
          source: 'api',
          version: '1.0',
        },
      };

      const dto = plainToClass(CreateUserDto, complexObject);

      expect(dto).toBeInstanceOf(CreateUserDto);
      expect(dto.email).toBe('<EMAIL>');
    });
  });
});
