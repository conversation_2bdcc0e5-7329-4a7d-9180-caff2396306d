import { Roles } from 'src/roles/roles.entity';
import { <PERSON>umn, <PERSON><PERSON>ty, ManyToOne, PrimaryGeneratedColumn, JoinColumn } from 'typeorm';

@Entity('role_history')
export class RoleHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  attachment_url: string;

  @ManyToOne(() => Roles, (role) => role.roleHistory)
  role: Roles;

  @Column({ nullable: true })
  roleId: number;
}
