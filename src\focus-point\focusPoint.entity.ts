import { Roles } from 'src/roles/roles.entity';
import { Users } from 'src/users/users.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity('focus_point')
export class FocusPoint {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    default: null,
    nullable: true,
    type: 'enum',
    enum: ['INFO', 'QUERY', 'SUGGESTION', 'ISSUE', 'OTHER'],
  })
  type: string;

  @Column({
    default: null,
    nullable: true,
  })
  message: string;

  @Column({
    default: null,
    nullable: true,
  })
  added_at: Date;

  @Column({
    default: null,
    nullable: true,
  })
  updated_at: Date;

  // soft delete
  @Column({
    default: false,
    nullable: true,
  })
  is_deleted: boolean;

  @Column({
    default: null,
    nullable: true,
  })
  deleted_at: Date;

  @ManyToOne(() => Users, (user) => user.focus_points, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  user: Users;

  @Column({
    nullable: true,
    type: 'uuid',
    default: null,
  })
  userId: string;

  @ManyToOne(() => Roles, (role) => role.focus_points, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  role: Roles;

  @Column({
    default: null,
    nullable: true,
    type: 'int',
  })
  roleId: number;
}
