import { Module } from '@nestjs/common';
import { PreferencesService } from './preferences.service';
import { PreferencesController } from './preferences.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobPreferences } from './prefrences.entity';

@Module({
  imports: [TypeOrmModule.forFeature([JobPreferences])],
  providers: [PreferencesService],
  controllers: [PreferencesController],
})
export class PreferencesModule {}
