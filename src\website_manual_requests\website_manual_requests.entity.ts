import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum ManualInterestType {
  BD_MANUAL = 'BD Manual',
  RECRUITMENT_MANUAL = 'Recruitment Manual',
}

@Entity('website_manual_requests')
export class WebsiteManualRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  first_name: string;

  @Index()
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  last_name: string;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: false,
  })
  job_title: string;

  @Index()
  @Column({
    type: 'varchar',
    length: 200,
    nullable: false,
  })
  company: string;

  @Index()
  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
  })
  email: string;

  @Index()
  @Column({
    type: 'enum',
    enum: ManualInterestType,
    nullable: false,
  })
  manual_interest: ManualInterestType;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'S3 bucket URL for the file',
  })
  s3_url: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Direct file URL for download',
  })
  file_url: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether the email has been sent',
  })
  email_sent: boolean;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the email was sent',
  })
  email_sent_at: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Error message if email sending failed',
  })
  email_error: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}