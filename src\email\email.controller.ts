import {
  Body,
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { EmailService } from './email.service';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { SendEmailDto } from './dto/sendEmail.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express';

@Controller('email')
@ApiTags('Email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @Post('sendMail')
  @ApiOperation({ summary: 'Send an email' })
  async sendEmail(@Body() sendEmailDto: SendEmailDto): Promise<void> {
    console.log('I am called !!!!!!!!!!!!!!!!!!!!!!!!');
    await this.emailService.sendEmail(sendEmailDto);
  }

  @Post('send-email-with-attachment-old')
  @ApiOperation({ summary: 'Send email with file(s) attachments' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', example: '<EMAIL>' },
        subject: { type: 'string', example: 'Test Subject' },
        body: { type: 'string', example: '<p>Hello World</p>' },
        from: { type: 'string', example: '<EMAIL>' },
        cc: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
        bcc: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
        replyTo: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
        attachments: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'attachments', maxCount: 100 }, // Supports up to 10 attachments
    ]),
  )
  async sendEmailWithAttachmentsOld(
    @UploadedFiles()
    files: { attachments?: Express.Multer.File[] },
    @Body() body: SendEmailDto,
  ) {
    console.log('I am send email with attachment old called');

    const attachments =
      files.attachments?.map((file) => ({
        filename: file.originalname,
        content: file.buffer,
        contentType: file.mimetype,
      })) || [];

    await this.emailService.sendEmailWithAttachments({
      ...body,
      to: Array.isArray(body.to) ? body.to : [body.to],
      cc: body.cc || [],
      bcc: body.bcc || [],
      replyTo: body.replyTo || [],
      attachments,
    });

    return { message: 'Email with attachment(s) sent successfully' };
  }

  // @Post('send-email-with-attachment')
  // @ApiOperation({ summary: 'Send email with base64 attachments (JSON body)' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       to: { type: 'string', example: '<EMAIL>' },
  //       subject: { type: 'string', example: 'Test Subject' },
  //       body: { type: 'string', example: '<p>Hello World</p>' },
  //       from: { type: 'string', example: '<EMAIL>' },
  //       cc: {
  //         type: 'array',
  //         items: { type: 'string' },
  //         example: ['<EMAIL>'],
  //       },
  //       bcc: {
  //         type: 'array',
  //         items: { type: 'string' },
  //         example: ['<EMAIL>'],
  //       },
  //       replyTo: {
  //         type: 'array',
  //         items: { type: 'string' },
  //         example: ['<EMAIL>'],
  //       },
  //       attachments: {
  //         type: 'array',
  //         items: {
  //           type: 'object',
  //           properties: {
  //             filename: { type: 'string', example: 'file.zip' },
  //             content: {
  //               type: 'string',
  //               format: 'base64',
  //               example: 'UEsDBBQACAg...',
  //             },
  //             contentType: { type: 'string', example: 'application/zip' },
  //           },
  //         },
  //       },
  //     },
  //   },
  // })
  // async sendEmailWithAttachments(
  //   @Body() body: SendEmailDto & {
  //     attachments?: {
  //       filename: string;
  //       content: string; // base64 encoded
  //       contentType: string;
  //     }[] | any; // Fallback for edge case
  //   },
  // ) {
  //   const attachments =
  //     Array.isArray(body.attachments) &&
  //     body.attachments.every((att) => att.content && att.filename)
  //       ? body.attachments.map((file) => ({
  //           filename: file.filename,
  //           content: Buffer.from(file.content, 'base64'),
  //           contentType: file.contentType || 'application/octet-stream',
  //         }))
  //       : [];

  //   await this.emailService.sendEmailWithAttachments({
  //     ...body,
  //     attachments,
  //   });

  //   return { message: 'Email with attachment(s) sent successfully' };
  // }

  @Post('send-email-with-attachment')
  async sendEmailWithAttachments(
    @Body()
    body: SendEmailDto & {
      attachments?: {
        filename: string;
        content: string; // base64
        contentType: string;
      }[];
      fileUrls?: string[];
    },
  ) {
    console.log('I am send email with attachment called');

    const attachments =
      Array.isArray(body.attachments) &&
      body.attachments.every((att) => att.content && att.filename)
        ? body.attachments.map((file) => ({
            filename: file.filename,
            content: Buffer.from(file.content as unknown as string, 'base64'),
            contentType: file.contentType || 'application/octet-stream',
          }))
        : [];

    const sendEmailInput = {
      ...body,
      attachments,
      fileUrls: body.fileUrls || [],
    };

    await this.emailService.sendEmailWithAttachments(sendEmailInput);

    return { message: 'Email with attachment(s) sent successfully' };
  }

  // sendSimpleEmail
  @Post('send-simple-email')
  @ApiOperation({ summary: 'Send a simple email' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', example: '<EMAIL>' },
        subject: { type: 'string', example: 'Test Subject' },
        body: { type: 'string', example: '<p>Hello World</p>' },
        cc: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
        bcc: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
        replyTo: {
          type: 'array',
          items: { type: 'string' },
          example: ['<EMAIL>'],
        },
      },
    },
  })
  async sendSimpleEmail(
    @Body()
    body: {
      to: string | string[];
      subject: string;
      body: string;
      cc?: string[];
      bcc?: string[];
      replyTo?: string[];
    },
  ): Promise<void> {
    const sendEmailDto: SendEmailDto = {
      to: [body.to].flat(), // Ensure to is always an array
      subject: body.subject,
      body: body.body,
      cc: body.cc || [],
      bcc: body.bcc || [],
      replyTo: body.replyTo || [],
    };
    await this.emailService.sendSimpleEmail(
      sendEmailDto.to,
      sendEmailDto.subject,
      sendEmailDto.body,
      sendEmailDto.cc,
      sendEmailDto.bcc,
      sendEmailDto.replyTo,
    );
  }

  @Post('website-manual-request-email')
  @ApiOperation({ summary: 'Send a website manual request email' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        jobTitle: { type: 'string', example: 'Software Engineer' },
        company: { type: 'string', example: 'Tech Corp' },
        email: { type: 'string', example: '<EMAIL>' },
        requestedManual: { type: 'string', example: 'BD Manual' },
      },
    },
  })
  async websiteManualRequestEmail(
    @Body()
    body: {
      firstName: string;
      lastName: string;
      jobTitle: string;
      company: string;
      email: string;
      requestedManual: string;
    },
  ): Promise<void> {
    await this.emailService.website_manual_request_email(
      body.firstName,
      body.lastName,
      body.jobTitle,
      body.company,
      body.email,
      body.requestedManual,
    );
  }
}
