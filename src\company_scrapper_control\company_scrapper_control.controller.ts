import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { CompanyScrapperControlService } from './company_scrapper_control.service';
import { CreateCompanyScrapperControlDto } from './dto/create-company_scrapper_control.dto';
import { UpdateCompanyScrapperControlDto } from './dto/update-company_scrapper_control.dto';

@Controller('company-scrapper-control')
export class CompanyScrapperControlController {
  constructor(
    private readonly companyScrapperControlService: CompanyScrapperControlService,
  ) {}

  @Post()
  create(
    @Body() createCompanyScrapperControlDto: CreateCompanyScrapperControlDto,
  ) {
    return this.companyScrapperControlService.create(
      createCompanyScrapperControlDto,
    );
  }

  @Post('create-update')
  createAndUpdate(@Body() data: CreateCompanyScrapperControlDto) {
    return this.companyScrapperControlService.createAndUpdate(data);
  }

  @Get('get-company-controller')
  findAll() {
    return this.companyScrapperControlService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.companyScrapperControlService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCompanyScrapperControlDto: UpdateCompanyScrapperControlDto,
  ) {
    return this.companyScrapperControlService.update(
      +id,
      updateCompanyScrapperControlDto,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.companyScrapperControlService.remove(+id);
  }
}
