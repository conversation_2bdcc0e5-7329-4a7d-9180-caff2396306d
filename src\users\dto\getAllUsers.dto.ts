import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsOptional } from 'class-validator';

export class GetAllUsersDto {
  @IsNumber()
  @ApiProperty()
  pageNumber: number;

  @IsNumber()
  @ApiProperty()
  pageSize: number;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  searchString: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  userStatus: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  userRole: string;
}
