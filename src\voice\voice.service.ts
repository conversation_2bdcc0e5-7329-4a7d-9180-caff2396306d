import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class VoiceService {
  async getAccessToken() {
    const response = await axios.post(
      process.env.EIGHT_BY_EIGHT_TOKEN_URL,
      new URLSearchParams({ grant_type: 'client_credentials' }),
      {
        auth: {
          username: process.env.EIGHT_BY_EIGHT_CLIENT_ID,
          password: process.env.EIGHT_BY_EIGHT_CLIENT_SECRET,
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
    return response.data.access_token;
  }
}
