import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Put,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { PersonPhoneService } from './phone.service';
import { PersonPhone } from './phone.entity';
import { PersonPhoneDto } from './dto/person-phone.dto';

@ApiTags('Person Phone')
@Controller('person-phone')
export class PersonPhoneController {
  constructor(private readonly personPhoneService: PersonPhoneService) {}

  @Post(':personId')
  @ApiOperation({ summary: 'Create a phone record for a person' })
  @ApiParam({ name: 'personId', type: Number })
  @ApiResponse({
    status: 201,
    description: 'Phone record created',
    type: PersonPhone,
  })
  async create(
    @Param('personId') personId: number,
    @Body() personPhoneDto: PersonPhoneDto,
  ): Promise<PersonPhone> {
    return this.personPhoneService.create(personId, personPhoneDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all phone records' })
  @ApiResponse({
    status: 200,
    description: 'List of phone records',
    type: [PersonPhone],
  })
  async findAll(): Promise<PersonPhone[]> {
    return this.personPhoneService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a phone record by ID' })
  @ApiParam({ name: 'id', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Phone record found',
    type: PersonPhone,
  })
  async findOne(@Param('id') id: number): Promise<PersonPhone> {
    return this.personPhoneService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a phone record' })
  @ApiParam({ name: 'id', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Phone record updated',
    type: PersonPhone,
  })
  async update(
    @Param('id') id: number,
    @Body() personPhoneDto: PersonPhoneDto,
  ): Promise<PersonPhone> {
    return this.personPhoneService.update(id, personPhoneDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a phone record' })
  @ApiParam({ name: 'id', type: Number })
  @ApiResponse({ status: 204, description: 'Phone record deleted' })
  async remove(@Param('id') id: number): Promise<void> {
    return this.personPhoneService.remove(id);
  }
}
