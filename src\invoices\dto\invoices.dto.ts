import { ApiProperty, PartialType } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { InvoiceCurrency } from '../invoices.entity';

export class InvoiceDto {
  @ApiProperty({
    description: 'The total roles associated with the invoice',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  total_roles?: number;

  @ApiProperty({
    description: 'The total amount of the invoice',
    example: 1500,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiProperty({
    description: 'The status of the invoice',
    example: 'PAID',
    enum: [
      'PAID',
      'UNPAID',
      'INVOICED',
      'NO_RESPONSE',
      'ISSUE',
      'CANCELLED',
      'REFUNDED',
      'EXPIRED',
      'DISPUTED',
      'PENDING',
      'FAILED',
      'COMPLETED',
      'PROCESSING',
      'CHARGEBACK',
    ],
  })
  @IsNotEmpty()
  @IsEnum([
    'PAID',
    'UNPAID',
    'INVOICED',
    'NO_RESPONSE',
    'ISSUE',
    'CANCELLED',
    'REFUNDED',
    'EXPIRED',
    'DISPUTED',
    'PENDING',
    'FAILED',
    'COMPLETED',
    'PROCESSING',
    'CHARGEBACK',
  ])
  status: string;

  @ApiProperty({
    description: 'currency of invoice',
    example: 'USD',
    required: false,
  })
  @IsString()
  currency: InvoiceCurrency;

  @ApiProperty({
    description: 'The unique invoice number',
    example: 12345,
  })
  @IsNotEmpty()
  @IsNumber()
  invoice_number: number;

  @ApiProperty({
    description: 'The date the invoice was issued',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsNotEmpty()
  @IsDateString()
  invoice_date: string;

  @ApiProperty({
    description: 'The due date for the invoice payment',
    example: '2023-01-15T00:00:00.000Z',
  })
  @IsNotEmpty()
  @IsDateString()
  due_date: string;

  @ApiProperty({
    description: 'The file associated with the invoice',
    example: 'invoice_12345.pdf',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  invoice_file?: string;

  @ApiProperty({
    description: 'The ID of the user associated with the invoice',
    example: 'user_123',
  })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({
    type: 'number',
    description: 'The ID of the service associated with the invoice',
    example: 'service_123',
  })
  @IsOptional()
  @IsNumber()
  serviceId?: number;

  @ApiProperty({
    type: 'number',
    description: 'The ID of the person associated with the invoice',
    example: 'person_123',
  })
  @IsOptional()
  @IsNumber()
  personId?: number;
}

export class UpdateInvoiceDto extends PartialType(InvoiceDto) {
  @ApiProperty({
    description: 'ID of the invoice',
    example: 5,
    required: false,
    type: 'number',
  })
  @IsNumber()
  id: number;
}
