import { <PERSON>, Get, Post, Param, Body, Res } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { QueueService } from './queue.service';
import { EmailQueueProcessor } from './processors/email-queue.processor';
import { WhatsAppQueueProcessor } from './processors/whatsapp-queue.processor';
import { QUEUE_NAMES } from './queue.constants';
import { Response } from 'express';
import * as path from 'path';

@Controller('queue')
@ApiTags('Queue Management')
export class QueueController {
  constructor(
    private readonly queueService: QueueService,
    private readonly moduleRef: ModuleRef,
    private readonly emailProcessor: EmailQueueProcessor,
    private readonly whatsappProcessor: WhatsAppQueueProcessor,
  ) {}

  @ApiOperation({ summary: 'Get queue statistics' })
  @Get('stats/:queueName')
  async getQueueStats(@Param('queueName') queueName: string) {
    return await this.queueService.getQueueStats(queueName);
  }

  @ApiOperation({ summary: 'Get all queue statistics' })
  @Get('stats')
  async getAllQueueStats() {
    const queueNames = Object.values(QUEUE_NAMES);
    const stats = [];

    for (const queueName of queueNames) {
      try {
        const queueStats = await this.queueService.getQueueStats(queueName);
        stats.push(queueStats);
      } catch (error) {
        stats.push({
          error: error.message,
          name: queueName,
          status: 'unavailable',
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
          total: 0
        });
      }
    }

    return stats;
  }

  @ApiOperation({ summary: 'Pause a queue' })
  @Post(':queueName/pause')
  async pauseQueue(@Param('queueName') queueName: string) {
    await this.queueService.pauseQueue(queueName);
    return { success: true, message: `Queue ${queueName} paused` };
  }

  @ApiOperation({ summary: 'Resume a queue' })
  @Post(':queueName/resume')
  async resumeQueue(@Param('queueName') queueName: string) {
    await this.queueService.resumeQueue(queueName);
    return { success: true, message: `Queue ${queueName} resumed` };
  }

  @ApiOperation({ summary: 'Clear all jobs from a specific queue' })
  @Post(':queueName/clear')
  async clearQueue(@Param('queueName') queueName: string) {
    const result = await this.queueService.clearQueue(queueName);
    return {
      success: true,
      message: `Cleared ${result.cleared} jobs from queue ${queueName}`,
      ...result
    };
  }

  @ApiOperation({ summary: 'Clear all jobs from all queues' })
  @Post('clear-all')
  async clearAllQueues() {
    const results = await this.queueService.clearAllQueues();
    const totalCleared = Object.values(results).reduce((sum, result: any) => sum + (result.cleared || 0), 0);

    return {
      success: true,
      message: `Cleared ${totalCleared} jobs from all queues`,
      results
    };
  }

  @ApiOperation({ summary: 'Add a test job to queue' })
  @Post(':queueName/test')
  async addTestJob(
    @Param('queueName') queueName: string,
    @Body() body: { candidateId: number; stepId: number },
  ) {
    const testJobData = {
      candidateSequenceStatusId: 1,
      candidateId: body.candidateId,
      stepId: body.stepId,
      sequenceId: 1,
      medium: queueName.replace('-queue', '').toUpperCase(),
      recipientEmail: '<EMAIL>',
      recipientPhone: '+923097442494',
      recipientLinkedIn: 'https://linkedin.com/in/test',
      subject: 'Test Subject',
      body: 'Test message body',
      metadata: { test: true },
    };

    await this.queueService.addJobToQueue(queueName.replace('-queue', ''), testJobData);
    return { success: true, message: `Test job added to ${queueName}` };
  }

  @ApiOperation({ summary: 'Get queue health and available queues' })
  @Get('health')
  async getQueueHealth() {
    return await this.queueService.getSystemHealth();
  }

  @ApiOperation({ summary: 'Queue Dashboard' })
  @Get('dashboard')
  async getDashboard(@Res() res: Response) {
    const dashboardPath = path.join(process.cwd(), 'public', 'queue-dashboard.html');
    return res.sendFile(dashboardPath);
  }

  @ApiOperation({ summary: 'Test Processor Status' })
  @Get('test-processor')
  async testProcessor() {
    try {
      console.log('🧪 QUEUE CONTROLLER: Testing processor instantiation...');

      console.log('🧪 QUEUE CONTROLLER: EmailQueueProcessor injected:', !!this.emailProcessor);
      console.log('🧪 QUEUE CONTROLLER: WhatsAppQueueProcessor injected:', !!this.whatsappProcessor);

      // Try to manually call the email processor
      if (this.emailProcessor) {
        console.log('🧪 QUEUE CONTROLLER: Manually calling email processor...');
        const testJob = {
          id: 'test-manual-call',
          data: {
            candidateSequenceStatusId: 1,
            candidateId: 1,
            stepId: 1,
            recipientEmail: '<EMAIL>',
            subject: 'Test Email',
            body: 'This is a test email from manual processor call',
          },
          opts: {},
          attemptsMade: 0,
          queue: null,
          timestamp: Date.now(),
          delay: 0,
          progress: () => {},
          log: () => {},
          moveToCompleted: () => {},
          moveToFailed: () => {},
          remove: () => {},
          retry: () => {},
          discard: () => {},
          promote: () => {},
          finished: () => Promise.resolve(),
          toJSON: () => ({}),
        } as any;

        try {
          const result = await this.emailProcessor.handleSendEmail(testJob);
          console.log('🧪 QUEUE CONTROLLER: Manual processor call result:', result);
        } catch (error) {
          console.error('🧪 QUEUE CONTROLLER: Manual processor call failed:', error);
        }
      }

      return {
        message: 'Queue processors are running via dependency injection',
        timestamp: new Date().toISOString(),
        status: 'active',
        processors: {
          email: !!this.emailProcessor,
          whatsapp: !!this.whatsappProcessor,
        },
      };
    } catch (error) {
      console.error('🧪 QUEUE CONTROLLER: Error testing processors:', error);
      return {
        message: 'Failed to check processor status',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('test-direct-queue')
  async testDirectQueue() {
    try {
      console.log('🧪 QUEUE CONTROLLER: Testing direct queue access...');

      // Get the email queue directly
      const emailQueue = this.queueService['getQueueByName']('email-queue');
      console.log('🧪 QUEUE CONTROLLER: Email queue found:', !!emailQueue);

      if (emailQueue) {
        console.log('🧪 QUEUE CONTROLLER: Queue name:', emailQueue.name);
        console.log('🧪 QUEUE CONTROLLER: Queue waiting count:', await emailQueue.getWaiting().then(jobs => jobs.length));
        console.log('🧪 QUEUE CONTROLLER: Queue active count:', await emailQueue.getActive().then(jobs => jobs.length));
        console.log('🧪 QUEUE CONTROLLER: Queue completed count:', await emailQueue.getCompleted().then(jobs => jobs.length));

        // Try to add a job directly to the queue
        console.log('🧪 QUEUE CONTROLLER: Adding job directly to queue...');
        const job = await emailQueue.add('send-email', {
          candidateSequenceStatusId: 999,
          candidateId: 999,
          stepId: 999,
          recipientEmail: '<EMAIL>',
          subject: 'Direct Queue Test',
          body: 'This is a direct queue test',
        });

        console.log('🧪 QUEUE CONTROLLER: Job added with ID:', job.id);

        // Wait a bit and check if the job was processed
        setTimeout(async () => {
          try {
            const jobStatus = await job.getState();
            console.log('🧪 QUEUE CONTROLLER: Job status after 2 seconds:', jobStatus);
          } catch (error) {
            console.error('🧪 QUEUE CONTROLLER: Error checking job status:', error);
          }
        }, 2000);
      }

      return {
        success: true,
        message: 'Direct queue test initiated',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('🧪 QUEUE CONTROLLER: Error testing direct queue:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get('force-process-jobs')
  async forceProcessJobs() {
    try {
      console.log('🔧 QUEUE CONTROLLER: Attempting to force process jobs...');

      // Get the email queue directly
      const emailQueue = this.queueService['getQueueByName']('email-queue');

      if (emailQueue) {
        // Get waiting jobs
        const waitingJobs = await emailQueue.getWaiting();
        console.log('🔧 QUEUE CONTROLLER: Found', waitingJobs.length, 'waiting jobs');

        if (waitingJobs.length > 0) {
          const job = waitingJobs[0];
          console.log('🔧 QUEUE CONTROLLER: Attempting to manually process job:', job.id);
          console.log('🔧 QUEUE CONTROLLER: Job data:', job.data);

          // Try to manually trigger job processing
          try {
            // This is a hack to manually trigger the processor
            // In a properly working Bull setup, this shouldn't be necessary
            const result = await this.emailProcessor.handleSendEmail(job);
            console.log('🔧 QUEUE CONTROLLER: Manual processing result:', result);

            // Mark job as completed
            await job.moveToCompleted('Manual processing completed', true);
            console.log('🔧 QUEUE CONTROLLER: Job marked as completed');

            return {
              success: true,
              message: 'Job processed manually',
              jobId: job.id,
              result: result,
              timestamp: new Date().toISOString(),
            };
          } catch (processingError) {
            console.error('🔧 QUEUE CONTROLLER: Error processing job:', processingError);
            await job.moveToFailed(processingError, true);

            return {
              success: false,
              message: 'Job processing failed',
              jobId: job.id,
              error: processingError.message,
              timestamp: new Date().toISOString(),
            };
          }
        } else {
          return {
            success: false,
            message: 'No waiting jobs found',
            timestamp: new Date().toISOString(),
          };
        }
      } else {
        return {
          success: false,
          message: 'Email queue not found',
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error) {
      console.error('🔧 QUEUE CONTROLLER: Error forcing job processing:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
