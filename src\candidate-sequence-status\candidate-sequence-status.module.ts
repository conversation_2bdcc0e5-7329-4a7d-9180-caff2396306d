import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CandidateSequenceStatus } from './candidate-sequence-status.entity';
import { CandidateSequenceStatusService } from './candidate-sequence-status.service';
import { CandidateSequenceStatusController } from './candidate-sequence-status.controller';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CandidateSequenceStatus,
      RoleSequence,
      SequenceSteps,
      RoleCandidate,
    ]),
  ],
  controllers: [CandidateSequenceStatusController],
  providers: [CandidateSequenceStatusService],
  exports: [CandidateSequenceStatusService],
})
export class CandidateSequenceStatusModule {}
