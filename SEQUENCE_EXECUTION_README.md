# 🚀 Recruitment Workflow Automation System

## Overview

This is a comprehensive backend execution engine for dynamic, large-scale recruitment workflow automation built with NestJS, PostgreSQL, Redis, and BullMQ. The system supports 100,000+ candidates efficiently and reliably.

## 🏗️ Architecture

### Core Components

1. **Sequence Management** - Dynamic workflow definitions
2. **Queue System** - BullMQ + Redis for asynchronous processing
3. **Execution Engine** - Handles parallel and sequential step execution
4. **Webhook System** - Processes candidate responses
5. **Status Tracking** - Real-time candidate progress monitoring

### Tech Stack

- **NestJS** - API framework
- **PostgreSQL + TypeORM** - Data storage
- **Redis + BullMQ** - Queue management
- **Bull Dashboard** - Queue monitoring (optional)

## 📊 Database Schema

### Key Entities

1. **RoleSequence** - Workflow definitions
2. **SequenceSteps** - Individual communication steps
3. **CandidateSequenceStatus** - Tracks candidate progress
4. **RoleCandidate** - Candidate information
5. **People** - Contact details

## 🔄 Queue System

### Available Queues

- `email-queue` - Email communications
- `whatsapp-queue` - WhatsApp messages
- `sms-queue` - SMS communications
- `call-queue` - Phone calls
- `linkedin-queue` - LinkedIn messages

### Queue Configuration

```typescript
{
  attempts: 3,
  backoff: 'exponential',
  removeOnComplete: true,
  removeOnFail: false,
  limiter: { max: 10, duration: 1000 }
}
```

## 🚀 Getting Started

### 1. Install Dependencies

```bash
npm install
```

### 2. Setup Environment

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

### 3. Setup Redis

```bash
# Using Docker
docker run -d -p 6379:6379 redis:alpine

# Or install locally
# Follow Redis installation guide for your OS
```

### 4. Database Migration

```bash
npm run start:dev
# TypeORM will auto-sync the schema
```

### 5. Start the Application

```bash
npm run start:dev
```

## 📡 API Endpoints

### Sequence Management

```http
POST /sequence/create
GET /sequence/get-all
GET /sequence/:id
PUT /sequence/:id
DELETE /sequence/:id
POST /sequence/:id/start
GET /sequence/:id/with-steps
GET /sequence/:id/stats
```

### Queue Management

```http
GET /queue/stats
GET /queue/stats/:queueName
POST /queue/:queueName/pause
POST /queue/:queueName/resume
POST /queue/:queueName/test
```

### Webhooks

```http
POST /webhooks/email
POST /webhooks/whatsapp
POST /webhooks/sms
POST /webhooks/call
POST /webhooks/linkedin
POST /webhooks/test/:candidateId/:stepId
```

### Candidate Sequence Status

```http
GET /candidate-sequence-status/candidate/:candidateId/sequence/:sequenceId
GET /candidate-sequence-status/status/:status
```

## 🎯 Usage Examples

### 1. Starting a Sequence

```typescript
// Start sequence for multiple candidates
POST /sequence/1/start
{
  "candidateIds": [1, 2, 3, 4, 5]
}
```

### 2. Webhook Response Processing

```typescript
// Email webhook
POST /webhooks/email
{
  "candidateId": 1,
  "stepId": 2,
  "event": "replied",
  "timestamp": "2024-01-01T10:00:00Z",
  "responseData": { "message": "I'm interested!" }
}
```

### 3. Queue Monitoring

```typescript
// Get all queue statistics
GET /queue/stats

// Response
{
  "email-queue": {
    "waiting": 5,
    "active": 2,
    "completed": 100,
    "failed": 3,
    "delayed": 0
  },
  "whatsapp-queue": { ... }
}
```

## 🔧 Configuration

### Queue Concurrency

Adjust concurrency in queue processors:

```typescript
@Process({ name: 'send-email', concurrency: 10 })
```

### Rate Limiting

Configure rate limits per queue:

```typescript
settings: {
  stalledInterval: 30 * 1000,
  maxStalledCount: 1,
}
```

## 📈 Monitoring & Logging

### Built-in Logging

The system includes comprehensive logging:

- Queue job processing
- Webhook events
- Error tracking
- Performance metrics

### Queue Dashboard (Optional)

Install Bull Dashboard for visual monitoring:

```bash
npm install bull-board
```

## 🔄 Workflow Execution Logic

### Sequential Steps

Steps with the same `order` value execute in parallel.
Steps with different `order` values execute sequentially.

### Example Flow

```
Order 0: Email (parallel with SMS)
Order 0: SMS (parallel with Email)
Order 1: WhatsApp (waits for Order 0 completion)
Order 2: Call (waits for Order 1 completion)
```

### Status Progression

```
PENDING → QUEUED → SENT → DELIVERED → OPENED → CLICKED → REPLIED → COMPLETED
                                                                  ↓
                                                            TRIGGER NEXT STEPS
```

## 🛠️ Customization

### Adding New Communication Channels

1. Create new queue processor
2. Add queue configuration
3. Update webhook handlers
4. Add to enum types

### Custom Business Logic

Extend the `SequenceService` for custom workflow logic:

```typescript
async customStepLogic(candidateId: number, stepId: number) {
  // Your custom logic here
}
```

## 🚨 Error Handling

### Retry Logic

- 3 automatic retries with exponential backoff
- Failed jobs moved to failed queue
- Manual retry capability

### Dead Letter Queue

Failed jobs after max retries are stored for manual review.

## 📊 Performance Optimization

### High Volume Handling

- Redis clustering support
- Queue concurrency tuning
- Database indexing
- Connection pooling

### Recommended Settings for 100k+ Candidates

```typescript
// Queue concurrency
concurrency: 50

// Redis connection pool
maxRetriesPerRequest: 3
retryDelayOnFailover: 100

// Database connection pool
max: 20
min: 5
```

## 🔐 Security

### Webhook Verification

Implement webhook signature verification:

```typescript
const signature = req.headers['x-webhook-signature'];
const isValid = verifyWebhookSignature(payload, signature, secret);
```

### Rate Limiting

API endpoints include rate limiting to prevent abuse.

## 🧪 Testing

### Unit Tests

```bash
npm run test
```

### Integration Tests

```bash
npm run test:e2e
```

### Manual Testing

Use the test webhook endpoint:

```http
POST /webhooks/test/1/2
{
  "event": "replied",
  "medium": "email"
}
```

## 📝 Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server status
   - Verify connection credentials

2. **Queue Jobs Stuck**
   - Check queue processor status
   - Review error logs

3. **Database Connection Issues**
   - Verify PostgreSQL status
   - Check connection pool settings

### Debug Mode

Enable debug logging:

```bash
DEBUG=bull* npm run start:dev
```

## 🚀 Production Deployment

### Environment Setup

1. Configure production Redis cluster
2. Setup database connection pooling
3. Enable monitoring and alerting
4. Configure log aggregation

### Scaling Considerations

- Horizontal scaling with multiple worker instances
- Redis Sentinel for high availability
- Database read replicas
- Load balancing

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review application logs
3. Monitor queue statistics
4. Contact development team

---

**Built with ❤️ for scalable recruitment automation**
