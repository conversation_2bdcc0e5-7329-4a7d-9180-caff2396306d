import { Type } from 'class-transformer';
import { IsString, IsArray, IsNotEmpty, IsPhoneNumber, IsEnum } from 'class-validator';

export class SendTemplateWhatsAppDto {
  @IsString()
  @IsNotEmpty()
  to: string;

  @IsString()
  @IsNotEmpty()
  templateName: string;

  @IsArray()
  @IsNotEmpty()
  templateParams: string[];
}

export class SendWhatsAppDto {
  to: string;
  body: string;
}

export enum MessageChannel {
  SMS = 'sms',
  WHATSAPP = 'whatsapp',
}

export class SendMessageDto {
  @IsPhoneNumber(null)
  to: string;

  @IsString()
  message: string;

  @IsEnum(MessageChannel)
  @Type(() => String)
  channel: MessageChannel;
}