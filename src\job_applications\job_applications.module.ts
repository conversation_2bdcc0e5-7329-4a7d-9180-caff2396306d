import { Module } from '@nestjs/common';
import { JobApplicationsService } from './job_applications.service';
import { JobApplicationsController } from './job_applications.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobApplications } from './job_applications.entity';

@Module({
  imports: [TypeOrmModule.forFeature([JobApplications])],
  providers: [JobApplicationsService],
  controllers: [JobApplicationsController],
})
export class JobApplicationsModule {}
