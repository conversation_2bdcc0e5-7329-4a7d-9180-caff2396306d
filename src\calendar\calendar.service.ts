import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Calendar } from './calendar.entity';
import { Repository } from 'typeorm';
import { CalendarDTO } from './dto/calendar.dto';
import { NotFoundError } from 'rxjs';
import { UpdateCalendarDto } from './dto/updateCalendar.dto';

@Injectable()
export class CalendarService {
  constructor(
    @InjectRepository(Calendar)
    private calendarRepository: Repository<Calendar>,
  ) {}

  async createCalendar(calendar: CalendarDTO): Promise<Calendar> {
    try {
      const newCalendar = this.calendarRepository.create(calendar);
      const savedCalendar = await this.calendarRepository.insert(newCalendar);
      return savedCalendar.raw[0];
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to create calendar',
        error: error.message,
      });
    }
  }

  async updateCalendar(
    id: number,
    calendar: UpdateCalendarDto,
  ): Promise<Calendar> {
    try {
      const existingCalendar = await this.calendarRepository.findOne({
        where: { id },
      });
      if (!existingCalendar) {
        throw new NotFoundException('Calendar not found');
      }

      const updatedCalendar = this.calendarRepository.merge(
        existingCalendar,
        calendar,
      );
      const savedCalendar = await this.calendarRepository.save(updatedCalendar);
      return savedCalendar;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to update calendar',
        error: error.message,
      });
    }
  }

  async getAllCalendars(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = null,
  ): Promise<Calendar[]> {
    try {
      const query = this.calendarRepository.createQueryBuilder('calendar');
      if (searchString) {
        query.where('calendar.event_name LIKE :searchString', {
          searchString: `%${searchString}%`,
        });
      }
      query.skip(page * pageSize).take(pageSize);
      const calendars = await query.getMany();
      return calendars;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to fetch calendars',
        error: error.message,
      });
    }
  }

  async getCalendarById(id: number): Promise<Calendar> {
    try {
      const calendar = await this.calendarRepository.findOne({
        where: { id },
      });
      if (!calendar) {
        throw new NotFoundException('Calendar not found');
      }
      return calendar;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to fetch calendar',
        error: error.message,
      });
    }
  }

  async deleteCalendar(id: number): Promise<void> {
    try {
      const result = await this.calendarRepository.delete(id);
      if (result.affected === 0) {
        throw new NotFoundException('Calendar not found');
      }
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to delete calendar',
        error: error.message,
      });
    }
  }

  async deleteAllCalendars(): Promise<void> {
    try {
      await this.calendarRepository.clear();
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to delete all calendars',
        error: error.message,
      });
    }
  }

  async getCalendarByEventType(
    eventType: string,
    userId: string,
    pageSize?: string,
  ): Promise<Calendar[]> {
    try {
      if (pageSize) {
        const limit = parseInt(pageSize);
        const calendars = await this.calendarRepository.find({
          where: { event_type: eventType, userId: userId },
          relations: ['user', 'person'],
          take: limit,
        });
        return calendars;
      } else {
        const calendars = await this.calendarRepository.find({
          where: { event_type: eventType, userId: userId },
          relations: ['user', 'person'],
        });
        return calendars;
      }
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to fetch calendars by event type',
        error: error.message,
      });
    }
  }
}
