import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ResumeTemplate } from './resume-template.entity';
import { ResumeTemplateDto } from './dto/resumeTemplates.dto';

@Injectable()
export class ResumeTemplateService {
  constructor(
    @InjectRepository(ResumeTemplate)
    private readonly resumeTemplateRepository: Repository<ResumeTemplate>,
  ) {}

  async create(dto: ResumeTemplateDto): Promise<ResumeTemplate> {
    const template = this.resumeTemplateRepository.create(dto);
    return await this.resumeTemplateRepository.save(template);
  }

  async findAll(): Promise<ResumeTemplate[]> {
    return await this.resumeTemplateRepository.find();
  }

  async findOne(id: number): Promise<ResumeTemplate> {
    const template = await this.resumeTemplateRepository.findOne({ where: { id } });
    if (!template) {
      throw new NotFoundException(`Resume template with ID ${id} not found`);
    }
    return template;
  }

  async update(id: number, dto: ResumeTemplateDto): Promise<ResumeTemplate> {
    const template = await this.findOne(id);
    Object.assign(template, dto);
    return await this.resumeTemplateRepository.save(template);
  }

  async delete(id: number): Promise<void> {
    const result = await this.resumeTemplateRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Resume template with ID ${id} not found`);
    }
  }
}
