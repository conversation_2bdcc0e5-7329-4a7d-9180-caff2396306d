import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';

import { UsersModule } from './users.module';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { AuthService } from './auth.service';
import { Users } from './users.entity';
import { EmailModule } from '../email/email.module';
import { EmailService } from '../email/email.service';

describe('UsersModule', () => {
  let module: TestingModule;
  let usersController: UsersController;
  let usersService: UsersService;
  let authService: AuthService;

  // Mock EmailService for testing
  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Users],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([Users]),
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1d' },
        }),
      ],
      controllers: [UsersController],
      providers: [
        UsersService,
        AuthService,
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    }).compile();

    usersController = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
    authService = module.get<AuthService>(AuthService);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('Module Compilation', () => {
    it('should compile the module successfully', () => {
      expect(module).toBeDefined();
    });

    it('should provide UsersController', () => {
      expect(usersController).toBeDefined();
      expect(usersController).toBeInstanceOf(UsersController);
    });

    it('should provide UsersService', () => {
      expect(usersService).toBeDefined();
      expect(usersService).toBeInstanceOf(UsersService);
    });

    it('should provide AuthService', () => {
      expect(authService).toBeDefined();
      expect(authService).toBeInstanceOf(AuthService);
    });
  });

  describe('Dependency Injection', () => {
    it('should inject UsersService into UsersController', () => {
      expect(usersController['usersService']).toBeDefined();
      expect(usersController['usersService']).toBeInstanceOf(UsersService);
    });

    it('should inject AuthService into UsersController', () => {
      expect(usersController['authService']).toBeDefined();
      expect(usersController['authService']).toBeInstanceOf(AuthService);
    });

    it('should inject EmailService into UsersService', () => {
      expect(usersService['emailService']).toBeDefined();
    });

    it('should inject EmailService into AuthService', () => {
      expect(authService['emailService']).toBeDefined();
    });
  });

  describe('TypeORM Integration', () => {
    it('should have Users repository injected into UsersService', () => {
      expect(usersService['usersRepository']).toBeDefined();
    });
  });

  describe('JWT Integration', () => {
    it('should have JwtService injected into AuthService', () => {
      expect(authService['jwtService']).toBeDefined();
    });

    it('should be able to sign JWT tokens', async () => {
      const token = await authService.login(
        '<EMAIL>',
        'password',
        'user-id',
        'Test User',
        'USER',
        'RECRUITER',
      );

      expect(token).toHaveProperty('token');
      expect(typeof token.token).toBe('string');
    });
  });

  describe('Service Integration', () => {
    it('should allow UsersService to interact with database', async () => {
      // This test verifies that the service can perform database operations
      const user = await usersService.findOne('<EMAIL>');
      expect(user).toBeUndefined(); // Should be undefined for non-existent user
    });

    it('should allow AuthService to send emails', async () => {
      mockEmailService.sendEmail.mockResolvedValue(true);

      await authService.sendVerificationCode(
        '<EMAIL>',
        '123456',
        new Date(),
      );

      expect(mockEmailService.sendEmail).toHaveBeenCalled();
    });
  });

  describe('Module Configuration', () => {
    it('should configure JWT with correct options', () => {
      const jwtService = authService['jwtService'];
      expect(jwtService).toBeDefined();
    });

    it('should configure TypeORM with Users entity', () => {
      const usersRepository = usersService['usersRepository'];
      expect(usersRepository).toBeDefined();
      expect(usersRepository.metadata.name).toBe('Users');
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Test that the module can handle errors without crashing
      try {
        await usersService.findOne(null as any);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Environment Configuration', () => {
    it('should use environment variables for JWT configuration', () => {
      // In a real scenario, this would test environment variable usage
      const originalSecret = process.env.JWT_SECRET;
      const originalExpiration = process.env.JWT_EXPIRATION;

      // The module should handle missing environment variables gracefully
      expect(authService).toBeDefined();

      // Restore original values
      if (originalSecret) process.env.JWT_SECRET = originalSecret;
      if (originalExpiration) process.env.JWT_EXPIRATION = originalExpiration;
    });
  });

  describe('Module Exports', () => {
    it('should make services available for other modules', () => {
      // In a real NestJS application, services would be exported for use in other modules
      expect(usersService).toBeDefined();
      expect(authService).toBeDefined();
    });
  });

  describe('Controller Routes', () => {
    it('should have all expected controller methods', () => {
      const controllerMethods = [
        'check',
        'registerUser',
        'verifyEmail',
        'resendVerificationEmail',
        'sendVerificationCode',
        'login',
        'verify',
        'updateUser',
        'getUser',
        'deleteUser',
        'getAllUsers',
        'getUsersByDesignation',
      ];

      controllerMethods.forEach(method => {
        expect(typeof usersController[method]).toBe('function');
      });
    });
  });

  describe('Service Methods', () => {
    it('should have all expected UsersService methods', () => {
      const serviceMethods = [
        'findOne',
        'findOneAndUpdateVerification',
        'createUser',
        'verifyEmail',
        'resendVerificationEmail',
        'updateUser',
        'deleteUser',
        'getAllUsersByFilters',
        'getUsersByDesignation',
        'findUserById',
      ];

      serviceMethods.forEach(method => {
        expect(typeof usersService[method]).toBe('function');
      });
    });

    it('should have all expected AuthService methods', () => {
      const authMethods = [
        'login',
        'sendVerificationCode',
        'comparePassword',
      ];

      authMethods.forEach(method => {
        expect(typeof authService[method]).toBe('function');
      });
    });
  });

  describe('Module Lifecycle', () => {
    it('should initialize and destroy cleanly', async () => {
      // Test module lifecycle
      expect(module).toBeDefined();
      
      // Module should close without errors
      await expect(module.close()).resolves.not.toThrow();
    });
  });
});
