import { ApiProperty, PartialType } from '@nestjs/swagger';
import { InvoiceDto } from './invoices.dto';
import { IsInt } from 'class-validator';

export class UpdateInvoiceDto extends PartialType(InvoiceDto) {
  @ApiProperty({
    type: Number,
    description: 'ID of the invoice to update',
    required: true,
  })
  @IsInt()
  id: number;
  @ApiProperty({
    type: () => InvoiceDto,
    description: 'Update invoice data',
    required: false,
  })
  invoice: InvoiceDto;
}
