import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Qualifications } from './qualifications.entity';
import { Like, Repository } from 'typeorm';
import { QualificationsDto } from './dto/qualifications.dto';

@Injectable()
export class QualificationsService {
  @InjectRepository(Qualifications)
  private qualificationsRepository: Repository<Qualifications>;

  async create(qualifications: QualificationsDto): Promise<Qualifications> {
    try {
      const newQualifications =
        this.qualificationsRepository.create(qualifications);
      return await this.qualificationsRepository.save(newQualifications);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to create qualification.',
        error: error.message,
      });
    }
  }

  async findAll(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = '',
  ): Promise<Qualifications[]> {
    try {
      if (searchString === '') {
        return await this.qualificationsRepository.find({
          order: { title: 'ASC' },
          skip: page * pageSize,
          take: pageSize,
        });
      } else {
        return await this.qualificationsRepository.find({
          where: { title: Like(`%${searchString}%`) },
          order: { title: 'ASC' },
          skip: page * pageSize,
          take: pageSize,
        });
      }
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to retrieve qualifications.',
        error: error.message,
      });
    }
  }

  async findOne(id: number): Promise<Qualifications> {
    try {
      const qualification = await this.qualificationsRepository.findOne({
        where: { id },
      });
      if (!qualification) {
        throw new NotFoundException('Qualification not found.');
      }
      return qualification;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException({
        message: 'Failed to retrieve qualification.',
        error: error.message,
      });
    }
  }

  async update(
    id: number,
    qualifications: QualificationsDto,
  ): Promise<Qualifications> {
    try {
      const result = await this.qualificationsRepository.update(
        { id },
        qualifications,
      );
      if (result.affected === 0) {
        throw new NotFoundException('Qualification not found.');
      }
      return await this.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException({
        message: 'Failed to update qualification.',
        error: error.message,
      });
    }
  }

  async remove(id: number): Promise<void> {
    try {
      const result = await this.qualificationsRepository.delete(id);
      if (result.affected === 0) {
        throw new NotFoundException('Qualification not found.');
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException({
        message: 'Failed to delete qualification.',
        error: error.message,
      });
    }
  }

  async findByCandidateId(candidateId: number): Promise<Qualifications[]> {
    try {
      return await this.qualificationsRepository.find({
        where: { personId: candidateId },
      });
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to retrieve qualifications by candidate ID.',
        error: error.message,
      });
    }
  }
}
