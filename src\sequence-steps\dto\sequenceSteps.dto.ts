import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class SequenceStepsDto {
  @ApiProperty({
    description: 'Name of the sequence step',
    example: 'Initial Outreach',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Order of the sequence step',
    example: 0,
    default: 0,
  })
  @IsInt()
  @IsOptional()
  order?: number;

  @ApiProperty({
    description: 'Type of the sequence step',
    enum: ['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'OTHER'],
    default: 'OUTREACH',
  })
  @IsEnum(['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'OTHER'])
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Medium of the sequence step',
    enum: ['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'],
    default: 'EMAIL',
  })
  @IsEnum(['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'])
  @IsNotEmpty()
  medium: string;

  @ApiProperty({
    description: 'ID of the associated email template',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  templateId?: number;

  @ApiProperty({
    description: 'ID of the associated role sequence',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  roleSequenceId?: number;
}
