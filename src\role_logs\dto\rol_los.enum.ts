export enum RoleLogsStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  ON_HOLD = 'ON_HOLD',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  APPROVED = 'APPROVED',
  REASSIGNED = 'REASSIGNED',
  DONE='DONE',
}

export enum RoleLogsType {
  RESOURCER = 'RESOURCER',
  QA = 'QA',
  ACM = 'ACM',
  BD = 'BD',
  CLIENT = 'CLIENT',
  ADMIN = 'ADMIN',
  HR = 'HR',
  RECRUITER = 'RECRUITER',
  PRE_QUALIFICATION = 'PRE-QUALIFICATION',
}

export enum RoleLogsAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DONE = 'DONE',
  VIEW = 'VIEW',
  DELETE = 'DELETE',
  ASSIGN = 'ASSIGN',
  UNASSIGN = 'UNASSIGN',
  REASSIGN = 'REASSIGN',
  UNDERCHECK = 'UNDERCHECK',
  CHECKED = 'CHECKED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  REOPENED = 'REOPENED',
  CLOSED = 'CLOSED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  STARTED = 'STARTED',
  PAUSED = 'PAUSED',
  RESUMED = 'RESUMED',
  FINISHED = 'FINISHED',
  EXPIRED = 'EXPIRED',
  IN_PROGRESS = 'IN_PROGRESS',
  FAILED = 'FAILED',
  ON_HOLD = 'ON_HOLD',
  FAVOURITES = 'FAVOURITES',
  SELECTED = 'SELECTED',
  INTERVIEWED = 'INTERVIEWED',
  APPLIED = 'APPLIED',
}
