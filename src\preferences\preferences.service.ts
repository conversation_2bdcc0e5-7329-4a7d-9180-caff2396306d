import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { JobPreferences } from './prefrences.entity';
import { Repository } from 'typeorm';
import { JobPreferencesDTO } from './dto/createResumePreferences.dto';

@Injectable()
export class PreferencesService {
  constructor(
    @InjectRepository(JobPreferences)
    private preferencesRepository: Repository<JobPreferences>,
  ) {}

  async createPreferences(data: JobPreferencesDTO): Promise<JobPreferences> {
    try {
      const preferences = this.preferencesRepository.create(data);
      await this.preferencesRepository.save(preferences);
      return preferences;
    } catch (error) {
      console.error('Error creating preferences:', error.message);
      throw new InternalServerErrorException(
        `Failed to create preferences: ${error.message}`,
      );
    }
  }

  async updatePreferences(
    id: number,
    data: JobPreferencesDTO,
  ): Promise<JobPreferences> {
    try {
      const preferences = await this.preferencesRepository.findOne({
        where: { id },
      });
      if (!preferences) {
        throw new NotFoundException(`Preferences with ID ${id} not found`);
      }

      await this.preferencesRepository.update({ id }, data);
      return { ...preferences, ...data };
    } catch (error) {
      console.error('Error updating preferences:', error.message);
      throw new InternalServerErrorException(
        `Failed to update preferences: ${error.message}`,
      );
    }
  }

  async deletePreferences(id: number): Promise<JobPreferences> {
    try {
      const preferences = await this.preferencesRepository.findOne({
        where: { id },
      });
      if (!preferences) {
        throw new NotFoundException(`Preferences with ID ${id} not found`);
      }
      await this.preferencesRepository.delete(id);
      return preferences;
    } catch (error) {
      console.error('Error deleting preferences:', error.message);
      throw new InternalServerErrorException(
        `Failed to delete preferences: ${error.message}`,
      );
    }
  }

  async getPreferencesById(id: number): Promise<JobPreferences> {
    try {
      const preferences = await this.preferencesRepository.findOne({
        where: { id },
      });
      if (!preferences) {
        throw new NotFoundException(`Preferences with ID ${id} not found`);
      }
      return preferences;
    } catch (error) {
      console.error('Error getting preferences:', error.message);
      throw new InternalServerErrorException(
        `Failed to get preferences: ${error.message}`,
      );
    }
  }

  async getAllPreferences(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = '',
  ): Promise<JobPreferences[]> {
    try {
      const preferences = await this.preferencesRepository.find({
        where: { job_title: searchString },
        take: pageSize,
        skip: page * pageSize,
      });
      if (!preferences.length) {
        throw new NotFoundException('No preferences found');
      }
      return preferences;
    } catch (error) {
      console.error('Error getting preferences:', error.message);
      throw new InternalServerErrorException(
        `Failed to get preferences: ${error.message}`,
      );
    }
  }

  //   async getPreferencesByPersonId(personId: number): Promise<JobPreferences> {
  //     try {
  //       const preferences = await this.preferencesRepository.findOne({
  //         where: { personId },
  //       });
  //       if (!preferences) {
  //         throw new NotFoundException(
  //           `Preferences with person ID ${personId} not found`,
  //         );
  //       }
  //       return preferences;
  //     } catch (error) {
  //       console.error('Error getting preferences:', error.message);
  //       throw new InternalServerErrorException(
  //         `Failed to get preferences: ${error.message}`,
  //       );
  //     }
  //   }
}
