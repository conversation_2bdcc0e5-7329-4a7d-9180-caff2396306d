import { Modu<PERSON> } from '@nestjs/common';
import { SequenceService } from './sequence.service';
import { SequenceController } from './sequence.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleSequence } from './sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { CandidateSequenceStatusModule } from 'src/candidate-sequence-status/candidate-sequence-status.module';
import { QueueModule } from 'src/queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RoleSequence,
      SequenceSteps,
      RoleCandidate,
      People,
      PersonEmail,
      PersonPhone,
    ]),
    CandidateSequenceStatusModule,
    QueueModule,
  ],
  providers: [SequenceService],
  controllers: [SequenceController],
  exports: [SequenceService],
})
export class SequenceModule {}
