import { IsString, IsOptional, IsNotEmpty } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateMessageDto {
  @ApiPropertyOptional({ description: 'Twilio message SID' })
  @IsString()
  @IsOptional()
  messageSid?: string;

  @ApiProperty({ description: 'Sender phone number or ID' })
  @IsString()
  @IsNotEmpty()
  from: string;

  @ApiProperty({ description: 'Recipient phone number or ID' })
  @IsString()
  @IsNotEmpty()
  to: string;

  @ApiPropertyOptional({ description: 'Message body text' })
  @IsString()
  @IsOptional()
  body?: string;

  @ApiPropertyOptional({ description: 'Media file URL' })
  @IsString()
  @IsOptional()
  mediaUrl?: string;

  @ApiPropertyOptional({ description: 'Media content type (e.g., image/jpeg)' })
  @IsString()
  @IsOptional()
  mediaContentType?: string;

  @ApiPropertyOptional({ description: 'Message status (sent, received, delivered, etc.)' })
  @IsString()
  @IsOptional()
  status?: string;

  @ApiPropertyOptional({ description: 'Channel (e.g., whatsapp, sms, etc.)' })
  @IsString()
  @IsOptional()
  channel?: string; // e.g., 'whatsapp', 'sms', etc.
}
