import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command,
  GetObjectCommand,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Upload } from '@aws-sdk/lib-storage';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { PassThrough, Readable } from 'stream';
import * as archiver from 'archiver';

@Injectable()
export class S3bucketService {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = this.configService.get<string>('AWS_S3_BUCKET_NAME');

    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.get<string>(
          'AWS_SECRET_ACCESS_KEY',
        ),
      },
      endpoint: `https://s3.${this.configService.get<string>('AWS_REGION')}.amazonaws.com`, // Explicitly setting endpoint
      forcePathStyle: false,
    });
  }

  // Upload file to S3
  async uploadFile(file: Express.Multer.File): Promise<string> {
    try {
      console.log("FILE", file);
      if (!this.bucketName) throw new Error('S3 Bucket name is not defined');

      const folder = 'users';
      const fileName = `${folder}/${uuidv4()}-${file.originalname}`;

      const upload = new Upload({
        client: this.s3Client,
        params: {
          Bucket: this.bucketName,
          Key: fileName,
          Body: file.buffer,
          ContentType: file.mimetype,
          ContentDisposition: `inline; filename="${file.originalname}"`,
          CacheControl: 'max-age=31536000', // Optional
          // Metadata: { customKey: 'customValue' }, // Optional
        },
      });

      await upload.done();

      return `https://${this.bucketName}.s3.${this.configService.get<string>('AWS_REGION')}.amazonaws.com/${fileName}`;
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw new Error('File upload failed');
    }
  }

  // Upload file to specific folder in S3
  async uploadFileToFolder(file: Express.Multer.File, folderPath: string = ''): Promise<string> {
    try {
      if (!this.bucketName) throw new Error('S3 Bucket name is not defined');

      // Clean and normalize the folder path
      let normalizedFolderPath = folderPath.trim();
      if (normalizedFolderPath && !normalizedFolderPath.endsWith('/')) {
        normalizedFolderPath += '/';
      }

      // Create the full file path
      const fileName = normalizedFolderPath ?
        `${normalizedFolderPath}${uuidv4()}-${file.originalname}` :
        `${uuidv4()}-${file.originalname}`;

      const upload = new Upload({
        client: this.s3Client,
        params: {
          Bucket: this.bucketName,
          Key: fileName,
          Body: file.buffer,
          ContentType: file.mimetype,
          ContentDisposition: `inline; filename="${file.originalname}"`,
          CacheControl: 'max-age=31536000',
        },
      });

      await upload.done();

      return `https://${this.bucketName}.s3.${this.configService.get<string>('AWS_REGION')}.amazonaws.com/${fileName}`;
    } catch (error) {
      console.error('Error uploading file to folder in S3:', error);
      throw new Error('File upload to folder failed');
    }
  }
  

  // Delete a file from S3
  async deleteFile(fileUrl: string): Promise<void> {
    try {
      const fileName = this.extractFileKey(fileUrl);
      await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: this.bucketName,
          Key: fileName,
        }),
      );
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      throw new Error('File deletion failed');
    }
  }

  // Delete multiple files from S3
  async deleteFiles(fileUrls: string[]): Promise<void> {
    try {
      await Promise.all(fileUrls.map((fileUrl) => this.deleteFile(fileUrl)));
    } catch (error) {
      console.error('Error deleting files from S3:', error);
      throw new Error('Files deletion failed');
    }
  }

  // List all files and folders in a given path
  async listFiles(prefix: string = ''): Promise<string[]> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix,
      });

      const response = await this.s3Client.send(command);

      return response.Contents?.map((file) => file.Key || '') || [];
    } catch (error) {
      console.error('Error listing files from S3:', error);
      throw new Error('File listing failed');
    }
  }

  // Create an empty folder in S3
  async createFolder(folderName: string): Promise<string> {
    try {
      if (!folderName.endsWith('/')) folderName += '/';

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: folderName,
        Body: '',
      });

      await this.s3Client.send(command);
      return `Folder ${folderName} created successfully`;
    } catch (error) {
      console.error('Error creating folder in S3:', error);
      throw new Error('Folder creation failed');
    }
  }

  async downloadFileWithMeta(fileUrl: string): Promise<{
    buffer: Buffer;
    contentType?: string;
    contentDisposition?: string;
  }> {
    try {
      const fileName = this.extractFileKey(fileUrl);
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: fileName,
      });
  
      const response = await this.s3Client.send(command);
      const chunks: Uint8Array[] = [];
      const stream = response.Body as Readable;
      for await (const chunk of stream) {
        chunks.push(chunk);
      }
  
      return {
        buffer: Buffer.concat(chunks),
        contentType: response.ContentType,
        contentDisposition: response.ContentDisposition,
      };
    } catch (error) {
      console.error('Error downloading file from S3:', error);
      throw new Error('File download failed');
    }
  }
  
  async zipFilesAsAttachment(fileUrls: string[]): Promise<{
    filename: string;
    content: Buffer;
    contentType: string;
  }> {
    try {
      const archive = archiver('zip', { zlib: { level: 9 } });
      const passThrough = new PassThrough();
      archive.pipe(passThrough);
  
      // Trigger streaming into archive
      const appendPromises = fileUrls.map(async (fileUrl) => {
        const fileName = this.extractFileKey(fileUrl);
        const command = new GetObjectCommand({
          Bucket: this.bucketName,
          Key: fileName,
        });
  
        const response = await this.s3Client.send(command);
        const stream = response.Body as Readable;
        archive.append(stream, { name: fileName });
      });
  
      // Await all appends before finalizing
      await Promise.all(appendPromises);
      archive.finalize();
  
      // Collect zip stream
      const chunks: Uint8Array[] = [];
      for await (const chunk of passThrough) {
        chunks.push(chunk);
      }
  
      return {
        filename: `Candidates-${Date.now()}.zip`,
        content: Buffer.concat(chunks),
        contentType: 'application/zip',
      };
    } catch (error) {
      console.error('Error zipping files from S3:', error);
      throw new Error('File zipping failed');
    }
  }
  
  // Generate pre-signed URL for file upload
  async generateUploadUrl(
    fileName: string,
    mimeType: string,
    userId: number,
    folderPath: string = '',
  ): Promise<{ uploadUrl: string; s3Key: string }> {
    try {
      const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
      const s3Key = `users/${userId}/${folderPath}${folderPath ? '/' : ''}${uuidv4()}-${sanitizedFileName}`;

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
        ContentType: mimeType,
      });

      const uploadUrl = await getSignedUrl(this.s3Client, command, {
        expiresIn: 3600, // 1 hour
      });

      return { uploadUrl, s3Key };
    } catch (error) {
      console.error('Error generating upload URL:', error);
      throw new Error('Failed to generate upload URL');
    }
  }

  // Generate pre-signed URL for file download
  async generateDownloadUrl(s3Key: string): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      return await getSignedUrl(this.s3Client, command, {
        expiresIn: 3600, // 1 hour
      });
    } catch (error) {
      console.error('Error generating download URL:', error);
      throw new Error('Failed to generate download URL');
    }
  }

  // Copy file/folder (for rename operations)
  async copyObject(sourceKey: string, destinationKey: string): Promise<void> {
    try {
      const command = new CopyObjectCommand({
        Bucket: this.bucketName,
        CopySource: `${this.bucketName}/${sourceKey}`,
        Key: destinationKey,
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error('Error copying object:', error);
      throw new Error('Failed to copy object');
    }
  }

  // Delete object by S3 key
  async deleteObjectByKey(s3Key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: s3Key,
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error('Error deleting object by key:', error);
      throw new Error('Failed to delete object');
    }
  }

  // List objects with folder structure support
  async listObjectsInFolder(
    prefix: string,
    delimiter: string = '/',
  ): Promise<{
    files: Array<{ key: string; size: number; lastModified: Date }>;
    folders: string[];
  }> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix,
        Delimiter: delimiter,
      });

      const response = await this.s3Client.send(command);

      const files = response.Contents?.map((obj) => ({
        key: obj.Key || '',
        size: obj.Size || 0,
        lastModified: obj.LastModified || new Date(),
      })) || [];

      const folders = response.CommonPrefixes?.map((prefix) => prefix.Prefix || '') || [];

      return { files, folders };
    } catch (error) {
      console.error('Error listing objects in folder:', error);
      throw new Error('Failed to list objects');
    }
  }

  // Generate folder path for user
  generateUserFolderPath(userId: number, folderPath: string = ''): string {
    const basePath = `users/${userId}`;
    return folderPath ? `${basePath}/${folderPath}` : basePath;
  }

  // List ALL objects in bucket for global search (no delimiter)
  async listAllObjects(
    prefix: string = '',
  ): Promise<{
    files: Array<{ key: string; size: number; lastModified: Date }>;
  }> {
    try {
      const allFiles: Array<{ key: string; size: number; lastModified: Date }> = [];
      let continuationToken: string | undefined;

      do {
        const command = new ListObjectsV2Command({
          Bucket: this.bucketName,
          Prefix: prefix,
          // NO DELIMITER - this is key for global search
          ContinuationToken: continuationToken,
          MaxKeys: 1000, // Get up to 1000 objects per request
        });

        const response = await this.s3Client.send(command);

        if (response.Contents) {
          const files = response.Contents.map((obj) => ({
            key: obj.Key || '',
            size: obj.Size || 0,
            lastModified: obj.LastModified || new Date(),
          }));
          allFiles.push(...files);
        }

        continuationToken = response.NextContinuationToken;
      } while (continuationToken);

      return { files: allFiles };
    } catch (error) {
      console.error('Error listing all objects:', error);
      throw new Error('Failed to list all objects');
    }
  }

  // List objects with pagination for optimized search
  async listObjectsPaginated(
    prefix: string = '',
    continuationToken?: string,
    maxKeys: number = 500,
  ): Promise<{
    Contents: Array<{ Key: string; Size: number; LastModified: Date }>;
    NextContinuationToken?: string;
  }> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucketName,
        Prefix: prefix,
        ContinuationToken: continuationToken,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      const objects = response.Contents || [];

      const Contents = objects.map((obj) => ({
        Key: obj.Key || '',
        Size: obj.Size || 0,
        LastModified: obj.LastModified || new Date(),
      }));

      return {
        Contents,
        NextContinuationToken: response.NextContinuationToken
      };
    } catch (error) {
      console.error('Error listing objects with pagination:', error);
      throw new Error('Failed to list objects');
    }
  }

  // Move folder or file to trash (handles both files and folders)
  async moveToTrash(sourcePath: string, trashPath: string): Promise<void> {
    try {
      if (sourcePath.endsWith('/')) {
        // It's a folder - move all contents
        await this.moveFolderToTrash(sourcePath, trashPath);
      } else {
        // It's a file - simple copy and delete
        await this.copyObject(sourcePath, trashPath);
        await this.deleteObjectByKey(sourcePath);
      }
    } catch (error) {
      console.error('Error moving to trash:', error);
      throw new Error(`Failed to move ${sourcePath} to trash: ${error.message}`);
    }
  }

  // Move entire folder to trash
  private async moveFolderToTrash(folderPath: string, trashPath: string): Promise<void> {
    try {
      console.log(`🗑️ Moving folder ${folderPath} to ${trashPath}`);

      // List all objects in the folder (limit to prevent timeout)
      const allObjects = await this.listAllObjects(folderPath);

      console.log(`🗑️ Found ${allObjects.files.length} files in folder`);

      if (allObjects.files.length === 0) {
        // Empty folder - create a marker file in trash
        console.log('🗑️ Empty folder, creating marker');
        await this.s3Client.send(new PutObjectCommand({
          Bucket: this.bucketName,
          Key: trashPath,
          Body: '',
        }));

        // Delete the original folder marker if it exists
        try {
          await this.deleteObjectByKey(folderPath);
        } catch (error) {
          // Folder marker might not exist, that's okay
          console.log('🗑️ Folder marker not found, continuing...');
        }
      } else {
        // Limit the number of files to process to prevent timeout
        const maxFiles = 50; // Process max 50 files at once
        const filesToProcess = allObjects.files.slice(0, maxFiles);

        if (allObjects.files.length > maxFiles) {
          throw new Error(`Folder contains ${allObjects.files.length} files. Please delete smaller folders or contact administrator.`);
        }

        console.log(`🗑️ Processing ${filesToProcess.length} files`);

        // Copy all files in the folder to trash
        for (const file of filesToProcess) {
          const relativePath = file.key.replace(folderPath, '');
          const newTrashPath = trashPath.replace(/\/$/, '') + '/' + relativePath;

          console.log(`🗑️ Moving file: ${file.key} -> ${newTrashPath}`);
          await this.copyObject(file.key, newTrashPath);
          await this.deleteObjectByKey(file.key);
        }
      }

      console.log('🗑️ Folder move completed successfully');
    } catch (error) {
      console.error('🗑️ Error moving folder to trash:', error);
      throw new Error(`Failed to move folder to trash: ${error.message}`);
    }
  }

  // Extract file key from URL
  private extractFileKey(fileUrl: string): string {
    return fileUrl.split(
      `https://${this.bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/`,
    )[1];
  }
}
