import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { AuthService } from './auth.service';
import { CreateUserDto } from './dto/create-user.dto';
import { LoginDto } from './dto/login.dto';
import { VerifyDto } from './dto/verify.dto';
import { UpdateUserDto } from './dto/updateUser.dto';

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;
  let authService: AuthService;

  const mockUser = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    full_name: '<PERSON>',
    username: 'joh<PERSON><PERSON>',
    password_hash: '$2b$10$hashedpassword',
    role: 'USER',
    designation: 'RECRUITER',
    status: 'ACTIVE',
    verification_code: '123456',
    verification_code_expires: new Date(Date.now() + 10 * 60 * 1000),
  };

  const mockUsersService = {
    createUser: jest.fn(),
    verifyEmail: jest.fn(),
    resendVerificationEmail: jest.fn(),
    findOneAndUpdateVerification: jest.fn(),
    findOne: jest.fn(),
    findUserById: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
    getAllUsersByFilters: jest.fn(),
    getUsersByDesignation: jest.fn(),
  };

  const mockAuthService = {
    login: jest.fn(),
    sendVerificationCode: jest.fn(),
    comparePassword: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
    authService = module.get<AuthService>(AuthService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('check', () => {
    it('should return welcome message', async () => {
      const result = await controller.check();

      expect(result).toBe('<h1>Welcome to the user module. Its working fine</h1>');
    });
  });

  describe('registerUser', () => {
    const createUserDto: CreateUserDto = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'Jane',
      last_name: 'Smith',
      username: 'janesmith',
      source: 'CRM',
      role: 'USER' as any,
      designation: 'RECRUITER' as any,
      profile_picture: 'https://example.com/profile.jpg',
      status: 'ACTIVE' as any,
    };

    it('should register a new user successfully', async () => {
      const expectedResult = {
        message: 'User created successfully. Please check your email to verify your account.',
        user: mockUser,
      };
      mockUsersService.createUser.mockResolvedValue(expectedResult);

      const result = await controller.registerUser(createUserDto);

      expect(result).toEqual(expectedResult);
      expect(mockUsersService.createUser).toHaveBeenCalledWith(createUserDto);
    });

    it('should throw BadRequestException when user creation fails', async () => {
      mockUsersService.createUser.mockRejectedValue(
        new BadRequestException('User with this email already exists'),
      );

      await expect(controller.registerUser(createUserDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('verifyEmail', () => {
    it('should verify email successfully', async () => {
      mockUsersService.verifyEmail.mockResolvedValue(undefined);

      const result = await controller.verifyEmail('valid-token');

      expect(result).toEqual({ message: 'Email verified successfully' });
      expect(mockUsersService.verifyEmail).toHaveBeenCalledWith('valid-token');
    });

    it('should throw BadRequestException when token is missing', async () => {
      await expect(controller.verifyEmail('')).rejects.toThrow(
        new BadRequestException('Verification token is required.'),
      );
    });

    it('should throw BadRequestException when token is invalid', async () => {
      mockUsersService.verifyEmail.mockRejectedValue(
        new BadRequestException('Invalid or expired verification token'),
      );

      await expect(controller.verifyEmail('invalid-token')).rejects.toThrow(BadRequestException);
    });
  });

  describe('resendVerificationEmail', () => {
    it('should resend verification email successfully', async () => {
      mockUsersService.resendVerificationEmail.mockResolvedValue(undefined);

      const result = await controller.resendVerificationEmail('<EMAIL>', 'CRM');

      expect(result).toEqual({ message: 'Verification email sent successfully' });
      expect(mockUsersService.resendVerificationEmail).toHaveBeenCalledWith('<EMAIL>', 'CRM');
    });

    it('should throw BadRequestException when email is missing', async () => {
      await expect(controller.resendVerificationEmail('', 'CRM')).rejects.toThrow(
        new BadRequestException('Email is required.'),
      );
    });
  });

  describe('login', () => {
    const loginDto: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
      source: 'crm',
      designation: 'RECRUITER',
    };

    it('should login successfully and send verification code', async () => {
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true as never);
      mockAuthService.sendVerificationCode.mockResolvedValue(undefined);

      const result = await controller.login(loginDto);

      expect(result).toEqual({ message: 'Verification code sent to your email' });
      expect(mockUsersService.findOneAndUpdateVerification).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthService.sendVerificationCode).toHaveBeenCalledWith(
        '<EMAIL>',
        mockUser.verification_code,
        mockUser.verification_code_expires,
      );
    });

    it('should throw BadRequestException when user not found', async () => {
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(null);

      await expect(controller.login(loginDto)).rejects.toThrow(
        new BadRequestException('User not found'),
      );
    });

    it('should throw BadRequestException for invalid credentials', async () => {
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(mockUser);
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(false as never);

      await expect(controller.login(loginDto)).rejects.toThrow(
        new BadRequestException('Invalid credentials'),
      );
    });

    it('should throw BadRequestException for unauthorized website login', async () => {
      const websiteLoginDto = { ...loginDto, source: 'website', designation: 'ADMIN' };
      const userWithDifferentDesignation = { ...mockUser, designation: 'RECRUITER' };
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(userWithDifferentDesignation);

      await expect(controller.login(websiteLoginDto)).rejects.toThrow(
        new BadRequestException('Not Authorized to login'),
      );
    });
  });

  describe('verify', () => {
    const verifyDto: VerifyDto = {
      email: '<EMAIL>',
      verification_code: '123456',
    };

    it('should verify 2FA code successfully', async () => {
      mockUsersService.findOne.mockResolvedValue(mockUser);
      const expectedToken = { token: 'jwt-token' };
      mockAuthService.login.mockResolvedValue(expectedToken);

      const result = await controller.verify(verifyDto);

      expect(result).toEqual(expectedToken);
      expect(mockUsersService.findOne).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthService.login).toHaveBeenCalledWith(
        '<EMAIL>',
        mockUser.password_hash,
        mockUser.id,
        mockUser.full_name,
        mockUser.role,
        mockUser.designation,
      );
    });
  });

  describe('updateUser', () => {
    const updateUserDto: UpdateUserDto = {
      first_name: 'Updated',
      last_name: 'Name',
      phone_number: '+1234567890',
    };

    it('should update user successfully', async () => {
      mockUsersService.findUserById.mockResolvedValue(mockUser);
      mockUsersService.updateUser.mockResolvedValue(undefined);

      const result = await controller.updateUser('user-id', updateUserDto);

      expect(result).toEqual({ message: 'User updated successfully' });
      expect(mockUsersService.findUserById).toHaveBeenCalledWith('user-id');
      expect(mockUsersService.updateUser).toHaveBeenCalledWith('user-id', updateUserDto);
    });

    it('should throw BadRequestException when user ID is invalid', async () => {
      await expect(controller.updateUser('', updateUserDto)).rejects.toThrow(
        new BadRequestException('Invalid user ID.'),
      );
    });

    it('should throw BadRequestException when user not found', async () => {
      mockUsersService.findUserById.mockResolvedValue(null);

      await expect(controller.updateUser('nonexistent-id', updateUserDto)).rejects.toThrow(
        new BadRequestException('User not found'),
      );
    });
  });

  describe('getUser', () => {
    it('should return user successfully', async () => {
      mockUsersService.findUserById.mockResolvedValue(mockUser);

      const result = await controller.getUser('user-id');

      expect(result).toEqual(mockUser);
      expect(mockUsersService.findUserById).toHaveBeenCalledWith('user-id');
    });

    it('should throw BadRequestException when user not found', async () => {
      mockUsersService.findUserById.mockResolvedValue(null);

      await expect(controller.getUser('nonexistent-id')).rejects.toThrow(
        new BadRequestException('User not found'),
      );
    });
  });

  describe('getAllUsers', () => {
    it('should return all users with filters', async () => {
      const mockUsers = [mockUser];
      mockUsersService.getAllUsersByFilters.mockResolvedValue(mockUsers);

      const result = await controller.getAllUsers(
        'USER',
        'RECRUITER',
        'ACTIVE',
        0,
        10,
        'John',
      );

      expect(result).toEqual(mockUsers);
      expect(mockUsersService.getAllUsersByFilters).toHaveBeenCalledWith(
        'USER',
        'RECRUITER',
        'ACTIVE',
        0,
        10,
        'John',
      );
    });

    it('should return all users without filters', async () => {
      const mockUsers = [mockUser];
      mockUsersService.getAllUsersByFilters.mockResolvedValue(mockUsers);

      const result = await controller.getAllUsers();

      expect(result).toEqual(mockUsers);
      expect(mockUsersService.getAllUsersByFilters).toHaveBeenCalledWith(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
      );
    });
  });

  describe('getUsersByDesignation', () => {
    it('should return users by designation', async () => {
      const mockUsers = [mockUser];
      mockUsersService.getUsersByDesignation.mockResolvedValue(mockUsers);

      const result = await controller.getUsersByDesignation('RECRUITER');

      expect(result).toEqual(mockUsers);
      expect(mockUsersService.getUsersByDesignation).toHaveBeenCalledWith('RECRUITER');
    });
  });

  describe('sendVerificationCode', () => {
    it('should send verification code successfully', async () => {
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(mockUser);
      mockAuthService.sendVerificationCode.mockResolvedValue(undefined);

      const result = await controller.sendVerificationCode({ email: '<EMAIL>' });

      expect(result).toEqual({ message: 'Verification code sent successfully' });
      expect(mockUsersService.findOneAndUpdateVerification).toHaveBeenCalledWith('<EMAIL>');
      expect(mockAuthService.sendVerificationCode).toHaveBeenCalledWith(
        '<EMAIL>',
        mockUser.verification_code,
        mockUser.verification_code_expires,
      );
    });

    it('should throw BadRequestException when user not found', async () => {
      mockUsersService.findOneAndUpdateVerification.mockResolvedValue(null);

      await expect(controller.sendVerificationCode({ email: '<EMAIL>' })).rejects.toThrow(
        new BadRequestException('User not found'),
      );
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully', async () => {
      mockUsersService.deleteUser.mockResolvedValue(undefined);

      const result = await controller.deleteUser('user-id');

      expect(result).toEqual({ message: 'User deleted successfully' });
      expect(mockUsersService.deleteUser).toHaveBeenCalledWith('user-id');
    });

    it('should throw BadRequestException when user ID is invalid', async () => {
      await expect(controller.deleteUser('')).rejects.toThrow(
        new BadRequestException('Invalid user ID.'),
      );
    });
  });
});
