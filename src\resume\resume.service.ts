import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Resume } from './resume.entity';
import { ResumeTemplate } from 'src/resume-templates/resume-template.entity';

@Injectable()
export class ResumeService {
  constructor(
    @InjectRepository(Resume) private readonly resumeRepo: Repository<Resume>,
    @InjectRepository(ResumeTemplate) private readonly templateRepo: Repository<ResumeTemplate>,
  ) {}

  async createResume(userId: string, templateId: number, data: Record<string, any>) {
    const template = await this.templateRepo.findOne({ where: { id: templateId } });
    if (!template) throw new NotFoundException('Template not found');

    const resume = this.resumeRepo.create({ userId, template, data });
    return this.resumeRepo.save(resume);
  }

  async getResumesByUser(userId: string) {
    return this.resumeRepo.find({ where: { userId } });
  }

  async getResumeById(id: number) {
    return this.resumeRepo.findOne({ where: { id } });
  }
}
