import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('contact_us')
export class ContactUs {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ name: 'first_name', type: 'varchar', length: 255 , nullable: true })
    firstName: string;

    @Column({ name: 'last_name', type: 'varchar', length: 255 , nullable: true })
    lastName: string;

    @Column({ name: 'job_title', type: 'varchar', length: 255 , nullable: true })
    jobTitle: string;

    @Column({ name: 'company_name', type: 'varchar', length: 255 , nullable: true })
    companyName: string;

    @Column({ name: 'business_email', type: 'varchar', length: 255 , nullable: true })
    businessEmail: string;

    @Column({ name: 'phone_number', type: 'varchar', length: 20  , nullable: true})
    phoneNumber: string;

    @Column({ name: 'company_post_code', type: 'varchar', length: 20 , nullable: true})
    companyPostCode: string;

    @Column({ name: 'industry', type: 'varchar', length: 255 ,nullable: true})
    industry: string;

    @Column({ name: 'interested_services', type: 'text' ,nullable: true})
    interestedServices: string;

    @Column({ name: 'company_website', type: 'text', nullable: true })
    companyWebsite: string;
}