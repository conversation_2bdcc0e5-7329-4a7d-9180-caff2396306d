import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Between,
  Brackets,
  DataSource,
  ILike,
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Not,
  Repository,
} from 'typeorm';
import { People } from './people.entity';
import { GetAllPersonsDto, PeopleDto } from './dto/createPeople.dto';
import { UpdatePeopleDTO } from './dto/updatePeople.dto';
import { RenewClientDto } from './dto/renewClient.dto';
import { Service } from 'src/service/service.entity';
import { PersonSource, PersonType, ProspectStatus } from './dto/people.enums';
import { PersonSkill } from 'src/skills/skills.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Experience } from 'src/experience/experience.entity';
import { Languages } from 'src/languages/langauges.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { Company } from 'src/company/company.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { calcPercent } from 'src/helper/helper';
import * as fs from 'fs';
import * as csv from 'csv-parse';
import { Project } from 'src/projects/projects.entity';
import { isArray } from 'class-validator';

@Injectable()
export class PeopleService {
  constructor(
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(PersonSkill)
    private readonly personSkillRepository: Repository<PersonSkill>,
    @InjectRepository(Qualifications)
    private readonly qualificationsRepository: Repository<Qualifications>,
    @InjectRepository(Experience)
    private readonly experienceRepository: Repository<Experience>,
    @InjectRepository(Languages)
    private readonly languagesRepository: Repository<Languages>,
    private readonly dataSource: DataSource,
    @InjectRepository(RoleCandidate)
    private readonly roleCandidateRepository: Repository<RoleCandidate>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(MailBox)
    private readonly mailBoxRepository: Repository<MailBox>,
  ) {}

  async create(
    PeopleDto: PeopleDto,
    company_name: string,
    company_link: string,
    email: string,
    phone_number: string,
    company_size: string,
  ): Promise<People> {
    try {
      // console.log('khjsdgfsdfsdfsdf', PeopleDto);
      if (
        PeopleDto.person_type !== PersonType.CLIENT &&
        PeopleDto.person_type !== PersonType.PROSPECT
      ) {
        // if service is assigned, its not a valid person
        if (PeopleDto.serviceId) {
          throw new NotFoundException(
            `Service ID cannot be assigned for this person type`,
          );
        }
      }

      // check if source is website, then no person should exist against userId

      if (PeopleDto.profile_source === 'WEBSITE') {
        const existingPerson = await this.peopleRepository.findOne({
          where: { userId: PeopleDto.userId },
        });
        if (existingPerson) {
          throw new NotFoundException(`Person already exists for this user ID`);
        }
      }

      // add company

      const newCompany = {
        name: company_name,
        profile_url: company_link,
        company_email: email,
        company_phone: phone_number,
        staff_count_range_start: parseInt(company_size.split('-')[0], 10),
        staff_count_range_end: parseInt(company_size.split('-')[1], 10),
        userId: PeopleDto.userId,
      };

      const existingCompany = await this.companyRepository.findOne({
        where: { name: company_name },
      });

      if (existingCompany) {
        PeopleDto.companyId = existingCompany.id;
      } else {
        const company = this.companyRepository.create(newCompany);
        const savedCompany = await this.companyRepository.save(company);
        PeopleDto.companyId = savedCompany.id;
      }

      const person = this.peopleRepository.create(PeopleDto);
      return await this.peopleRepository.save(person);
    } catch (error) {
      console.error('Error creating person:', error);
      throw new InternalServerErrorException({
        message: 'Error creating person',
        error: error.message,
      });
    }
  }

  async findAll(
    page: number = 0,
    pageSize = 10,
    search: string = '',
    person_type: PersonType = null,
  ): Promise<People[]> {
    try {
      const queryBuilder = this.peopleRepository.createQueryBuilder('people');
      if (search) {
        queryBuilder.where('people.name LIKE :search', {
          search: `%${search}%`,
        });
      }
      if (person_type) {
        queryBuilder.andWhere('people.person_type = :person_type', {
          person_type,
        });
      }
      queryBuilder.skip(page * pageSize).take(pageSize);
      const people = await queryBuilder.getMany();
      return people;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving people',
        error: error.message,
      });
    }
  }

  async getAllPersons(queryParams: GetAllPersonsDto) {
    try {
      const {
        page,
        pageSize,
        search,
        country_id,
        sector_id,
        findHiringPersons,
        findWithEmails,
        findWithoutEmails,
        selectedCountry,
        selectedSector,
        industries,
        startDate,
        endDate,
      } = queryParams;

      const pageInt = parseInt(page) || 0;
      const pageSizeInt = parseInt(pageSize) || 10;
      const limit = pageSizeInt;
      const skip = pageInt * pageSizeInt;

      // Build QueryBuilder
      let queryBuilder = this.peopleRepository
        .createQueryBuilder('people')
        .leftJoinAndSelect('people.company', 'company')
        .leftJoinAndSelect('people.country', 'country')
        .leftJoinAndSelect('people.sector', 'sector')
        .leftJoinAndSelect('people.jobs', 'jobs')
        .leftJoinAndSelect('people.emails', 'emails');

      let countBaseCondition = {} as any;

      // Apply conditions using QueryBuilder syntax
      if (country_id || selectedCountry) {
        const countryId = parseInt(country_id) || parseInt(selectedCountry);
        queryBuilder = queryBuilder.andWhere('people.countryId = :countryId', {
          countryId,
        });
        countBaseCondition.countryId = countryId;
      }

      if (sector_id || selectedSector) {
        const sectorIdValue = parseInt(sector_id) || parseInt(selectedSector);
        queryBuilder = queryBuilder.andWhere('people.sectorId = :sectorId', {
          sectorId: sectorIdValue,
        });
        countBaseCondition.sectorId = sectorIdValue;
      }

      // Handle date conditions with QueryBuilder
      if (startDate && endDate) {
        if (startDate === endDate) {
          queryBuilder = queryBuilder.andWhere(
            'people.created_at >= :startDate',
            {
              startDate: `${startDate}T00:00:00.000Z`,
            },
          );
          countBaseCondition.created_at = MoreThanOrEqual(
            `${startDate}T00:00:00.000Z`,
          );
        } else {
          queryBuilder = queryBuilder.andWhere(
            'people.created_at BETWEEN :startDate AND :endDate',
            {
              startDate: `${startDate}T00:00:00.000Z`,
              endDate: `${endDate}T23:59:59.999Z`,
            },
          );
          countBaseCondition.created_at = Between(
            `${startDate}T00:00:00.000Z`,
            `${endDate}T23:59:59.999Z`,
          );
        }
      } else if (startDate) {
        queryBuilder = queryBuilder.andWhere(
          'people.created_at >= :startDate',
          {
            startDate: `${startDate}T00:00:00.000Z`,
          },
        );
        countBaseCondition.created_at = MoreThanOrEqual(
          `${startDate}T00:00:00.000Z`,
        );
      } else if (endDate) {
        queryBuilder = queryBuilder.andWhere('people.created_at <= :endDate', {
          endDate: `${endDate}T23:59:59.999Z`,
        });
        countBaseCondition.created_at = LessThanOrEqual(
          `${endDate}T23:59:59.999Z`,
        );
      }

      if (findHiringPersons === 'true') {
        queryBuilder = queryBuilder.andWhere('people.is_hiring = :isHiring', {
          isHiring: true,
        });
        countBaseCondition.is_hiring = true;
      }

      if (industries && industries.length > 0) {
        queryBuilder = queryBuilder.andWhere(
          'people.industry ILIKE :industry',
          {
            industry: `%${industries}%`,
          },
        );
        countBaseCondition.industry = ILike(`%${industries}%`);
      }

      // Handle email conditions
      if (findWithEmails === 'true') {
        queryBuilder = queryBuilder.andWhere('emails.id IS NOT NULL');
        countBaseCondition.emailId = Not(IsNull());
      }

      if (findWithoutEmails === 'true') {
        queryBuilder = queryBuilder.andWhere('emails.id IS NULL');
        countBaseCondition.emailId = IsNull();
      }

      // Handle search
      if (search && search.trim()) {
        queryBuilder = queryBuilder.andWhere(
          '(people.first_name ILIKE :search OR people.last_name ILIKE :search OR people.current_title ILIKE :search OR people.profile_url ILIKE :search OR people.industry ILIKE :search)',
          { search: `%${search}%` },
        );
      }

      // Get total count
      const total = await queryBuilder.getCount();

      // Get paginated results
      const people = await queryBuilder.skip(skip).take(limit).getMany();

      // For count queries, use the countBaseCondition (without email conditions)
      const sr_count = await this.peopleRepository.count({
        where: {
          ...countBaseCondition,
          sectorId: 2,
        },
      });

      const direct_count = await this.peopleRepository.count({
        where: {
          ...countBaseCondition,
          sectorId: 1,
        },
      });

      const unknown_count = await this.peopleRepository.count({
        where: {
          ...countBaseCondition,
          sectorId: null,
        },
      });

      const response = {
        persons: people,
        totalCount: total,
        sr_count,
        direct_count,
        unknown_count,
      };

      return response;
    } catch (error) {
      throw new Error(`Error getting persons: ${error.message}`);
    }
  }

  async findPersonByCompanyId(company_id: string) {
    if (!company_id) {
      throw new InternalServerErrorException('Company ID is required');
    }

    const companyIdInt = parseInt(company_id);

    try {
      const person = await this.peopleRepository.find({
        where: {
          companyId: companyIdInt,
        },
        relations: ['emails', 'company', 'country', 'sector', 'jobs'],
      });
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving person',
        error: error.message,
      });
    }
  }

  async findUniquePersonTitles() {
    try {
      const titles = await this.peopleRepository
        .createQueryBuilder('people')
        .select('DISTINCT people.current_title')
        .getRawMany();
      return titles;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving person',
        error: error.message,
      });
    }
  }

  async findPersons(searchTerm: string) {
    try {
      const people = await this.peopleRepository.find({
        where: [
          { first_name: ILike(`%${searchTerm}%`) },
          { last_name: ILike(`%${searchTerm}%`) },
          { current_title: ILike(`%${searchTerm}%`) },
          { profile_url: ILike(`%${searchTerm}%`) },
          { industry: ILike(`%${searchTerm}%`) },
        ],
        relations: ['emails', 'company', 'country', 'sector', 'jobs'],
      });
      return people;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving person',
        error: error.message,
      });
    }
  }

  async findPersonsByCompany(company_id: string, searchTerm?: string) {
    if (!company_id) {
      throw new InternalServerErrorException('Company ID is required');
    }

    const companyIdInt = parseInt(company_id);

    try {
      const query = this.peopleRepository
        .createQueryBuilder('person')
        .leftJoinAndSelect('person.emails', 'emails')
        .leftJoinAndSelect('person.company', 'company')
        .leftJoinAndSelect('person.country', 'country')
        .leftJoinAndSelect('person.sector', 'sector')
        .leftJoinAndSelect('person.jobs', 'jobs')
        .where('company.id = :companyId', { companyId: companyIdInt });

      if (searchTerm) {
        query.andWhere(
          new Brackets((qb) => {
            qb.where('person.first_name ILIKE :search', {
              search: `%${searchTerm}%`,
            })
              .orWhere('person.last_name ILIKE :search', {
                search: `%${searchTerm}%`,
              })
              .orWhere('person.current_title ILIKE :search', {
                search: `%${searchTerm}%`,
              })
              .orWhere('person.profile_url ILIKE :search', {
                search: `%${searchTerm}%`,
              })
              .orWhere('person.industry ILIKE :search', {
                search: `%${searchTerm}%`,
              });
          }),
        );
      }

      const people = await query.getMany();
      return people;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error finding persons',
        error: error.message,
      });
    }
  }

  async findOne(id: number): Promise<People> {
    const person = await this.peopleRepository.findOne({
      where: { id },
      relations: [
        'emails',
        'phones',
        'skills',
        'company',
        'country',
        'sector',
        'jobs',
      ],
    });
    if (!person) {
      throw new NotFoundException(`Person with ID ${id} not found`);
    }
    return person;
  }

  parseLinkedInDate = (str: string): Date | null => {
    if (!str || str.toLowerCase().includes('present')) return null;
    const formatted = '01 ' + str; // Convert "Oct 2016" to "01 Oct 2016"
    const date = new Date(formatted);
    return isNaN(date.getTime()) ? null : date;
  };

  async updateLiCandidate(id: number, candidate: PeopleDto) {
    // console.log("jhsdfffsdfsdfsdf", candidate)
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // First: update the People table
      await queryRunner.manager.update(People, id, {
        // full_name: candidate.full_name,
        first_name: candidate.first_name,
        last_name: candidate.last_name,
        profile_url: candidate.profile_url,
        headline: candidate.headline,
        location: candidate.location,
        current_title: candidate.current_title,
        summary: candidate.summary,
        profile_img: candidate.profile_img,
      });

      // Delete old experiences
      await queryRunner.manager.delete(Experience, { personId: id });

      if (candidate.experience?.length) {
        const experiences = candidate.experience.map((exp) => {
          const durationPart = exp.duration?.split('·')[0]?.trim() || '';
          const [startStr, endStr]: [string, string] = durationPart
            .split('-')
            .map((s: string): string => s.trim());
          return {
            company_name: exp.company_name,
            company_img: exp.company_logo,
            position: exp.job_title,
            duration: exp.duration,
            start_date: this.parseLinkedInDate(startStr),
            end_date: this.parseLinkedInDate(endStr),
            responsibilities: exp.responsibilities,
            location: exp.location,
            personId: id,
          };
        });

        await queryRunner.manager.insert(Experience, experiences);
      }

      // Delete old skills
      await queryRunner.manager.delete(PersonSkill, { personId: id });
      // Insert new skills
      if (candidate?.skills?.length) {
        const skills = candidate.skills
          .filter((skill) => skill && skill.trim())
          .map((skill) => ({
            personId: id,
            skill_name: skill.trim(),
            created_at: new Date(),
            updated_at: new Date(),
          }));
        await queryRunner.manager.insert(PersonSkill, skills);
      }

      // Delete old qualifications
      await queryRunner.manager.delete(Qualifications, { personId: id });
      if (candidate.qualifications?.length) {
        const qualifications = candidate.qualifications.map((edu) => ({
          title: edu.degree,
          qualification_type: 'DEGREE',
          institution: edu.institution,
          duration: edu.duration,
          // check if duration is in "2014 - 2014" format
          // if so, split it and take the first part as end_date
          start_date: edu.duration?.includes('-')
            ? this.parseLinkedInDate(edu.duration.split('-')[0])
            : null,
          end_date: edu.duration?.includes('-')
            ? this.parseLinkedInDate(edu.duration.split('-')[1])
            : null,
          personId: id,
        }));
        await queryRunner.manager.insert(Qualifications, qualifications);
      }

      // Delete old languages
      await queryRunner.manager.delete(Languages, { personId: id });
      if (candidate.languages?.length) {
        const langs = candidate.languages.map((lang) => ({
          personId: id,
          name: lang,
        }));
        await queryRunner.manager.insert(Languages, langs);
      }

      // add Certifications
      await queryRunner.manager.delete(Qualifications, {
        personId: id,
        qualification_type: 'CERTIFICATE',
      });
      if (candidate.certifications?.length) {
        const certifications = candidate.certifications.map((cert) => ({
          title: cert.certification_name,
          qualification_type: 'CERTIFICATE',
          institution: cert.issuer,
          description: cert.date,
          issued_by: cert.issuer,
          personId: id,
        }));
        await queryRunner.manager.insert(Qualifications, certifications);
      }

      // Delete old projects
      await queryRunner.manager.delete(Project, { personId: id });

      if (
        candidate.projects?.length &&
        candidate.projects[0].project_name !== ''
      ) {
        const projects = candidate.projects.map((project) => ({
          personId: id,
          project_name: project.project_name,
          project_description: project.project_description,
          project_duration: project.project_duration,
          project_associated_with: project.project_assocated,
          project_skill: isArray(project.project_skill)
            ? project.project_skill
            : [project.project_skill],
        }));
        await queryRunner.manager.insert(Project, projects);
      }

      await queryRunner.commitTransaction();

      // Update role candidate status if role_candidate_id is provided
      if (candidate.role_candidate_id) {
        await queryRunner.manager.update(
          RoleCandidate,
          { id: candidate.role_candidate_id },
          {
            candidate_status: 'COMPLETED',
            candidateId: candidate?.id,
          },
        );
      }

      return { message: 'Candidate updated successfully.' };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new Error(`Update failed: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async update(id: number, updatePeopleDto: UpdatePeopleDTO): Promise<People> {
    try {
      const person = await this.findOne(id);
      Object.assign(person, updatePeopleDto);
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating person',
        error: error.message,
      });
    }
  }

  async delete(id: number): Promise<void> {
    const person = await this.findOne(id);
    try {
      await this.peopleRepository.remove(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error deleting person',
        error: error.message,
      });
    }
  }

  // Client Services

  async findClientByServiceId(serviceId: number): Promise<People[]> {
    try {
      const clients = await this.peopleRepository.find({
        where: { service: { id: serviceId }, person_type: 'CLIENT' },
        relations: ['service', 'company', 'user'],
      });
      return clients;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving clients by service ID',
        error: error.message,
      });
    }
  }

  async findAllClients(
    page: number = 0,
    pageSize = 10,
    search: string = null,
    acmUserId: string = null,
    bdUserId: string = null,
    start_date: Date = null,
    end_date: Date = null,
    serviceId: number = null,
  ): Promise<People[]> {
    try {
      if (page < 0) {
        throw new NotFoundException(`Page number cannot be negative`);
      }

      const offset = page * pageSize;
      const limit = pageSize;

      const queryBuilder = this.peopleRepository
        .createQueryBuilder('people')
        .where('people.person_type = :person_type', { person_type: 'CLIENT' });

      if (start_date && end_date) {
        queryBuilder.andWhere(
          'people.created_at BETWEEN :start_date AND :end_date',
          {
            start_date,
            end_date,
          },
        );
      } else if (start_date) {
        queryBuilder.andWhere('people.created_at >= :start_date', {
          start_date,
        });
      } else if (end_date) {
        queryBuilder.andWhere('people.created_at <= :end_date', { end_date });
      }

      if (search) {
        queryBuilder.andWhere('people.first_name LIKE :search', {
          search: `%${search}%`,
        });
      }
      if (acmUserId) {
        queryBuilder.andWhere('people.acmUserId = :acmUserId', { acmUserId });
      }
      if (bdUserId) {
        queryBuilder.andWhere('people.bdUserId = :bdUserId', { bdUserId });
      }
      if (serviceId) {
        queryBuilder.andWhere('people.serviceId = :serviceId', { serviceId });
      }

      queryBuilder.skip(offset).take(limit);
      queryBuilder.orderBy('people.id', 'DESC');
      queryBuilder.leftJoinAndSelect('people.service', 'service');
      queryBuilder.leftJoinAndSelect('people.company', 'company');
      queryBuilder.leftJoinAndSelect('people.acmUser', 'acmUser');
      queryBuilder.leftJoinAndSelect('people.bdUser', 'bdUser');
      queryBuilder.leftJoinAndSelect('people.user', 'user');
      //add sector
      queryBuilder.leftJoinAndSelect('people.sector', 'sector');

      const clients = await queryBuilder.getMany();

      return clients.length > 0 ? clients : [];
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving clients',
        error: error.message,
      });
    }
  }

  async renewClient(
    id: number,
    renewClientDto: RenewClientDto,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'CLIENT' },
      });
      Object.assign(person, renewClientDto);
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error renewing client',
        error: error.message,
      });
    }
  }

  async updateClientStatus(id: number, client_status: string): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'CLIENT' },
      });
      person.client_status = client_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating client status',
        error: error.message,
      });
    }
  }

  async findClientById(clientId: number): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id: clientId, person_type: 'CLIENT' },
        relations: ['service', 'company', 'user'],
      });
      if (!person) {
        throw new NotFoundException(`Client with ID ${clientId} not found`);
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  async findClientByClientNumber(client_number: string): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { client_number, person_type: 'CLIENT' },
        relations: ['service', 'company', 'user'],
      });
      if (!person) {
        throw new NotFoundException(
          `Client with client number ${client_number} not found`,
        );
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  async getClientServiceStats(
    start_date?: Date,
    end_date?: Date,
    serviceId?: number,
    bdUserId?: string,
    acmUserId?: string,
  ): Promise<{ service_name: string; total: number }[]> {
    try {
      console.log('Fetching client service stats...');

      // Check if there are any services available
      const servicesCount = await this.serviceRepository.count();
      if (servicesCount === 0) {
        return [];
      }

      // Build query dynamically based on optional parameters
      const queryBuilder = this.serviceRepository
        .createQueryBuilder('service')
        .leftJoin(
          'service.people',
          'people',
          'people.person_type = :person_type',
          { person_type: 'CLIENT' },
        )
        .select('service.id', 'service_id') // Include service ID for better grouping
        .addSelect('service.name', 'service_name')
        .addSelect('COUNT(people.id)', 'total')
        .groupBy('service.id, service.name'); // Group by ID and name for all services

      if (serviceId) {
        queryBuilder.andWhere('service.id = :serviceId', { serviceId });
      }

      if (start_date && end_date) {
        queryBuilder.andWhere(
          'people.subscription_start_date BETWEEN :start_date AND :end_date',
          { start_date, end_date },
        );
      } else if (start_date) {
        queryBuilder.andWhere('people.subscription_start_date >= :start_date', {
          start_date,
        });
      } else if (end_date) {
        queryBuilder.andWhere('people.subscription_start_date <= :end_date', {
          end_date,
        });
      }
      if (bdUserId) {
        queryBuilder.andWhere('people.bdUserId = :bdUserId', { bdUserId });
      }
      if (acmUserId) {
        queryBuilder.andWhere('people.acmUserId = :acmUserId', { acmUserId });
      }

      const stats = await queryBuilder.getRawMany();

      return stats.length > 0 ? stats : [];
    } catch (error) {
      console.error('Error retrieving service client stats:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving service client stats',
        error: error.message,
      });
    }
  }
  // PROSPECT Services

  async findProspectByServiceId(serviceId: number): Promise<People[]> {
    try {
      const clients = await this.peopleRepository.find({
        where: { service: { id: serviceId }, person_type: 'PROSPECT' },
        relations: ['service', 'company', 'user'],
      });
      return clients;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving clients by service ID',
        error: error.message,
      });
    }
  }

  async findAllProspects(
    page: number = 0,
    pageSize = 10,
    search: string = null,
    acmUserId: string = null,
    bdUserId: string = null,
    start_date: Date = null,
    end_date: Date = null,
    serviceId: number = null,
  ): Promise<People[]> {
    try {
      if (page < 0) {
        throw new NotFoundException(`Page number cannot be negative`);
      }

      const offset = page * pageSize;
      const limit = pageSize;

      const queryBuilder = this.peopleRepository
        .createQueryBuilder('people')
        .where('people.person_type = :person_type', {
          person_type: 'PROSPECT',
        });

      if (start_date && end_date) {
        queryBuilder.andWhere(
          'people.created_at BETWEEN :start_date AND :end_date',
          {
            start_date,
            end_date,
          },
        );
      } else if (start_date) {
        queryBuilder.andWhere('people.created_at >= :start_date', {
          start_date,
        });
      } else if (end_date) {
        queryBuilder.andWhere('people.created_at <= :end_date', { end_date });
      }

      if (search) {
        const lowerSearch = `%${search.toLowerCase()}%`;
        queryBuilder.andWhere(
          `(LOWER(people.first_name) LIKE :search 
            OR LOWER(people.last_name) LIKE :search 
            OR LOWER(people.client_number) LIKE :search 
            OR LOWER(people.current_title) LIKE :search 
            OR LOWER(people.industry) LIKE :search)`,
          { search: lowerSearch },
        );
      }

      if (acmUserId) {
        queryBuilder.andWhere('people.acmUserId = :acmUserId', { acmUserId });
      }
      if (bdUserId) {
        queryBuilder.andWhere('people.bdUserId = :bdUserId', { bdUserId });
      }
      if (serviceId) {
        queryBuilder.andWhere('people.serviceId = :serviceId', { serviceId });
      }

      queryBuilder.skip(offset).take(limit);
      queryBuilder.orderBy('people.id', 'DESC');
      queryBuilder.leftJoinAndSelect('people.service', 'service');
      queryBuilder.leftJoinAndSelect('people.company', 'company');
      queryBuilder.leftJoinAndSelect('people.user', 'user');
      //ADD FOR SECTOR
      queryBuilder.leftJoinAndSelect('people.sector', 'sector');

      const prospects = await queryBuilder.getMany();

      return prospects.length > 0 ? prospects : [];
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving prospects',
        error: error.message,
      });
    }
  }

  async renewProspect(
    id: number,
    renewClientDto: RenewClientDto,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'PROSPECT' },
      });
      Object.assign(person, renewClientDto);
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error renewing client',
        error: error.message,
      });
    }
  }

  async updateProspectStatus(
    id: number,
    client_status: string,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'PROSPECT' },
      });
      person.client_status = client_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating PROSPECT status',
        error: error.message,
      });
    }
  }

  async findProspectById(clientId: number): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id: clientId, person_type: 'PROSPECT' },
        relations: ['service', 'company', 'user'],
      });
      if (!person) {
        throw new NotFoundException(`Client with ID ${clientId} not found`);
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  async findProspectByClientNumber(client_number: string): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { client_number, person_type: 'PROSPECT' },
        relations: ['service', 'company', 'user'],
      });
      if (!person) {
        throw new NotFoundException(
          `Client with client number ${client_number} not found`,
        );
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  async getProspectServiceStats(
    start_date?: Date,
    end_date?: Date,
    serviceId?: number,
    bdUserId?: string,
    acmUserId?: string,
  ): Promise<{ service_name: string; total: number }[]> {
    try {
      console.log('Fetching client service stats...');

      // Check if there are any services available
      const servicesCount = await this.serviceRepository.count();
      if (servicesCount === 0) {
        return [];
      }

      // Build query dynamically based on optional parameters
      const queryBuilder = this.serviceRepository
        .createQueryBuilder('service')
        .leftJoin(
          'service.people',
          'people',
          'people.person_type = :person_type',
          { person_type: 'PROSPECT' },
        )
        .select('service.id', 'service_id') // Include service ID for better grouping
        .addSelect('service.name', 'service_name')
        .addSelect('COUNT(people.id)', 'total')
        .groupBy('service.id, service.name'); // Group by ID and name for all services

      if (serviceId) {
        queryBuilder.andWhere('service.id = :serviceId', { serviceId });
      }

      if (start_date && end_date) {
        queryBuilder.andWhere(
          'people.subscription_start_date BETWEEN :start_date AND :end_date',
          { start_date, end_date },
        );
      } else if (start_date) {
        queryBuilder.andWhere('people.subscription_start_date >= :start_date', {
          start_date,
        });
      } else if (end_date) {
        queryBuilder.andWhere('people.subscription_start_date <= :end_date', {
          end_date,
        });
      }
      if (bdUserId) {
        queryBuilder.andWhere('people.bdUserId = :bdUserId', { bdUserId });
      }
      if (acmUserId) {
        queryBuilder.andWhere('people.acmUserId = :acmUserId', { acmUserId });
      }
      const stats = await queryBuilder.getRawMany();

      return stats.length > 0 ? stats : [];
    } catch (error) {
      console.error('Error retrieving service PROSPECT stats:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving service PROSPECT stats',
        error: error.message,
      });
    }
  }

  async getProspectSubscriptionStatus(
    client_status: string,
  ): Promise<People[]> {
    try {
      const prospects = await this.peopleRepository.find({
        where: { client_status, person_type: 'PROSPECT' },
        relations: ['service', 'company', 'user'],
      });
      return prospects;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving prospects by status',
        error: error.message,
      });
    }
  }

  async updateProspectSubscriptionStatusById(
    id: number,
    client_status: string,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'PROSPECT' },
      });
      person.client_status = client_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating PROSPECT status',
        error: error.message,
      });
    }
  }

  async updateProspectSubscriptionStatusByClientNumber(
    client_number: string,
    client_status: string,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { client_number, person_type: 'PROSPECT' },
      });
      person.client_status = client_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating PROSPECT status',
        error: error.message,
      });
    }
  }
  async findProspectByClientNumberAndServiceId(
    client_number: string,
    serviceId: number,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: {
          client_number,
          service: { id: serviceId },
          person_type: 'PROSPECT',
        },
        relations: ['service', 'company', 'user'],
      });
      if (!person) {
        throw new NotFoundException(
          `Client with client number ${client_number} not found`,
        );
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  // prospect_status games
  async updateProspectStatusById(
    id: number,
    prospect_status: string,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { id, person_type: 'PROSPECT' },
      });
      person.prospect_status = prospect_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating PROSPECT status',
        error: error.message,
      });
    }
  }

  async getProspectByProspectStatus(prospect_status: string): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { prospect_status, person_type: 'PROSPECT' },
      });
      if (!person) {
        throw new NotFoundException(
          `Client with client number ${prospect_status} not found`,
        );
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving client',
        error: error.message,
      });
    }
  }

  async updateProspectStatusByClientNumberAndServiceId(
    client_number: string,
    serviceId: number,
    prospect_status: string,
  ): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: {
          client_number,
          service: { id: serviceId },
          person_type: 'PROSPECT',
        },
      });
      person.prospect_status = prospect_status;
      return await this.peopleRepository.save(person);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating PROSPECT status',
        error: error.message,
      });
    }
  }

  async getPersonByUserId(userId: string): Promise<People> {
    try {
      const person = await this.peopleRepository.findOne({
        where: { userId },
        relations: ['skills', 'qualifications', 'experiences', 'languages'],
      });
      if (!person) {
        throw new NotFoundException(`Person with user ID ${userId} not found`);
      }
      return person;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving person',
        error: error.message,
      });
    }
  }

  async upsertPerson(PeopleDto: PeopleDto): Promise<People> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let person = await this.peopleRepository.findOne({
        where: { userId: PeopleDto.userId },
      });

      if (person) {
        Object.assign(person, PeopleDto);
        person = await queryRunner.manager.save(person);
      } else {
        person = this.peopleRepository.create(PeopleDto);
        person = await queryRunner.manager.save(person);

        // Bulk insert skills
        if (PeopleDto.skills?.length) {
          const personSkills = PeopleDto.skills.map((skill) => ({
            skill_name: skill.skill_name,
            proficiency_level: skill.proficiency_level,
            personId: person.id,
          }));
          await queryRunner.manager
            .getRepository(this.personSkillRepository.target)
            .insert(personSkills);
        }

        // Bulk insert qualifications
        if (PeopleDto.qualifications?.length) {
          const personQualifications = PeopleDto.qualifications.map(
            (qualification) => ({
              title: qualification.title,
              qualification_type: qualification.qualification_type,
              start_date: qualification.start_date,
              end_date: qualification.end_date,
              institution: qualification.institution,
              description: qualification.description,
              is_verified: qualification.is_verified,
              is_current: qualification.is_current,
              date_expiry: qualification.date_expiry,
              issued_by: qualification.issued_by,
              personId: person.id,
            }),
          );
          await queryRunner.manager
            .getRepository(this.qualificationsRepository.target)
            .insert(personQualifications);
        }

        // Bulk insert experience
        if (PeopleDto.experience?.length) {
          const personExperiences = PeopleDto.experience.map((experience) => ({
            company_name: experience.company_name,
            position: experience.position,
            start_date: experience.start_date,
            end_date: experience.end_date,
            personId: person.id,
          }));
          await queryRunner.manager
            .getRepository(this.experienceRepository.target)
            .insert(personExperiences);
        }
      }

      // Bulk insert languages
      if (PeopleDto.languages?.length) {
        const personLanguages = PeopleDto.languages.map((language) => ({
          language_name: language.language_name,
          proficiency_level: language.proficiency_level,
          personId: person.id,
        }));
        await queryRunner.manager
          .getRepository(this.languagesRepository.target)
          .insert(personLanguages);
      }

      await queryRunner.commitTransaction();
      return person;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new InternalServerErrorException({
        message: 'Error upserting person',
        error: error.message,
      });
    } finally {
      await queryRunner.release();
    }
  }

  async getPersonByPersonType(person_type: PersonType): Promise<People[]> {
    try {
      const people = await this.peopleRepository.find({
        where: { person_type },
      });
      return people;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching people by person type',
        error: error.message,
      });
    }
  }

  async getPartiallyIntrestedPeople(): Promise<{ [key: string]: People[] }> {
    try {
      const partially_interested = [
        ProspectStatus.BASIC_INFO,
        ProspectStatus.RECIEVED,
        ProspectStatus.TEMPLATE_SENT,
        ProspectStatus.IN_PROCESS,
        ProspectStatus.RESULTS_RECIEVED,
        ProspectStatus.SENT,
      ];

      const people = await this.peopleRepository.find({
        where: { prospect_status: In(partially_interested) },
      });

      // Initialize response with empty arrays for all statuses
      const groupedByStatus: { [key: string]: People[] } =
        partially_interested.reduce(
          (acc, status) => {
            acc[status] = [];
            return acc;
          },
          {} as { [key: string]: People[] },
        );

      // Populate with actual data
      people.forEach((person) => {
        groupedByStatus[person.prospect_status].push(person);
      });

      return groupedByStatus;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving partially interested people',
        error: error.message,
      });
    }
  }

  async getTrialProspects(): Promise<{ [key: string]: People[] }> {
    try {
      const partially_interested = [
        ProspectStatus.SENT,
        ProspectStatus.RETRIAL,
        ProspectStatus.SUCCESS,
        ProspectStatus.FAILED,
      ];

      const people = await this.peopleRepository.find({
        where: { prospect_status: In(partially_interested) },
      });

      // Initialize response with empty arrays for all statuses
      const groupedByStatus: { [key: string]: People[] } =
        partially_interested.reduce(
          (acc, status) => {
            acc[status] = [];
            return acc;
          },
          {} as { [key: string]: People[] },
        );

      people.forEach((person) => {
        groupedByStatus[person.prospect_status].push(person);
      });

      return groupedByStatus;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving partially interested people',
        error: error.message,
      });
    }
  }
  async getInFutureProspects(
    page: number = 0,
    pageSize: number = 10,
    search: string = null,
  ): Promise<{ [key: string]: People[] }> {
    try {
      const partially_interested = [ProspectStatus.INFUTURE];

      const queryBuilder = this.peopleRepository.createQueryBuilder('people');

      if (search) {
        queryBuilder.where('people.name LIKE :search', {
          search: `%${search}%`,
        });
      }

      queryBuilder.andWhere('people.prospect_status IN (:...statuses)', {
        statuses: partially_interested,
      });

      queryBuilder.skip(page * pageSize).take(pageSize);

      const people = await queryBuilder.getMany();

      // Initialize response with empty arrays for all statuses
      const groupedByStatus: { [key: string]: People[] } =
        partially_interested.reduce(
          (acc, status) => {
            acc[status] = [];
            return acc;
          },
          {} as { [key: string]: People[] },
        );

      // Populate with actual data
      people.forEach((person) => {
        groupedByStatus[person.prospect_status].push(person);
      });

      return groupedByStatus;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving partially interested people',
        error: error.message,
      });
    }
  }
  async getConvertedProspects(
    page: number = 0,
    pageSize: number = 10,
    search: string = null,
  ): Promise<{ [key: string]: People[] }> {
    try {
      const converted = [ProspectStatus.CONVERTED];

      const queryBuilder = this.peopleRepository.createQueryBuilder('people');

      if (search) {
        queryBuilder.where('people.name LIKE :search', {
          search: `%${search}%`,
        });
      }

      queryBuilder.andWhere('people.prospect_status IN (:...statuses)', {
        statuses: converted,
      });

      queryBuilder.skip(page * pageSize).take(pageSize);

      const people = await queryBuilder.getMany();

      // Initialize response with empty arrays for all statuses
      const groupedByStatus: { [key: string]: People[] } = converted.reduce(
        (acc, status) => {
          acc[status] = [];
          return acc;
        },
        {} as { [key: string]: People[] },
      );

      // Populate with actual data
      people.forEach((person) => {
        groupedByStatus[person.prospect_status].push(person);
      });

      return groupedByStatus;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving partially interested people',
        error: error.message,
      });
    }
  }

  // Person Type Candidate
  async candidateStatsBySource(roleId: number): Promise<
    {
      source: string;
      total: number;
    }[]
  > {
    try {
      const stats = await this.peopleRepository
        .createQueryBuilder('people')
        .select('people.profile_source', 'source')
        .addSelect('COUNT(people.id)', 'total')
        .where('people.person_type = :person_type', {
          person_type: 'CANDIDATE',
        })
        // .andWhere('"people"."roleId" = :roleId', { roleId })
        .groupBy('people.profile_source')
        .getRawMany();

      return stats.length > 0 ? stats : [];
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error retrieving candidate stats by source',
        error: error.message,
      });
    }
  }
  
  async addPeopleInBulk(peopleDtos: PeopleDto[]) {
    const [dbPeople, dbCompanies] = await Promise.all([
      this.peopleRepository.find({
        where: { person_type: 'JOB_POST_LEAD' },
        relations: ['experiences', 'company'],
      }),
      this.companyRepository.find(),
    ]);

    const personByProfileUrl = new Map(
      dbPeople.filter((p) => p.profile_url).map((p) => [p.profile_url, p]),
    );
    const personByName = new Map(
      dbPeople.map((p) => [
        `${p.first_name?.trim().toLowerCase()}_${p.last_name?.trim().toLowerCase()}`,
        p,
      ]),
    );
    const companyById = new Map(dbCompanies.map((c) => [c.id, c]));
    const companyByName = new Map(
      dbCompanies.map((c) => [c.name?.trim().toLowerCase(), c]),
    );

    const newPersons = [];
    const existedPerson = [];
    const existedPersonNoUpdated = [];

    for (const dto of peopleDtos) {
      if (!dto.profile_url && (!dto.first_name || !dto.last_name)) {
        continue;
      }

      let person = dto.profile_url
        ? personByProfileUrl.get(dto.profile_url)
        : personByName.get(
            `${dto.first_name?.trim().toLowerCase()}_${dto.last_name?.trim().toLowerCase()}`,
          );

      if (person) {
        const dbExp = person.experiences?.[0];
        const dtoExp = dto.experience?.[0];

        let needsUpdate = false;
        if (dtoExp && dbExp) {
          if (
            dtoExp.company_name !== dbExp.company_name ||
            dtoExp.position !== dbExp.position ||
            new Date(dtoExp.start_date) > new Date(dbExp.end_date)
          ) {
            needsUpdate = true;
          }
        } else if (dtoExp && !dbExp) {
          needsUpdate = true;
        }

        if (needsUpdate) {
          Object.assign(person, dto);
          await this.peopleRepository.save(person);
          existedPerson.push(person);
        } else {
          existedPersonNoUpdated.push(person);
        }
      } else {
        let company = null;
        if (dto.companyId) {
          company = companyById.get(dto.companyId);
          if (!company) {
            const companyData: Partial<Company> = {
              name: ' ',
              scrapper_level: 2,
              company_source: 'MANUAL',
            };
            company = this.companyRepository.create(companyData);
            await this.companyRepository.save(company);
            companyById.set(company.id, company);
          }
        }

        const newPerson = this.peopleRepository.create({
          ...dto,
          company: company || undefined,
          experiences: dto.experience || [],
          person_type: 'JOB_POST_LEAD',
        });
        await this.peopleRepository.save(newPerson);
        newPersons.push(newPerson);
      }
    }

    return {
      message: `New ${newPersons.length} created successfully, ${existedPerson.length} updated successfully and ${existedPersonNoUpdated.length} persons already have exists.`,
    };
  }

  async importPeopleFromCsv(filePath: string) {
    try {
      const csvRows: any[] = await this.parseCsv(filePath);

      // 2. Fetch all people and companies for fast lookup
      const [dbPeople, dbCompanies] = await Promise.all([
        this.peopleRepository.find({
          where: { person_type: PersonType.CSV_UPLOAD },
          relations: ['company', 'experiences'],
        }),
        this.companyRepository.find(),
      ]);
      const personByProfileUrl = new Map(
        dbPeople.filter((p) => p.profile_url).map((p) => [p.profile_url, p]),
      );
      const companyByProfileUrl = new Map(
        dbCompanies.filter((c) => c.profile_url).map((c) => [c.profile_url, c]),
      );

      // 3. Track results
      const newPersons = [];
      const updatedPersons = [];
      const unchangedPersons = [];

      function isEmptyRow(row: Record<string, any>): boolean {
        return Object.values(row).every(
          (value) =>
            value === undefined ||
            value === null ||
            String(value).trim() === '',
        );
      }

      // 4. Iterate through CSV rows
      for (const row of csvRows) {
        if (isEmptyRow(row)) continue;
        const profile_url = row['profile_url']?.trim();
        if (!profile_url) continue;

        // Map CSV to People fields
        const peopleData = {
          first_name: row['first_name'],
          last_name: row['last_name'],
          full_name: row['full_name'],
          current_title: row['headline'],
          profile_img: row['avatar'],
          headline: row['headline'],
          profile_url: profile_url,
          location: row['location_name'],
          summary: row['summary'],
          industry: row['industry'],
          person_type: PersonType.CSV_UPLOAD,
          profile_source: PersonSource.CSV_UPLOAD,
        };

        // Map organizations to Experience entities
        const experiences: Partial<Experience>[] = [];
        for (let i = 1; i <= 3; i++) {
          // only first 3 experiences
          if (row[`organization_${i}`]) {
            experiences.push({
              company_name: row[`organization_${i}`],
              position: row[`organization_title_${i}`],
              start_date: row[`organization_start_${i}`]
                ? new Date(row[`organization_start_${i}`])
                : null,
              end_date: row[`organization_end_${i}`]
                ? new Date(row[`organization_end_${i}`])
                : null,
              responsibilities: row[`organization_description_${i}`],
              location: row[`organization_location_${i}`],
              company_img: null,
            });
          }
        }

        // Company info from first org
        const companyProfileUrl = row['organization_url_1']?.trim();
        let company = null;
        if (companyProfileUrl) {
          company = companyByProfileUrl.get(companyProfileUrl);
          if (!company) {
            // Create new company if not exists
            company = this.companyRepository.create({
              name: row['organization_1'],
              profile_url: companyProfileUrl,
            });
            company = await this.companyRepository.save(company);
            companyByProfileUrl.set(companyProfileUrl, company);
          }
        }

        const dbPerson = personByProfileUrl.get(profile_url);

        if (dbPerson) {
          // Check if organisation (company_name) matches
          const dbFirstOrg = dbPerson.experiences?.[0]?.company_name;
          const csvFirstOrg = experiences[0]?.company_name;

          if (dbFirstOrg === csvFirstOrg) {
            unchangedPersons.push(dbPerson);
          } else {
            const csvOtherOrgs = [
              experiences[1]?.company_name,
              experiences[2]?.company_name,
            ];
            if (csvOtherOrgs.includes(dbFirstOrg)) {
              Object.assign(dbPerson, peopleData);
              dbPerson.company = company || null;

              // Update experiences
              dbPerson.experiences = experiences.map((exp) =>
                this.experienceRepository.create(exp),
              );
              await this.peopleRepository.save(dbPerson);
              updatedPersons.push(dbPerson);
            } else {
              unchangedPersons.push(dbPerson);
            }
          }
        } else {
          const newPerson = this.peopleRepository.create({
            ...peopleData,
            company: company || null,
            experiences: experiences.map((exp) =>
              this.experienceRepository.create(exp),
            ),
          });
          await this.peopleRepository.save(newPerson);
          newPersons.push(newPerson);
        }
      }

      return {
        message: `New ${newPersons.length} created successfully, ${updatedPersons.length} updated successfully and ${unchangedPersons.length} persons already exist.`,
      };
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error.message);
    }
  }

  // Helper: CSV file parser
  private async parseCsv(filePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const rows = [];
      fs.createReadStream(filePath)
        .pipe(csv.parse({ columns: true, skip_empty_lines: true }))
        .on('data', (row) => rows.push(row))
        .on('end', () => resolve(rows))
        .on('error', reject);
    });
  }

  async deleteAll(): Promise<void> {
    const people = await this.peopleRepository.find();
    if (people.length === 0) {
      throw new NotFoundException('No people found.');
    }
    await this.peopleRepository.remove(people);
  }

  async bdDashboard() {
    try {
      const totalProspects = await this.peopleRepository.count({
        where: { person_type: PersonType.PROSPECT },
      });

      const srProspects = await this.peopleRepository.count({
        where: { person_type: PersonType.PROSPECT, sectorId: 2 },
      });

      const directProspects = await this.peopleRepository.count({
        where: { person_type: PersonType.PROSPECT, sectorId: 1 },
      });

      const sectorWiseProspects = {
        total: totalProspects,
        direct: directProspects,
        sr: srProspects,
      };

      const trialRecieved = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.RECIEVED,
        },
      });
      const trialSent = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.SENT,
        },
      });
      const trialInProcess = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.IN_PROCESS,
        },
      });
      const trialResultsRecieved = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.RESULTS_RECIEVED,
        },
      });
      const trialPending = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.WAITING_RESPONSE,
        },
      });
      const trialSuccessfull = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.SUCCESS,
        },
      });
      const trialFailed = await this.peopleRepository.count({
        where: {
          person_type: PersonType.PROSPECT,
          prospect_status: ProspectStatus.FAILED,
        },
      });

      const prospectsOverview = {
        trialRecieved,
        trialSent,
        trialInProcess,
        trialResultsRecieved,
        trialPending,
        trialSuccessfull,
        trialFailed,
      };

      const now = new Date();
      const dayOfWeek = now.getDay();
      const diffToMonday = (dayOfWeek + 6) % 7;
      const startOfThisWeek = new Date(now);
      startOfThisWeek.setDate(now.getDate() - diffToMonday);
      startOfThisWeek.setHours(0, 0, 0, 0);

      const startOfLastWeek = new Date(startOfThisWeek);
      startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);

      const endOfLastWeek = new Date(startOfThisWeek);
      endOfLastWeek.setMilliseconds(-1);

      const [
        currentTotal,
        lastWeekTotal,
        currentAutoReply,
        lastAutoReply,
        currentBounce,
        lastBounce,
        currentPotential,
        lastPotential,
      ] = await Promise.all([
        this.mailBoxRepository.count({
          where: { created_at: Between(startOfThisWeek, now) },
        }),
        this.mailBoxRepository.count({
          where: { created_at: Between(startOfLastWeek, endOfLastWeek) },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Auto-Reply',
            created_at: Between(startOfThisWeek, now),
          },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Auto-Reply',
            created_at: Between(startOfLastWeek, endOfLastWeek),
          },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Bounce Back',
            created_at: Between(startOfThisWeek, now),
          },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Bounce Back',
            created_at: Between(startOfLastWeek, endOfLastWeek),
          },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Potential Reply',
            created_at: Between(startOfThisWeek, now),
          },
        }),
        this.mailBoxRepository.count({
          where: {
            category: 'Potential Reply',
            created_at: Between(startOfLastWeek, endOfLastWeek),
          },
        }),
      ]);

      const emailsData = {
        totalEmails: {
          current: currentTotal,
          lastWeek: lastWeekTotal,
          percentChange: calcPercent(currentTotal, lastWeekTotal),
        },
        autoRepliesEmails: {
          current: currentAutoReply,
          lastWeek: lastAutoReply,
          percentChange: calcPercent(currentAutoReply, lastAutoReply),
        },
        bouncedEmails: {
          current: currentBounce,
          lastWeek: lastBounce,
          percentChange: calcPercent(currentBounce, lastBounce),
        },
        potentialReplies: {
          current: currentPotential,
          lastWeek: lastPotential,
          percentChange: calcPercent(currentPotential, lastPotential),
        },
      };

      return {
        sectorWiseProspects,
        prospectsOverview,
        emailsData,
      };
    } catch (error) {
      console.log(error);
      throw new InternalServerErrorException({
        message: 'Error retrieving candidate stats by source',
        error: error.message,
      });
    }
  }



  async searchCandidatesWithBooleanQuery(
    query: string,
  ): Promise<{ message: string; results: People[] }> {
    const tsQuery = convertBooleanToSafeTsQuery(query);

    try {
      const candidates = await this.peopleRepository
        .createQueryBuilder('people')
        .where(
          `to_tsvector('english',
        coalesce(people.first_name, '') || ' ' ||
        coalesce(people.last_name, '') || ' ' ||
        coalesce(people.current_title, '') || ' ' ||
        coalesce(people.summary, '') || ' ' ||
        coalesce(people.cv_text, '') || ' ' ||
        coalesce(people.domain, '') || ' ' ||
        coalesce(people.industry, '')
      ) @@ to_tsquery(:query)`,
          { query: tsQuery },
        )
        .andWhere('people.person_type = :type', { type: 'CANDIDATE' })
        .getMany();

      if (candidates.length === 0) {
        return { message: 'No matching candidates found.', results: [] };
      }

      return {
        message: 'Candidates retrieved successfully.',
        results: candidates,
      };
    } catch (error) {
      throw new Error(`Invalid query: ${error.message}`);
    }
  }

  async searchCandidatesWithAdvancedFilters(
    query?: string,
    filters?: {
      titles: string[];
      locations: string[];
      skills: string[];
      companies: string[];
      industries: string[];
      keywords: string[];
      seniority: string[];
      hideViewed: boolean;
    },
    page: number = 1,
    pageSize: number = 20,
  ): Promise<{
    message: string;
    results: People[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      const queryBuilder = this.peopleRepository
        .createQueryBuilder('people')
        .leftJoinAndSelect('people.skills', 'skills')
        .leftJoinAndSelect('people.experiences', 'experiences')
        .leftJoinAndSelect('people.company', 'company')
        .where('people.person_type = :type', { type: 'CANDIDATE' });

      // Boolean string search
      if (query && query.trim() !== '') {
        const tsQuery = convertBooleanToSafeTsQuery(query);
        queryBuilder.andWhere(
          `to_tsvector('english',
            coalesce(people.first_name, '') || ' ' ||
            coalesce(people.last_name, '') || ' ' ||
            coalesce(people.current_title, '') || ' ' ||
            coalesce(people.headline, '') || ' ' ||
            coalesce(people.summary, '') || ' ' ||
            coalesce(people.cv_text, '') || ' ' ||
            coalesce(people.domain, '') || ' ' ||
            coalesce(people.industry, '')
          ) @@ to_tsquery(:query)`,
          { query: tsQuery },
        );
      }

      // Title filters (search in both current_title and headline)
      if (filters?.titles && filters.titles.length > 0) {
        const titleConditions = filters.titles.map((title, index) =>
          `(LOWER(people.current_title) LIKE :title${index} OR LOWER(people.headline) LIKE :titleHeadline${index})`
        ).join(' OR ');

        queryBuilder.andWhere(`(${titleConditions})`);

        filters.titles.forEach((title, index) => {
          const lowerTitle = `%${title.toLowerCase()}%`;
          queryBuilder.setParameter(`title${index}`, lowerTitle);
          queryBuilder.setParameter(`titleHeadline${index}`, lowerTitle);
        });
      }

      // Location filters
      if (filters?.locations && filters.locations.length > 0) {
        const locationConditions = filters.locations.map((location, index) =>
          `LOWER(people.location) LIKE :location${index}`
        ).join(' OR ');

        queryBuilder.andWhere(`(${locationConditions})`);

        filters.locations.forEach((location, index) => {
          queryBuilder.setParameter(`location${index}`, `%${location.toLowerCase()}%`);
        });
      }

      // Skills filters
      if (filters?.skills && filters.skills.length > 0) {
        const skillConditions = filters.skills.map((skill, index) =>
          `LOWER(skills.skill_name) LIKE :skill${index}`
        ).join(' OR ');

        queryBuilder.andWhere(`(${skillConditions})`);

        filters.skills.forEach((skill, index) => {
          queryBuilder.setParameter(`skill${index}`, `%${skill.toLowerCase()}%`);
        });
      }

      // Company filters
      if (filters?.companies && filters.companies.length > 0) {
        const companyConditions = filters.companies.map((companyName, index) =>
          `LOWER(company.name) LIKE :company${index} OR LOWER(experiences.company_name) LIKE :companyExp${index}`
        ).join(' OR ');

        queryBuilder.andWhere(`(${companyConditions})`);

        filters.companies.forEach((companyName, index) => {
          queryBuilder.setParameter(`company${index}`, `%${companyName.toLowerCase()}%`);
          queryBuilder.setParameter(`companyExp${index}`, `%${companyName.toLowerCase()}%`);
        });
      }

      // Industry filters
      if (filters?.industries && filters.industries.length > 0) {
        const industryConditions = filters.industries.map((industry, index) =>
          `LOWER(people.industry) LIKE :industry${index}`
        ).join(' OR ');

        queryBuilder.andWhere(`(${industryConditions})`);

        filters.industries.forEach((industry, index) => {
          queryBuilder.setParameter(`industry${index}`, `%${industry.toLowerCase()}%`);
        });
      }

      // Keywords filters (search across multiple fields)
      if (filters?.keywords && filters.keywords.length > 0) {
        const keywordConditions = filters.keywords.map((keyword, index) =>
          `(LOWER(people.summary) LIKE :keyword${index} OR
            LOWER(people.cv_text) LIKE :keywordCv${index} OR
            LOWER(experiences.job_description) LIKE :keywordExp${index})`
        ).join(' OR ');

        queryBuilder.andWhere(`(${keywordConditions})`);

        filters.keywords.forEach((keyword, index) => {
          const lowerKeyword = `%${keyword.toLowerCase()}%`;
          queryBuilder.setParameter(`keyword${index}`, lowerKeyword);
          queryBuilder.setParameter(`keywordCv${index}`, lowerKeyword);
          queryBuilder.setParameter(`keywordExp${index}`, lowerKeyword);
        });
      }

      // Seniority filters (based on title analysis)
      if (filters?.seniority && filters.seniority.length > 0) {
        const seniorityConditions = filters.seniority.map((level, index) => {
          switch (level.toLowerCase()) {
            case 'entry level':
              return `(LOWER(people.current_title) LIKE '%junior%' OR
                      LOWER(people.current_title) LIKE '%entry%' OR
                      LOWER(people.current_title) LIKE '%trainee%' OR
                      LOWER(people.current_title) LIKE '%intern%')`;
            case 'junior':
              return `LOWER(people.current_title) LIKE '%junior%'`;
            case 'mid-level':
              return `(LOWER(people.current_title) NOT LIKE '%junior%' AND
                      LOWER(people.current_title) NOT LIKE '%senior%' AND
                      LOWER(people.current_title) NOT LIKE '%lead%' AND
                      LOWER(people.current_title) NOT LIKE '%manager%' AND
                      LOWER(people.current_title) NOT LIKE '%director%')`;
            case 'senior':
              return `LOWER(people.current_title) LIKE '%senior%'`;
            case 'lead':
              return `LOWER(people.current_title) LIKE '%lead%'`;
            case 'manager':
              return `LOWER(people.current_title) LIKE '%manager%'`;
            case 'director':
              return `LOWER(people.current_title) LIKE '%director%'`;
            case 'executive':
              return `(LOWER(people.current_title) LIKE '%ceo%' OR
                      LOWER(people.current_title) LIKE '%cto%' OR
                      LOWER(people.current_title) LIKE '%cfo%' OR
                      LOWER(people.current_title) LIKE '%executive%')`;
            default:
              return `LOWER(people.current_title) LIKE :seniority${index}`;
          }
        }).join(' OR ');

        queryBuilder.andWhere(`(${seniorityConditions})`);

        filters.seniority.forEach((level, index) => {
          if (!['entry level', 'junior', 'mid-level', 'senior', 'lead', 'manager', 'director', 'executive'].includes(level.toLowerCase())) {
            queryBuilder.setParameter(`seniority${index}`, `%${level.toLowerCase()}%`);
          }
        });
      }

      // Hide viewed candidates (this would require a separate table to track views)
      // For now, we'll add a placeholder condition
      if (filters?.hideViewed) {
        // TODO: Implement viewed candidates tracking
        // queryBuilder.andWhere('people.id NOT IN (SELECT candidate_id FROM viewed_candidates WHERE user_id = :userId)', { userId });
      }

      // Get total count for pagination
      const totalCount = await queryBuilder.getCount();

      // Apply pagination
      const offset = (page - 1) * pageSize;
      queryBuilder.skip(offset).take(pageSize);

      // Order by relevance (you can customize this)
      queryBuilder.orderBy('people.updated_at', 'DESC');

      const candidates = await queryBuilder.getMany();

      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        message: candidates.length > 0 ? 'Candidates retrieved successfully.' : 'No matching candidates found.',
        results: candidates,
        total: totalCount,
        page,
        pageSize,
        totalPages,
      };
    } catch (error) {
      throw new Error(`Error searching candidates: ${error.message}`);
    }
  }
}

// Place this function outside the PeopleService class
function convertBooleanToSafeTsQuery(input: string): string {
  if (!input || input.trim() === '') {
    return '';
  }

  try {
    // Parse the boolean expression and convert to PostgreSQL tsquery format
    return parseBooleanExpression(input.trim());
  } catch (error) {
    // Fallback to simple word search if parsing fails
    console.warn('Failed to parse boolean expression, falling back to simple search:', error.message);
    const words = input
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0)
      .map(word => word.replace(/[^\w]/g, ''));

    return words.length > 0 ? words.join(' & ') : '';
  }
}

function parseBooleanExpression(input: string): string {
  // Tokenize the input
  const tokens = tokenize(input);

  // Parse the tokens into an expression tree
  const expression = parseExpression(tokens);

  // Convert the expression tree to PostgreSQL tsquery format
  return expressionToTsQuery(expression);
}

function tokenize(input: string): Array<{type: string, value: string}> {
  const tokens = [];
  let i = 0;

  while (i < input.length) {
    const char = input[i];

    // Skip whitespace
    if (/\s/.test(char)) {
      i++;
      continue;
    }

    // Handle quoted strings
    if (char === '"') {
      let value = '';
      i++; // Skip opening quote
      while (i < input.length && input[i] !== '"') {
        value += input[i];
        i++;
      }
      if (i < input.length) i++; // Skip closing quote
      tokens.push({type: 'TERM', value: value.trim()});
      continue;
    }

    // Handle parentheses
    if (char === '(') {
      tokens.push({type: 'LPAREN', value: char});
      i++;
      continue;
    }

    if (char === ')') {
      tokens.push({type: 'RPAREN', value: char});
      i++;
      continue;
    }

    // Handle operators and terms
    let value = '';
    while (i < input.length && !/[\s()"]/.test(input[i])) {
      value += input[i];
      i++;
    }

    if (value.toLowerCase() === 'and') {
      tokens.push({type: 'AND', value: 'and'});
    } else if (value.toLowerCase() === 'or') {
      tokens.push({type: 'OR', value: 'or'});
    } else if (value.trim()) {
      tokens.push({type: 'TERM', value: value.trim()});
    }
  }

  return tokens;
}

function parseExpression(tokens: Array<{type: string, value: string}>): any {
  let index = 0;

  function parseOr(): any {
    let left = parseAnd();

    while (index < tokens.length && tokens[index].type === 'OR') {
      index++; // consume OR
      const right = parseAnd();
      left = {type: 'OR', left, right};
    }

    return left;
  }

  function parseAnd(): any {
    let left = parsePrimary();

    while (index < tokens.length && tokens[index].type === 'AND') {
      index++; // consume AND
      const right = parsePrimary();
      left = {type: 'AND', left, right};
    }

    return left;
  }

  function parsePrimary(): any {
    if (index >= tokens.length) {
      throw new Error('Unexpected end of expression');
    }

    const token = tokens[index];

    if (token.type === 'LPAREN') {
      index++; // consume (
      const expr = parseOr();
      if (index >= tokens.length || tokens[index].type !== 'RPAREN') {
        throw new Error('Missing closing parenthesis');
      }
      index++; // consume )
      return expr;
    }

    if (token.type === 'TERM') {
      index++;
      return {type: 'TERM', value: token.value};
    }

    throw new Error(`Unexpected token: ${token.type}`);
  }

  return parseOr();
}

function expressionToTsQuery(expr: any): string {
  if (expr.type === 'TERM') {
    // Handle multi-word terms by joining with &
    const words = expr.value
      .split(/\s+/)
      .filter((word: string) => word.length > 0)
      .map((word: string) => word.replace(/[^\w]/g, ''))
      .filter((word: string) => word.length > 0);

    if (words.length === 0) return '';
    if (words.length === 1) return words[0];
    return words.join(' & ');
  }

  if (expr.type === 'AND') {
    const left = expressionToTsQuery(expr.left);
    const right = expressionToTsQuery(expr.right);
    if (!left || !right) return left || right;
    return `(${left}) & (${right})`;
  }

  if (expr.type === 'OR') {
    const left = expressionToTsQuery(expr.left);
    const right = expressionToTsQuery(expr.right);
    if (!left || !right) return left || right;
    return `(${left}) | (${right})`;
  }

  throw new Error(`Unknown expression type: ${expr.type}`);
}