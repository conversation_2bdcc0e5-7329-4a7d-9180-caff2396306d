import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Experience } from './experience.entity';
import { Repository } from 'typeorm';
import { ExperienceDto } from './dto/experience.dto';
import { UpdateExperienceDto } from './dto/updateExperience.dto';

@Injectable()
export class ExperienceService {
  constructor(
    @InjectRepository(Experience)
    private readonly experienceRepository: Repository<Experience>,
  ) {}

  async createExperience(experienceDto: ExperienceDto): Promise<Experience> {
    const experience = this.experienceRepository.create(experienceDto);
    return await this.experienceRepository.save(experience);
  }

  async getAllExperiences(): Promise<Experience[]> {
    return await this.experienceRepository.find();
  }

  async getExperienceById(id: number): Promise<Experience> {
    return await this.experienceRepository.findOneOrFail({ where: { id } });
  }

  async updateExperience(
    id: number,
    dto: UpdateExperienceDto,
  ): Promise<Experience> {
    await this.experienceRepository.update(id, dto);
    return await this.getExperienceById(id);
  }

  async deleteExperience(id: number): Promise<void> {
    await this.experienceRepository.delete(id);
  }
}
