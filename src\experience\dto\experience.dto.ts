import { <PERSON>Date, <PERSON>NotEmpty, IsOptional, IsString, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ExperienceDto {
    @ApiProperty({
        description: 'Name of the company',
        example: 'Tech Corp'
    })
    @IsOptional()
    @IsString()
    company_name: string;
   
    @ApiProperty({
        description: 'Name of the company',
        example: 'Tech Corp'
    })
    @IsOptional()
    @IsString()
    company_img: string;

    @ApiProperty({
        description: 'Position held in the company',
        example: 'Software Engineer'
    })
    @IsOptional()
    @IsString()
    position: string;

    @ApiPropertyOptional({
        description: 'Duration of the experience',
        example: '2 years'
    })
    @IsOptional()
    @IsString()
    duration: string;

    @ApiProperty({
        description: 'Start date of the experience',
        example: '2023-01-01'
    })
    @IsOptional()
    @IsDate()
    start_date: Date;

    @ApiPropertyOptional({
        description: 'End date of the experience (if applicable)',
        example: '2023-12-31'
    })
    @IsOptional()
    @IsDate()
    end_date?: Date;

    @ApiPropertyOptional({
        description: 'Responsibilities during the experience',
        example: 'Developed and maintained web applications'
    })
    @IsOptional()
    @IsString()
    responsibilities: string;

    @ApiPropertyOptional({
        description: 'Location of the company',
        example: 'New York, USA'
    })
    @IsOptional()
    @IsString()
    location: string;

    @ApiPropertyOptional({
        description: 'ID of the person associated with the experience',
        example: 123
    })
    @IsOptional()
    @IsNumber()
    personId?: number;
}