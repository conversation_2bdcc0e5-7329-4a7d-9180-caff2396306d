import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_NAMES } from '../queue.constants';
import { QueueJobData } from '../queue.service';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';

@Injectable()
@Processor(QUEUE_NAMES.LINKEDIN)
export class LinkedInQueueProcessor {
  private readonly logger = new Logger(LinkedInQueueProcessor.name);

  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
  ) {}

  @Process('send-linkedin')
  async handleSendLinkedIn(job: Job<QueueJobData>) {
    const { candidateSequenceStatusId, candidateId, stepId, recipientLinkedIn, subject, body } = job.data;
    
    this.logger.log(`Processing LinkedIn job for candidate ${candidateId}, step ${stepId}`);

    try {
      // Update status to QUEUED
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.QUEUED,
      );

      // Simulate LinkedIn message/connection request
      await this.sendLinkedInMessage(recipientLinkedIn, subject, body);

      // Update status to SENT
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.SENT,
        {
          sentTo: recipientLinkedIn,
          subject,
          sentAt: new Date().toISOString(),
        },
      );

      this.logger.log(`LinkedIn message sent successfully for candidate ${candidateId}, step ${stepId}`);

      // Simulate delivery confirmation
      setTimeout(async () => {
        try {
          await this.candidateSequenceStatusService.updateStatus(
            candidateSequenceStatusId,
            SequenceStepStatus.DELIVERED,
          );
          this.logger.log(`LinkedIn delivery confirmed for candidate ${candidateId}, step ${stepId}`);
        } catch (error) {
          this.logger.error(`Failed to update delivery status: ${error.message}`);
        }
      }, 4000);

    } catch (error) {
      this.logger.error(`Failed to send LinkedIn message for candidate ${candidateId}: ${error.message}`);
      
      await this.candidateSequenceStatusService.incrementAttemptCount(candidateSequenceStatusId);
      
      await this.candidateSequenceStatusService.updateStatus(
        candidateSequenceStatusId,
        SequenceStepStatus.FAILED,
        {
          error: error.message,
          failedAt: new Date().toISOString(),
        },
      );

      throw error;
    }
  }

  private async sendLinkedInMessage(profileUrl: string, subject: string, message: string): Promise<void> {
    this.logger.log(`Sending LinkedIn message to: ${profileUrl}`);
    this.logger.log(`Subject: ${subject}`);
    this.logger.log(`Message: ${message?.substring(0, 100)}...`);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simulate occasional failures
    if (Math.random() < 0.08) { // 8% failure rate (LinkedIn has stricter limits)
      throw new Error('Simulated LinkedIn service failure');
    }

    this.logger.log('LinkedIn message sent successfully (mock)');
  }
}
