import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactUs } from './contact-us.entity';
import { EmailService } from 'src/email/email.service';
import { ContactUsDto } from './dto/contact_us.dto';

@Injectable()
export class ContactUsService {
  constructor(
    @InjectRepository(ContactUs)
    private contactUsRepository: Repository<ContactUs>,
    private readonly emailService: EmailService,
  ) {}

  async createContactUs(contactUsData: ContactUsDto): Promise<ContactUs> {
    // Construct full name
    const fullName = `${contactUsData.firstName} ${contactUsData.lastName}`;

    // Save the contact query
    const newContactUs = this.contactUsRepository.create(contactUsData);
    const savedContactUs = await this.contactUsRepository.save(newContactUs);

    // Prepare emails
    const userEmail = contactUsData.businessEmail;
    const adminEmail = '<EMAIL>';

    try {
      // Notify the user
      await this.emailService.sendEmail({
        to: [userEmail],
        subject: 'Query Received',
        body: `<p>Dear ${fullName},</p>
               <p>Thank you for reaching out to us regarding ${contactUsData.interestedServices}. 
               We have received your query and will respond shortly.</p>
               <p>Best regards,<br>Ultimate Outsourcing Team</p>`,
        from: process.env.DEFAULT_FROM_EMAIL,
      });

      // Notify the admin
      await this.emailService.sendEmail({
        to: [adminEmail],
        subject: 'New Contact Us Query',
        body: `<p>A new contact form submission has been received:</p>
               <ul>
                 <li><strong>Name:</strong> ${fullName}</li>
                 <li><strong>Email:</strong> ${userEmail}</li>
                 <li><strong>Phone:</strong> ${contactUsData.phoneNumber}</li>
                 <li><strong>Job Title:</strong> ${contactUsData.jobTitle}</li>
                 <li><strong>Company:</strong> ${contactUsData.companyName}</li>
                 <li><strong>Post Code:</strong> ${contactUsData.companyPostCode}</li>
                 <li><strong>Industry:</strong> ${contactUsData.industry}</li>
                 <li><strong>Interested Services:</strong> ${contactUsData.interestedServices}</li>
               </ul>`,
        from: process.env.DEFAULT_FROM_EMAIL,
      });
    } catch (error) {
      console.error('Error sending email:', error);
    }

    return savedContactUs;
  }
}
