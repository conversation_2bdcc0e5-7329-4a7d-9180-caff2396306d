import { Module } from '@nestjs/common';
import { CompanyScrapperControlService } from './company_scrapper_control.service';
import { CompanyScrapperControlController } from './company_scrapper_control.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanyScrapperControl } from './entities/company_scrapper_control.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CompanyScrapperControl])],
  controllers: [CompanyScrapperControlController],
  providers: [CompanyScrapperControlService],
  exports: [CompanyScrapperControlService],
})
export class CompanyScrapperControlModule {}
