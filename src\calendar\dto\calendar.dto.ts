import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsDate,
  IsUrl,
  Length,
  IsNotEmpty,
} from 'class-validator';

export class CalendarDTO {
  @ApiProperty({
    description: 'Type of calendar',
    enum: ['PERSONAL', 'WORK', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['PERSONAL', 'WORK', 'OTHER'])
  calendar_type?: string;

  @ApiProperty({
    description: 'Type of event',
    enum: ['MEETING', 'INTERVIEW', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['MEETING', 'INTERVIEW', 'OTHER'])
  event_type?: string;

  @ApiProperty({
    description: 'Type of interview',
    enum: ['ONLINE', 'ONSITE', 'PHONE', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['ONLINE', 'ONSITE', 'PHONE', 'OTHER'])
  interview_type?: string;

  @ApiProperty({
    description: 'Start date of the event',
    type: 'string',
    format: 'date',
    required: false,
  })
  @IsOptional()
  @IsDate()
  start_date?: Date;

  @ApiProperty({
    description: 'ID of the candidate',
    type: String,
    required: false,
  })
  @IsOptional()
  candidateId?: string;

  @ApiProperty({
    description: 'ID of the role',
    type: String,
    required: false,
  })
  @IsOptional()
  roleId?: string;

  @ApiProperty({
    description: 'Name of the event',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(0, 255)
  event_name?: string;

  @ApiProperty({
    description: 'Type of client',
    enum: ['CLIENT', 'PROSPECT', 'CANDIDATE', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['CLIENT', 'PROSPECT', 'CANDIDATE', 'OTHER'])
  client_type?: string;

  @ApiProperty({
    description: 'Source of the event',
    enum: ['ZOOM', 'GOOGLE MEETS', 'TEAMS', 'PHYSICAL', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['ZOOM', 'GOOGLE MEETS', 'TEAMS', 'PHYSICAL', 'OTHER'])
  event_source?: string;

  @ApiProperty({
    description: 'Link to the event',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @Length(0, 255)
  event_link?: string;

  @ApiProperty({
    description: 'Location of the event',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(0, 255)
  event_location?: string;

  @ApiProperty({
    description: 'Status of the event',
    required: false,
  })
  @IsOptional()
  @IsString()
  event_status?: string;

  @ApiProperty({
    description: 'End date of the event',
    type: 'string',
    format: 'date',
    required: false,
  })
  @IsOptional()
  @IsDate()
  end_date?: Date;

  @ApiProperty({
    description: 'Reminder date for the event',
    type: 'string',
    format: 'date',
    required: false,
  })
  @IsOptional()
  @IsDate()
  reminder_date?: Date;

  @ApiProperty({
    description: 'ID of the User entry',
    type: String,
    required: false,
  })
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'Person id ',
    example: '1',
  })
  @IsNotEmpty()
  personId: string;
}
