import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsOptional,
  IsString,
  IsUrl,
  IsInt,
  IsNumber,
} from 'class-validator';

export class RoleCandidateDto {
  @ApiPropertyOptional({
    description: 'The source type of the role candidate',
    enum: ['LINKEDIN', 'CV'],
  })
  @IsOptional()
  @IsEnum(['LINKEDIN', 'CV'])
  source_type?: string;

  @ApiPropertyOptional({
    description: 'The profile link of the candidate',
    example: 'https://linkedin.com/in/example',
  })
  @IsOptional()
  @IsUrl()
  profile_url?: string;

  @ApiPropertyOptional({
    description: 'The source of the candidate profile (e.g., LinkedIn)',
    example: 'LinkedIn',
  })
  @IsOptional()
  @IsString()
  profile_source?: string;

  @ApiPropertyOptional({
    description: 'The LinkedIn type of the candidate',
    example: ['OTW', 'HEAD HUNTING'],
  })
  @IsOptional()
  @IsString()
  li_type?: string;

  @ApiPropertyOptional({
    description: 'Indicates if the candidate is willing to relocate',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_willing_to_relocate?: boolean;

  @ApiPropertyOptional({
    description: 'Indicates if the candidate is accepted',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_accepted?: boolean;

  @ApiPropertyOptional({
    description: 'The current status of the candidate',
    enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ISSUE'],
    default: 'PENDING',
  })
  @IsOptional()
  @IsEnum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'ISSUE'])
  candidate_status?: string;

  @ApiPropertyOptional({
    description: 'The ID of the associated role',
    example: 101,
  })
  @IsOptional()
  @IsInt()
  roleId?: number;

  @ApiPropertyOptional({
    description: 'The ID of the associated client (People entity)',
    example: 'client123',
  })
  @IsOptional()
  @IsNumber()
  clientId?: number;

  @ApiPropertyOptional({
    description: 'The ID of the associated candidate (People entity)',
    example: 'candidate123',
  })
  @IsOptional()
  @IsNumber()
  candidateId?: number;

  @ApiPropertyOptional({
    description: 'The ID of the associated prospect (People entity)',
    example: 'prospect123',
  })
  @IsOptional()
  @IsNumber()
  prospectId?: number;

  @ApiPropertyOptional({
    description: 'The ID of the user who added the role candidate',
    example: 'user123',
  })
  @IsOptional()
  @IsString()
  userId?: string;
}
