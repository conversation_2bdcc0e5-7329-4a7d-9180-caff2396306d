import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { RoleCandidatesService } from './role_candidates.service';
import { RoleCandidate } from './role_candidates.entity';
import { Roles } from '../roles/roles.entity';
import { People } from '../people/people.entity';
import { PersonEmail } from '../emails/emails.entity';
import { PersonPhone } from '../phone/phone.entity';
import { Qualifications } from '../qualifications/qualifications.entity';
import { Languages } from '../languages/langauges.entity';
import { PersonSkill } from '../skills/skills.entity';
import { S3bucketService } from '../s3bucket/s3bucket.service';

describe('RoleCandidatesService', () => {
  let service: RoleCandidatesService;
  let roleCandidateRepository: Repository<RoleCandidate>;

  const mockRoleCandidate = {
    id: 1,
    source_type: 'LINKEDIN',
    profile_url: 'https://linkedin.com/in/test',
    li_connection_send_status: 'NOT_SENT',
    li_connection_response_status: null,
    candidate_status: 'PENDING',
    roleId: 1,
    candidateId: 1,
    created_at: new Date(),
    updated_at: new Date(),
  };

  const mockRepositoryFactory = () => ({
    findOne: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
    findOneBy: jest.fn(),
    findAndCount: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleCandidatesService,
        {
          provide: getRepositoryToken(RoleCandidate),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(Roles),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(People),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(PersonEmail),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(PersonPhone),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(Qualifications),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(Languages),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: getRepositoryToken(PersonSkill),
          useFactory: mockRepositoryFactory,
        },
        {
          provide: S3bucketService,
          useValue: {
            uploadFile: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RoleCandidatesService>(RoleCandidatesService);
    roleCandidateRepository = module.get<Repository<RoleCandidate>>(
      getRepositoryToken(RoleCandidate),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('markProfileReadyForConnection', () => {
    it('should mark a profile as ready for connection', async () => {
      const roleCandidateId = 1;
      const updatedCandidate = {
        ...mockRoleCandidate,
        li_connection_send_status: 'READY_TO_SEND',
      };

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(mockRoleCandidate as any);
      jest.spyOn(roleCandidateRepository, 'save').mockResolvedValue(updatedCandidate as any);

      const result = await service.markProfileReadyForConnection(roleCandidateId);

      expect(roleCandidateRepository.findOne).toHaveBeenCalledWith({
        where: { id: roleCandidateId },
        relations: ['candidate', 'role'],
      });
      expect(roleCandidateRepository.save).toHaveBeenCalledWith({
        ...mockRoleCandidate,
        li_connection_send_status: 'READY_TO_SEND',
      });
      expect(result.li_connection_send_status).toBe('READY_TO_SEND');
    });

    it('should throw NotFoundException when role candidate not found', async () => {
      const roleCandidateId = 999;

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(null);

      await expect(service.markProfileReadyForConnection(roleCandidateId))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('getOneReadyToSendConnectionProfile', () => {
    it('should get and update a ready to send profile', async () => {
      const readyProfile = {
        ...mockRoleCandidate,
        li_connection_send_status: 'READY_TO_SEND',
      };
      const updatedProfile = {
        ...readyProfile,
        li_connection_send_status: 'SENT',
      };

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(readyProfile as any);
      jest.spyOn(roleCandidateRepository, 'save').mockResolvedValue(updatedProfile as any);

      const result = await service.getOneReadyToSendConnectionProfile();

      expect(roleCandidateRepository.findOne).toHaveBeenCalledWith({
        where: {
          li_connection_send_status: 'READY_TO_SEND',
          source_type: 'LINKEDIN'
        },
        relations: [
          'candidate',
          'candidate.emails',
          'candidate.phones',
          'role',
          'user',
        ],
        order: { created_at: 'ASC' },
      });
      expect(roleCandidateRepository.save).toHaveBeenCalled();
      expect(result.li_connection_send_status).toBe('SENT');
    });

    it('should throw NotFoundException when no ready profiles found', async () => {
      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(null);

      await expect(service.getOneReadyToSendConnectionProfile())
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('updateConnectionRequestStatus', () => {
    it('should update connection request status', async () => {
      const roleCandidateId = 1;
      const connectionStatus = 'SENT';
      const responseStatus = 'Accepted';
      const updatedCandidate = {
        ...mockRoleCandidate,
        li_connection_send_status: connectionStatus,
        li_connection_response_status: responseStatus,
      };

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(mockRoleCandidate as any);
      jest.spyOn(roleCandidateRepository, 'save').mockResolvedValue(updatedCandidate as any);

      const result = await service.updateConnectionRequestStatus(
        roleCandidateId,
        connectionStatus,
        responseStatus,
      );

      expect(roleCandidateRepository.findOne).toHaveBeenCalledWith({
        where: { id: roleCandidateId },
        relations: ['candidate', 'role'],
      });
      expect(result.li_connection_send_status).toBe(connectionStatus);
      expect(result.li_connection_response_status).toBe(responseStatus);
    });

    it('should update only connection status when response status not provided', async () => {
      const roleCandidateId = 1;
      const connectionStatus = 'RECEIVED';
      const updatedCandidate = {
        ...mockRoleCandidate,
        li_connection_send_status: connectionStatus,
      };

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(mockRoleCandidate as any);
      jest.spyOn(roleCandidateRepository, 'save').mockResolvedValue(updatedCandidate as any);

      const result = await service.updateConnectionRequestStatus(
        roleCandidateId,
        connectionStatus,
      );

      expect(result.li_connection_send_status).toBe(connectionStatus);
    });

    it('should throw NotFoundException when role candidate not found', async () => {
      const roleCandidateId = 999;

      jest.spyOn(roleCandidateRepository, 'findOne').mockResolvedValue(null);

      await expect(service.updateConnectionRequestStatus(roleCandidateId, 'SENT'))
        .rejects.toThrow(NotFoundException);
    });
  });
});
