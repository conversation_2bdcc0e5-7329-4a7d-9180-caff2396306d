import { Injectable } from '@nestjs/common';
import { CreateCompanyScrapperControlDto } from './dto/create-company_scrapper_control.dto';
import { UpdateCompanyScrapperControlDto } from './dto/update-company_scrapper_control.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanyScrapperControl } from './entities/company_scrapper_control.entity';
import { Repository } from 'typeorm';

@Injectable()
export class CompanyScrapperControlService {
  constructor(
    @InjectRepository(CompanyScrapperControl)
    private readonly companyScrapperControlRepository: Repository<CompanyScrapperControl>,
  ) {}

  create(createCompanyScrapperControlDto: CreateCompanyScrapperControlDto) {
    return 'This action adds a new companyScrapperControl';
  }

  async createAndUpdate(data: CreateCompanyScrapperControlDto) {
    const existedData = await this.companyScrapperControlRepository.find();
    if (existedData.length > 0) {
      const result = await this.companyScrapperControlRepository.update(
        { id: existedData[0].id },
        {
          countryId: data.countryId,
          sectorId: data.sectorId,
          company_source: data.company_source,
          is_default: data.is_default,
        },
      );
      return {
        message: 'Company Scrapper settings updated successfully',
        result,
      };
    } else {
      const result = this.companyScrapperControlRepository.create({
        countryId: data.countryId,
        sectorId: data.sectorId,
        company_source: data.company_source,
        is_default: data.is_default,
      });

      await this.companyScrapperControlRepository.save(result);
      return {
        message: 'Company Scrapper settings updated successfully',
        result,
      };
    }
  }

  async findAll() {
    const result = await this.companyScrapperControlRepository.find();
    const data = result[0];
    return data;
  }

  findOne(id: number) {
    return `This action returns a #${id} companyScrapperControl`;
  }

  update(
    id: number,
    updateCompanyScrapperControlDto: UpdateCompanyScrapperControlDto,
  ) {
    return `This action updates a #${id} companyScrapperControl`;
  }

  remove(id: number) {
    return `This action removes a #${id} companyScrapperControl`;
  }
}
