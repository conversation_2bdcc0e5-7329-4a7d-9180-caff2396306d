module.exports = {
  displayName: 'Users Module Tests',
  testMatch: ['<rootDir>/src/users/**/*.spec.ts'],
  collectCoverageFrom: [
    '<rootDir>/src/users/**/*.ts',
    '!<rootDir>/src/users/**/*.spec.ts',
    '!<rootDir>/src/users/**/*.e2e.spec.ts',
    '!<rootDir>/src/users/**/*.d.ts',
  ],
  coverageDirectory: '<rootDir>/coverage/users',
  coverageReporters: ['text', 'lcov', 'html'],
  testEnvironment: 'node',
  roots: ['<rootDir>/src/users'],
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleFileExtensions: ['js', 'json', 'ts'],
  setupFilesAfterEnv: ['<rootDir>/src/users/test-setup.ts'],
  testTimeout: 30000,
  verbose: true,
  collectCoverage: true,
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
