import { Modu<PERSON> } from '@nestjs/common';
import { WebhooksController } from './webhooks.controller';
import { WebhooksService } from './webhooks.service';
import { CandidateSequenceStatusModule } from 'src/candidate-sequence-status/candidate-sequence-status.module';
import { SequenceModule } from 'src/sequence/sequence.module';

@Module({
  imports: [CandidateSequenceStatusModule, SequenceModule],
  controllers: [WebhooksController],
  providers: [WebhooksService],
  exports: [WebhooksService],
})
export class WebhooksModule {}
