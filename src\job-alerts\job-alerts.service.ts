import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Like, Repository } from 'typeorm';
import { JobAlerts } from './job-alerts.entity';
import { CreateJobAlertDto } from './dto/jobAlert.dto';

@Injectable()
export class JobAlertsService {
  constructor(
    @InjectRepository(JobAlerts)
    private readonly jobAlertRepository: Repository<JobAlerts>,
  ) {}

  async createJobAlert(data: CreateJobAlertDto): Promise<JobAlerts> {
    try {
      const jobAlert = this.jobAlertRepository.create(data);
      return await this.jobAlertRepository.save(jobAlert);
    } catch (error) {
      throw new InternalServerErrorException('Failed to create job alert.');
    }
  }

  async sendJobAlert(id: number): Promise<boolean> {
    try {
      const jobAlert = await this.jobAlertRepository.findOne({ where: { id } });
      if (!jobAlert) throw new NotFoundException('Job alert not found.');
      // Logic to send job alert via email
      return true;
    } catch (error) {
      throw new InternalServerErrorException('Failed to send job alert.');
    }
  }

  async getAllJobAlerts(page: number, limit: number): Promise<JobAlerts[]> {
    try {
      return await this.jobAlertRepository.find({
        skip: (page - 1) * limit,
        take: limit,
      });
    } catch (error) {
      throw new InternalServerErrorException('Failed to retrieve job alerts.');
    }
  }

  async getJobAlertByCandidateEmail(email: string): Promise<JobAlerts | null> {
    try {
      return await this.jobAlertRepository.findOne({ where: { email } });
    } catch (error) {
      throw new InternalServerErrorException('Failed to fetch job alert.');
    }
  }

  async getJobAlertsByKeywords(keywords: string): Promise<JobAlerts[]> {
    try {
      return await this.jobAlertRepository.find({
        where: { keywords: Like(`%${keywords}%`) },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to retrieve job alerts by keywords.',
      );
    }
  }

  async updateJobAlert(
    id: number,
    data: Partial<CreateJobAlertDto>,
  ): Promise<JobAlerts | null> {
    try {
      const result = await this.jobAlertRepository.update(id, data);
      if (result.affected === 0)
        throw new NotFoundException('Job alert not found.');
      return await this.jobAlertRepository.findOne({ where: { id } });
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to update job alert.',
        error: error.message,
      });
    }
  }

  async deleteJobAlert(id: number): Promise<boolean> {
    try {
      const result = await this.jobAlertRepository.delete(id);
      if (result.affected === 0)
        throw new NotFoundException('Job alert not found.');
      return true;
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete job alert.');
    }
  }
}
