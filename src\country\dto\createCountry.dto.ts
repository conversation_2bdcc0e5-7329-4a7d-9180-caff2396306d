import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCountryDto {
  @ApiProperty({
    description: 'Country name',
    example: 'Nigeria',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Country code',
    example: 'NG',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    description: 'Country timezone',
    example: 'Africa/Lagos',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  timezone: string;

  @ApiProperty({
    description: 'Country flag',
    example: 'https://restcountries.com/data/nga.svg',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  flag: string;

  @ApiProperty({
    description: 'Country region',
    example: 'Africa',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  region: string;
}
