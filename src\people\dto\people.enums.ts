export enum PersonType {
  CLIENT = 'CLIENT',
  CANDIDATE = 'CANDIDATE',
  PROSPECT = 'PROSPECT',
  OTHER = 'OTHER',
  JOB_POST_LEAD = 'JOB_POST_LEAD',
  CSV_UPLOAD = 'CSV_UPLOAD',
}

export enum PersonSource {
  REED = 'REED',
  TOTAL_JOBS = 'TOTAL_JOBS',
  CAREER_BUILDER = 'CAREER_BUILDER',
  CVL = 'CVL',
  CW = 'CW',
  INDEED = 'INDEED',
  MONSTER = 'MONSTER',
  DICE = 'DICE',
  WEBSITE = 'WEBSITE',
  RESUME_LIBRARY = 'RESUME_LIBRARY',
  LINKEDIN = 'LINKEDIN',
  E_FINANCE = 'E_FINANCE',
  CRM_MANUAL = 'CRM_MANUAL',
  GITHUB = 'GITHUB',
  OTHER = 'OTHER',
  JOB_POST_SCRAPPER = 'JOB_POST_SCRAPPER',
  COMPANY_PROFILE_SCRAPPER = 'COMPANY_PROFILE_SCRAPPER',
  CSV_UPLOAD = 'CSV_UPLOAD',
}

export enum PersonStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export enum SubscriptionType {
  FREE = 'FREE',
  MONTHLY = 'MONTHLY',
  ANNUALLY = 'ANNUALLY',
  ADHOC = 'ADHOC',
  TRIAL = 'TRIAL',
}

export enum PersonStatusType {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ARCHIVED = 'ARCHIVED',
  PAUSED = 'PAUSED',
  EXPIRED = 'EXPIRED',
  RENEWED = 'RENEWED',
}

export enum ProspectStatus {
  CONTACTED = 'CONTACTED',
  NOT_RESPONDED = 'NOT_RESPONDED',
  NOT_INTERESTED = 'NOT_INTERESTED',
  INTERESTED = 'INTERESTED',
  BASIC_INFO = 'BASIC_INFO',
  TEMPLATE_SENT = 'TEMPLATE_SENT',
  RECIEVED = 'RECIEVED',
  IN_PROCESS = 'IN_PROCESS',
  RESULTS_RECIEVED = 'RESULTS_RECIEVED',
  SENT = 'SENT',
  WAITING_RESPONSE = 'WAITING_RESPONSE',
  RETRIAL = 'RETRIAL',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  FEEDBACK_RECIEVED = 'FEEDBACK_RECIEVED',
  CLOSED = 'CLOSED',
  INFUTURE = 'INFUTURE',
  CONVERTED = 'CONVERTED',
}
