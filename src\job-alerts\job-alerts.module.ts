import { Modu<PERSON> } from '@nestjs/common';
import { JobAlertsService } from './job-alerts.service';
import { <PERSON><PERSON>lertsController } from './job-alerts.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobAlerts } from './job-alerts.entity';

@Module({
  imports: [TypeOrmModule.forFeature([JobAlerts])],
  providers: [JobAlertsService],
  controllers: [JobAlertsController],
})
export class JobAlertsModule {}
