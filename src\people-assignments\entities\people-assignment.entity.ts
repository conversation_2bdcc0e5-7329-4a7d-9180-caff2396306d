import { Company } from 'src/company/company.entity';
import { Country } from 'src/country/country.entity';
import { People } from 'src/people/people.entity';
import { Sector } from 'src/sector/sector.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum AssignmentType {
  PERSONAL = 'PERSONAL',
  BUSINESS = 'BUSINESS',
  OTHER = 'OTHER',
}

export enum AssignmentStatus {
  NOT_VERIFIED = 'not-verified',
  VERIFIED = 'verified',
  FAILED = 'failed',
}

@Entity('people_assignment')
export class PeopleAssignment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, nullable: true })
  email: string;

  @Column({
    type: 'enum',
    enum: AssignmentType,
    default: AssignmentType.BUSINESS,
  })
  type: AssignmentType;

  @Column({
    type: 'enum',
    enum: AssignmentStatus,
    default: AssignmentStatus.NOT_VERIFIED,
  })
  status: AssignmentStatus;

  @Column({ type: 'boolean', default: false })
  is_verified_by_amazon: boolean;

  @Column({ type: 'boolean', default: false })
  is_found: boolean;

  @Column({ type: 'boolean', default: false })
  is_default_email: boolean;

  @Column({ type: 'boolean', default: false })
  is_replacement: boolean;

  @Column({ type: 'boolean', default: false })
  is_verified: boolean;

  @Column({ type: 'boolean', default: false })
  is_verified_by_lead_expert: boolean;

  @Column({ type: 'boolean', default: false })
  is_unsubscribed: boolean;

  @Column({ type: 'timestamp with time zone', nullable: true })
  unsubscribed_date: Date;

  @Column({ type: 'boolean', default: false })
  is_suppressed: boolean;

  @Column({ type: 'boolean', default: false })
  is_blocked: boolean;

  @Column({ type: 'boolean', default: false })
  is_business_email_added: boolean;

  @Column({ type: 'boolean', default: false })
  is_replacement_needed: boolean;

  @Column({ type: 'boolean', default: false })
  is_email_not_found: boolean;

  @Column({ type: 'boolean', default: false })
  is_email_info_added: boolean;

  @Column({ type: 'boolean', default: false })
  is_hiring_person: boolean;

  @Column({ type: 'boolean', default: false })
  is_bounce_back: boolean;

  @Column({ type: 'boolean', default: false })
  is_replacement_not_found: boolean;

  @Column({ type: 'boolean', default: false })
  is_working_completed: boolean;

  @Column({ type: 'timestamp with time zone', nullable: true })
  assignment_date: Date;

  @Column({ nullable: true })
  personId: number;

  @ManyToOne(() => People, (person) => person.id)
  @JoinColumn({ name: 'personId' })
  person: People;

  @ManyToOne(() => Users, (user) => user.id)
  @JoinColumn({ name: 'leadUserId' })
  leadUser: Users;

  @Column({ nullable: true })
  leadUserId: string;

  @Column({ nullable: true })
  not_found_by: string;

  @ManyToOne(() => Company, (company) => company.people, { nullable: true })
  company: Company;

  @Column({ nullable: true })
  companyId: number;

  @ManyToOne(() => Sector, (sector) => sector.people, { nullable: true })
  @JoinColumn({ name: 'sectorId' })
  sector: Sector;

  @Column({ nullable: true })
  sectorId: number;

  @ManyToOne(() => Country, (country) => country.people, { nullable: true })
  @JoinColumn({ name: 'countryId' })
  country: Country;

  @Column({ nullable: true })
  countryId: number;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
