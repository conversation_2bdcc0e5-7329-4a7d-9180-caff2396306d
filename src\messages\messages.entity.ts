import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('messages')
export class Message {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  messageSid: string;

  @Column()
  from: string;

  @Column()
  to: string;

  @Column({ nullable: true })
  body: string;

  @Column({ nullable: true })
  mediaUrl: string;

  @Column({ nullable: true })
  mediaContentType: string;

  @Column({ default: 'sent' })
  status: string;

  @Column({ nullable: true })
  channel: string; // e.g., 'whatsapp', 'sms', etc.

  @CreateDateColumn()
  createdAt: Date;
}
