import { Modu<PERSON> } from '@nestjs/common';
import { ScrapperService } from './scrapper.service';
import { ScrapperController } from './scrapper.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from 'src/company/company.entity';
import { Country } from 'src/country/country.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { ScrapperStats } from './scrapperStats.entity';
import { CompanyScrapperControl } from 'src/company_scrapper_control/entities/company_scrapper_control.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Company,
      Country,
      People,
      PersonEmail,
      PeopleAssignment,
      PersonPhone,
      Jobs,
      ScrapperStats,
      CompanyScrapperControl,
    ]),
  ],
  controllers: [ScrapperController],
  providers: [ScrapperService],
})
export class ScrapperModule {}
