import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsEnum, IsOptional, IsString, IsUUID, IsInt } from 'class-validator';

export class FocusPointDto {

  @ApiProperty({
    description: 'Type of the focus point',
    enum: ['INFO', 'QUERY', 'SUGGESTION', 'ISSUE', 'OTHER'],
    nullable: true,
  })
  @IsOptional()
  @IsEnum(['INFO', 'QUERY', 'SUGGESTION', 'ISSUE', 'OTHER'])
  type: string;

  @ApiProperty({
    description: 'Message associated with the focus point',
    type: String,
    nullable: true,
  })
  @IsOptional()
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Date when the focus point was added',
    type: Date,
    nullable: true,
  })
  @IsOptional()
  @IsDate()
  added_at: Date;

  @ApiProperty({
    description: 'Date when the focus point was last updated',
    type: Date,
    nullable: true,
  })
  @IsOptional()
  @IsDate()
  updated_at: Date;

  @ApiProperty({
    description: 'Indicates if the focus point is soft deleted',
    type: Boolean,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  is_deleted: boolean;

  @ApiProperty({
    description: 'Date when the focus point was soft deleted',
    type: Date,
    nullable: true,
  })
  @IsOptional()
  @IsDate()
  deleted_at: Date;

  @ApiProperty({
    description: 'UUID of the associated user',
    type: String,
    format: 'uuid',
    nullable: true,
  })
  @IsOptional()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'ID of the associated role',
    type: Number,
    nullable: true,
  })
  @IsOptional()
  @IsInt()
  roleId: number;
}