import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PeopleAssignmentsService } from './people-assignments.service';
import { PeopleAssignmentsController } from './people-assignments.controller';
import { People } from 'src/people/people.entity';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { PeopleAssignment } from './entities/people-assignment.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      Company,
      PersonEmail,
      PersonPhone,
      PeopleAssignment,
      Jobs,
      Users,
    ]),
  ],
  controllers: [PeopleAssignmentsController],
  providers: [PeopleAssignmentsService],
})
export class PeopleAssignmentsModule {}
