import { Body, Controller, Put } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { RecruitmentService } from './recruitment.service';

@Controller('recruitment')
@ApiTags('Recruitment')
export class RecruitmentController {
  constructor(private readonly recruitmentService: RecruitmentService) {}

  @Put('update-role-candidate-stage')
  @ApiOperation({ summary: 'Update role candidate stage' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chennel: { type: 'string' },
        screening_stage: { type: 'string' },
        partially_interested_stage: { type: 'string' },
        submission_stage: { type: 'string' },
        role_candidate_id: { type: 'number' },
      },
    },
  })
  async updateRoleCandidateStage(
    @Body()
    {
      chennel,
      screening_stage,
      partially_interested_stage,
      submission_stage,
      role_candidate_id,
    }: any,
  ) {
    return await this.recruitmentService.updateRoleCandidateStage(
      chennel,
      screening_stage,
      partially_interested_stage,
      submission_stage,
      role_candidate_id,
    );
  }
}
