import { Module } from '@nestjs/common';
import { SequenceStepsService } from './sequence-steps.service';
import { SequenceStepsController } from './sequence-steps.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SequenceSteps } from './sequence_steps.entity';

@Module({
  imports: [TypeOrmModule.forFeature([SequenceSteps])], // Add your entities here
  providers: [SequenceStepsService],
  controllers: [SequenceStepsController],
})
export class SequenceStepsModule {}
