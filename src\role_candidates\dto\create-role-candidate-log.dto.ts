import { CommunicationType } from '../role_candidate_log.entity';
import { IsEnum, IsInt, IsOptional, IsString } from 'class-validator';

export class CreateRoleCandidateLogDto {
  @IsInt()
  roleCandidateId: number;

  @IsEnum(CommunicationType)
  type: CommunicationType;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  messageId?: string;

  @IsString()
  @IsOptional()
  details?: string;
}
