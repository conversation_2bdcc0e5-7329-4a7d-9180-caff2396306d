import { Module } from '@nestjs/common';
import { LeadsService } from './leads.service';
import { LeadsController } from './leads.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { People } from 'src/people/people.entity';
import { Users } from 'src/users/users.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { Country } from 'src/country/country.entity';
import { Sector } from 'src/sector/sector.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { ScrapperStats } from 'src/scrapper/scrapperStats.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      Users,
      PeopleAssignment,
      Company,
      PersonEmail,
      Country,
      Sector,
      Jobs,
      MailBox,
      ScrapperStats,
    ]),
  ],
  controllers: [LeadsController],
  providers: [LeadsService],
})
export class LeadsModule {}
