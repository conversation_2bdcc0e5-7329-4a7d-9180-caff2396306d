import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordTokenDto {
  @ApiProperty({
    example: '123456',
    description: 'The reset password token',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    example: 'password',
    description: 'The new password',
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
