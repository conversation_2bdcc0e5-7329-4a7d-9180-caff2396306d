import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FocusPoint } from './focusPoint.entity';
import { Repository } from 'typeorm';
import { FocusPointDto } from './dto/focusPoint.dto';
import { UpdateFocusPointDto } from './dto/updateFocusPoint.dto';

@Injectable()
export class FocusPointService {
  constructor(
    @InjectRepository(FocusPoint)
    private focusPointRepository: Repository<FocusPoint>,
  ) {}

  async createFocusPoint(focusPoint: FocusPointDto): Promise<FocusPoint> {
    try {
      const newFocusPoint = this.focusPointRepository.create(focusPoint);
      return await this.focusPointRepository.save(newFocusPoint);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error creating focus point',
        error.message,
      );
    }
  }

  async deleteFocusPoint(id: string): Promise<void> {
    try {
      const focusPoint = await this.focusPointRepository.findOneBy({ id });
      if (!focusPoint) {
        throw new NotFoundException('Focus point not found');
      }
      //   soft delete
      await this.focusPointRepository.update(id, {
        is_deleted: true,
        deleted_at: new Date(),
      });
    } catch (error) {
      throw new InternalServerErrorException(
        'Error deleting focus point',
        error.message,
      );
    }
  }

  async getFocusPointById(id: string): Promise<FocusPoint> {
    try {
      const focusPoint = await this.focusPointRepository.findOneBy({ id });
      if (!focusPoint) {
        throw new NotFoundException('Focus point not found');
      }
      return focusPoint;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving focus point',
        error.message,
      );
    }
  }

  async getAllFocusPoints(
    searchString: string = null,
    roleId: string = null,
  ): Promise<any> {
    try {
      const queryBuilder =
        this.focusPointRepository.createQueryBuilder('focusPoint');

      queryBuilder.leftJoinAndSelect('focusPoint.user', 'user'); // 👈 important!

      if (searchString) {
        queryBuilder.andWhere(
          '(focusPoint.message LIKE :searchString OR focusPoint.type LIKE :searchString)',
          { searchString: `%${searchString}%` },
        );
      }

      if (roleId) {
        queryBuilder.andWhere('focusPoint.roleId = :roleId', { roleId });
      }

      // No need to addSelect for user.id separately if you're using leftJoinAndSelect

      // Group by necessary fields (only if you're aggregating — you may not need this!)
      queryBuilder.groupBy('focusPoint.id');
      queryBuilder.addGroupBy('user.id');
      queryBuilder.addGroupBy('user.first_name');
      queryBuilder.addGroupBy('user.last_name');

      // Order by type and added_at
      queryBuilder.orderBy('focusPoint.type', 'ASC');
      queryBuilder.addOrderBy('focusPoint.added_at', 'DESC');

      const results = await queryBuilder.getMany();

      // Transforming the results into the desired format
      // Transforming the results into the desired format
      const groupedResults = results.reduce((acc, focusPoint) => {
        const type = focusPoint.type;

        // Initialize the array for that type if not already present
        if (!acc[type]) {
          acc[type] = [];
        }

        // Add focus point data, including only the full name of the user (if the user exists)
        acc[type].push({
          id: focusPoint.id,
          type: focusPoint.type,
          message: focusPoint.message,
          added_at: focusPoint.added_at,
          updated_at: focusPoint.updated_at,
          is_deleted: focusPoint.is_deleted,
          deleted_at: focusPoint.deleted_at,
          userId: focusPoint.userId,
          roleId: focusPoint.roleId,
          fullName: focusPoint.user
            ? `${focusPoint.user.first_name} ${focusPoint.user.last_name}`
            : 'Unknown User', // Default if user is null
        });

        return acc;
      }, {});

      return groupedResults;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving focus points',
        error.message || error,
      );
    }
  }

  async updateFocusPoint(
    id: string,
    focusPoint: UpdateFocusPointDto,
  ): Promise<void> {
    try {
      const existingFocusPoint = await this.focusPointRepository.findOneBy({
        id,
      });
      if (!existingFocusPoint) {
        throw new NotFoundException('Focus point not found');
      }
      await this.focusPointRepository.update(id, focusPoint);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error updating focus point',
        error.message,
      );
    }
  }

  //   async getFocusPointGroupByType(): Promise<any[]> {
  //     try {
  //       return await this.focusPointRepository
  //         .createQueryBuilder('focusPoint')
  //         .select('focusPoint.type')
  //         .addSelect('COUNT(focusPoint.id)', 'count')
  //         .groupBy('focusPoint.type')
  //         .getRawMany();
  //     } catch (error) {
  //       throw new InternalServerErrorException(
  //         'Error retrieving focus points',
  //         error.message,
  //       );
  //     }
  //   }

  async getFocusPointGroupByType(roleId: string, userId: string): Promise<any> {
    try {
      const focusPoints = await this.focusPointRepository
        .createQueryBuilder('focusPoint')
        .leftJoinAndSelect('focusPoint.user', 'user') // Include user relation
        .leftJoinAndSelect('focusPoint.role', 'role') // Include role relation
        .where('focusPoint.roleId = :roleId', { roleId })
        .andWhere('focusPoint.userId = :userId', { userId })
        .select([
          'focusPoint.type',
          'focusPoint.message',
          'focusPoint.added_at',
          'focusPoint.updated_at',
          'user.id',
          'user.first_name',
          'user.last_name',
          'user.email',
          'role.id',
          'role.title',
        ])
        .orderBy('focusPoint.type', 'ASC')
        .getMany();

      // Group focus points by type
      const groupedFocusPoints = focusPoints.reduce((acc, focusPoint) => {
        if (!acc[focusPoint.type]) {
          acc[focusPoint.type] = [];
        }
        acc[focusPoint.type].push(focusPoint);
        return acc;
      }, {});

      return groupedFocusPoints;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error retrieving focus points grouped by type',
        error.message,
      );
    }
  }
}
