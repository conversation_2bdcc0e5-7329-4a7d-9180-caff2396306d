import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean } from 'class-validator';

export enum Role {
  USER = 'USER',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
}

export enum Designation {
  RPO = 'RPO',
  RECRUITER_WEB = 'RECRUITER_WEB',
  EMPLOY = 'EMPLOY',
  CANDIDATE = 'CANDIDATE',
  LEADEXPERT = 'LEADEXPERT',
  PROJECT_HEAD = 'PROJECT_HEAD',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  PROJECT_MEMBER = 'PROJECT_MEMBER',
  BUSINESS_DEVELOPMENT_EXECUTIVE = 'BUSINESS_DEVELOPMENT_EXECUTIVE',
  BUSINESS_DEVELOPMENT_MANAGER = 'BUSINESS_DEVELOPMENT_MANAGER',
  ACCOUNT_MANAGER = 'ACCOUNT_MANAGER',
  ACCOUNT_EXECUTIVE = 'ACCOUNT_EXECUTIVE',
  ACCOUNTANT = 'ACCOUNTANT',
  FINANCE_MANAGER = 'FINANCE_MANAGER',
  FINANCE_EXECUTIVE = 'FINANCE_EXECUTIVE',
  HR_MANAGER = 'HR_MANAGER',
  HR_EXECUTIVE = 'HR_EXECUTIVE',
  HR_RECRUITER = 'HR_RECRUITER',
  RECRUITER_CRM = 'RECRUITER_CRM',
  PRE_QUALIFICATION = 'PRE_QUALIFICATION',
  RESOURCER_CRM = 'RESOURCER_CRM',
}

export enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BLOCKED = 'BLOCKED',
  DELETED = 'DELETED',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
  REJECTED = 'REJECTED',
  APPROVED = 'APPROVED',
  UNAPPROVED = 'UNAPPROVED',
  UNVERIFIED = 'UNVERIFIED',
  VERIFIED = 'VERIFIED',
}

export class CreateUserDto {
  @ApiProperty({
    example: 1,
    description: 'The id of the user',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  id: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'The full name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  full_name: string;

  @ApiProperty({
    example: 'John',
    description: 'The first name of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  first_name: string;

  @ApiProperty({
    example: 'Doe',
    description: 'The last name of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  last_name: string;

  @ApiProperty({
    example: 'johndoe',
    description: 'The username of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    example: ['CRM', 'WEBSITE'],
    description: 'Platform of the user from where he/she is being registered',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  source: string;

  @ApiProperty({
    example: 'ONSITE',
    description: 'working mode of the user',
    required: false,
    type: 'enum',
    enum: ['ONSITE', 'REMOTE', 'HYBRID'],
  })
  @IsEnum(['ONSITE', 'REMOTE', 'HYBRID'])
  @IsString()
  @IsOptional()
  working_mode: string;

  @ApiProperty({
    example: 'USER',
    description: 'The role of the user',
    required: true,
    enum: Role,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Role)
  role: Role;

  @ApiProperty({
    example: 'RECRUITER',
    description: 'The designation of the user',
    required: false,
    enum: Designation,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Designation)
  designation: Designation;

  @ApiProperty({
    example: '12345-1234567-1',
    description: 'The CNIC of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  cnic: string;

  @ApiProperty({
    example: '',
    description: 'The password of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_original: string;

  @ApiProperty({
    example: '12345',
    description: 'The password hash of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_hash: string;

  @ApiProperty({
    example: '12345',
    description: 'The password salt of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  password_salt: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: '1990-01-01',
    description: 'The birth date of the user',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  birth_date: string;

  @ApiProperty({
    example: 'US',
    description: 'The country code of the user',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  country_code: string;

  @ApiProperty({
    example: '1234567890',
    description: 'The phone number of the user',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  phone_number: string;

  @ApiProperty({
    example: '************',
    description: 'The display phone number of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  display_phone_number: string;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'The profile picture of the user',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  profile_picture: string;

  @ApiProperty({
    example: 'password',
    description: 'The password of the user',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'The status of the user',
    required: false,
    enum: Status,
  })
  @IsString()
  @IsOptional()
  @IsEnum(Status)
  status: Status;
}
