import { Modu<PERSON> } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Roles } from './roles.entity';
import { RoleLogs } from 'src/role_logs/role_logs.entity';
import { RoleHistory } from 'src/role-history/role-history.entity';
import { People } from 'src/people/people.entity';
import { Company } from 'src/company/company.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Roles, RoleLogs, RoleHistory, People, Company]),
  ],
  providers: [RolesService],
  controllers: [RolesController],
})
export class RolesModule {}
