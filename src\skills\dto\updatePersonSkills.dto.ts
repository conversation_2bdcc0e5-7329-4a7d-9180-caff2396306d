import { ApiProperty, PartialType } from '@nestjs/swagger';
import { PersonSkillDto } from './person-skill.dto';
import { IsNumber } from 'class-validator';

export class UpdatepersonSkillsDto extends PartialType(PersonSkillDto) {
  @ApiProperty({
    example: 'ID of the skill to update',
    description: 'ID of the skill to update',
    required: true,
    type: Number,
  })
  @IsNumber()
  id: number;
}
