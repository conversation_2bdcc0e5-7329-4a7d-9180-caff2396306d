import { Controller } from '@nestjs/common';
import { Body, Delete, Get, Param, Post, Put } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ExperienceService } from './experience.service';
import { Experience } from './experience.entity';
import { ExperienceDto } from './dto/experience.dto';
import { UpdateExperienceDto } from './dto/updateExperience.dto';

@ApiTags('Experience')
@Controller('experience')
export class ExperienceController {
  constructor(private readonly experienceService: ExperienceService) {}

  @ApiOperation({ summary: 'Create a new experience' })
  @ApiResponse({
    status: 201,
    description: 'The experience has been successfully created.',
    type: Experience,
  })
  @Post()
  async createExperience(
    @Body() experienceDto: ExperienceDto,
  ): Promise<Experience> {
    return await this.experienceService.createExperience(experienceDto);
  }

  @ApiOperation({ summary: 'Get all experiences' })
  @ApiResponse({
    status: 200,
    description: 'List of all experiences.',
    type: [Experience],
  })
  @Get()
  async getAllExperiences(): Promise<Experience[]> {
    return await this.experienceService.getAllExperiences();
  }

  @ApiOperation({ summary: 'Get experience by ID' })
  @ApiResponse({
    status: 200,
    description: 'The experience with the given ID.',
    type: Experience,
  })
  @ApiResponse({ status: 404, description: 'Experience not found.' })
  @Get(':id')
  async getExperienceById(@Param('id') id: number): Promise<Experience> {
    return await this.experienceService.getExperienceById(id);
  }

  @ApiOperation({ summary: 'Update an experience' })
  @ApiResponse({
    status: 200,
    description: 'The experience has been successfully updated.',
    type: Experience,
  })
  @ApiResponse({ status: 404, description: 'Experience not found.' })
  @Put(':id')
  async updateExperience(
    @Param('id') id: number,
    @Body() updateExperienceDto: UpdateExperienceDto,
  ): Promise<Experience> {
    return await this.experienceService.updateExperience(
      id,
      updateExperienceDto,
    );
  }

  @ApiOperation({ summary: 'Delete an experience' })
  @ApiResponse({
    status: 200,
    description: 'The experience has been successfully deleted.',
  })
  @ApiResponse({ status: 404, description: 'Experience not found.' })
  @Delete(':id')
  async deleteExperience(@Param('id') id: number): Promise<void> {
    return await this.experienceService.deleteExperience(id);
  }
}
