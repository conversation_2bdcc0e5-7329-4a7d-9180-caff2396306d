import { Roles } from 'src/roles/roles.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import {
  RoleLogsAction,
  RoleLogsStatus,
  RoleLogsType,
} from './dto/rol_los.enum';

@Entity('role_logs')
export class RoleLogs {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: RoleLogsAction,
    default: 'CREATE',
  })
  action: string;

  @PrimaryGeneratedColumn('uuid')
  @Index()
  log_action_id: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @Column({ type: 'text', nullable: true })
  details: string;

  @Column({ type: 'int', nullable: true })
  role_number: number;

  @Column({
    type: 'enum',
    enum: RoleLogsStatus,
    default: RoleLogsStatus.PENDING,
    nullable: true,
  })
  log_status_type: RoleLogsStatus;

  @Column({
    type: 'enum',
    enum: RoleLogsType,
    default: RoleLogsType.RESOURCER,
    nullable: true,
  })
  log_status_at: RoleLogsType;

  @Column({ type: 'varchar', nullable: true })
  next_stage: string;

  @Column({ type: 'date', nullable: true })
  role_date: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  start_time: Date;

  @Column({ type: 'timestamp', nullable: true })
  end_time: Date;

  @Column({ type: 'text', nullable: true })
  comment: string;

  @Column({ type: 'text', nullable: true })
  time_spent: string;

  @Column({ type: 'varchar', length: '255', nullable: true })
  status: string;

  @ManyToOne(() => Roles, (role) => role.roleLogs, { nullable: true })
  role: Roles;

  @Column({ type: 'int', nullable: true, default: null, name: 'roleId' })
  roleId: number;

  @ManyToOne(() => Users, (user) => user.roleLogs, { nullable: true })
  user: Users;

  @Column({ type: 'varchar', nullable: true, default: null, name: 'userId' })
  userId: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
