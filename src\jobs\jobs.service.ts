import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Jobs } from './jobs.entity';
import {
  Between,
  ILike,
  IsNull,
  LessThanOrEqual,
  Like,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';
import {
  CreateJobDto,
  GetAllJobsDto,
  searchJobPostsOfCompanyDto,
} from './dto/createJob.dto';
import { UpdateJobDto } from './dto/updateJob.dto';

@Injectable()
export class JobsService {
  constructor(
    @InjectRepository(Jobs)
    private jobRepository: Repository<Jobs>,
  ) {}

  async createJob(createJobDto: CreateJobDto): Promise<Jobs> {
    try {
      const job = this.jobRepository.create(createJobDto);
      await this.jobRepository.save(job);
      return job;
    } catch (error) {
      console.error('Error creating job:', error.message);
      throw new InternalServerErrorException(
        `Failed to create job: ${error.message}`,
      );
    }
  }

  async updateJob(id: number, updateJobDto: UpdateJobDto): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { id } });
      if (!job) {
        throw new NotFoundException(`Job with ID ${id} not found`);
      }

      await this.jobRepository.update({ id }, updateJobDto);
      return { ...job, ...updateJobDto };
    } catch (error) {
      console.error('Error updating job:', error.message);
      throw new InternalServerErrorException(
        `Failed to update job: ${error.message}`,
      );
    }
  }

  async deleteJob(id: number): Promise<void> {
    try {
      const result = await this.jobRepository.delete({ id });
      if (result.affected === 0) {
        throw new NotFoundException(`Job with ID ${id} not found`);
      }
    } catch (error) {
      console.error('Error deleting job:', error.message);
      throw new InternalServerErrorException(
        `Failed to delete job: ${error.message}`,
      );
    }
  }

  async findAll(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = '',
    sortingOrder: string = 'ASC',
    sortBy: string = 'id',
    userId: string = '1',
  ): Promise<Jobs[]> {
    try {
      const jobs = await this.jobRepository.find({
        where: { userId: userId },
        take: pageSize,
        skip: page * pageSize,
        order: {
          [sortBy]: sortingOrder,
        },
        // return companies with their associated jobs
        relations: ['company'],
      });
      if (!jobs.length) {
        throw new NotFoundException('No jobs found');
      }
      return jobs;
    } catch (error) {
      console.error('Error finding jobs:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve jobs: ${error.message}`,
      );
    }
  }
  async findAllGeneral(
    page: number = 0,
    pageSize: number = 10,
    searchString: string = '',
    sortingOrder: string = 'ASC',
    sortBy: string = 'id',
  ): Promise<Jobs[]> {
    try {
      if (searchString === '') {
        searchString = null;
      }
      const whereCondition = searchString
        ? { title: ILike(`%${searchString}%`) }
        : {};
      const jobs = await this.jobRepository.find({
        where: whereCondition,
        take: pageSize,
        skip: page * pageSize,
        order: {
          [sortBy]: sortingOrder,
        },
        // return companies with their associated jobs
        relations: ['company'],
      });
      if (!jobs.length) {
        throw new NotFoundException('No jobs found');
      }
      return jobs;
    } catch (error) {
      console.error('Error finding jobs:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve jobs: ${error.message}`,
      );
    }
  }

  async findOne(id: number): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { id } });
      if (!job) {
        throw new NotFoundException(`Job with ID ${id} not found`);
      }
      return job;
    } catch (error) {
      console.error(`Error retrieving job ID ${id}:`, error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  async findJobByTitle(title: string): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { title } });
      if (!job) {
        throw new NotFoundException(`No job found with title "${title}"`);
      }
      return job;
    } catch (error) {
      console.error('Error finding job by title:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  async findJobByPersonId(personId: number): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { personId } });
      if (!job) {
        throw new NotFoundException(`No job found for person ID ${personId}`);
      }
      return job;
    } catch (error) {
      console.error('Error finding job by person ID:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  async findJobByLocation(job_location_type: string): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({
        where: { job_location_type },
      });
      if (!job) {
        throw new NotFoundException(
          `No job found at location "${job_location_type}"`,
        );
      }
      return job;
    } catch (error) {
      console.error('Error finding job by location:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  async findJobByType(job_type: string): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { job_type } });
      if (!job) {
        throw new NotFoundException(`No job found of type "${job_type}"`);
      }
      return job;
    } catch (error) {
      console.error('Error finding job by type:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  async findJobByCompanyId(companyId: number): Promise<Jobs> {
    try {
      const job = await this.jobRepository.findOne({ where: { companyId } });
      if (!job) {
        throw new NotFoundException(`No job found for company ID ${companyId}`);
      }
      return job;
    } catch (error) {
      console.error('Error finding job by company ID:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job: ${error.message}`,
      );
    }
  }

  //  find all jobs by userId and its job applicants with person data from people table against userId
  async findAllJobApplicantsByUserId(userId: string): Promise<Jobs[]> {
    try {
      const jobs = await this.jobRepository.find({
        where: { userId: userId },
        relations: ['job_applications', 'job_applications.user.people'],
      });
      if (!jobs.length) {
        throw new NotFoundException('No jobs found for this user');
      }
      return jobs;
    } catch (error) {
      console.error('Error finding job applicants by user ID:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job applicants: ${error.message}`,
      );
    }
  }

  async getJobPostsByCompanyId(companyId: string) {
    if (!companyId) {
      throw new InternalServerErrorException('Company ID is required');
    }

    const companyIdInt = parseInt(companyId);

    try {
      const jobs = await this.jobRepository.find({
        where: { companyId: companyIdInt },
      });
      if (!jobs.length) {
        throw new NotFoundException('No jobs found for this user');
      }
      return jobs;
    } catch (error) {
      console.error('Error finding job applicants by user ID:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve job applicants: ${error.message}`,
      );
    }
  }

  async getAllJobs(queryParam: GetAllJobsDto) {
    const {
      page,
      pageSize,
      search,
      country_id,
      sector_id,
      remote,
      jobType,
      experienceLevel,
      selectedCountry,
      selectedSector,
      startDate,
      endDate,
    } = queryParam;
    const pageInt = parseInt(page) || 0;
    const pageSizeInt = parseInt(pageSize) || 10;

    const limit = pageSizeInt;
    const skip = pageInt * pageSizeInt;

    let whereCondition = {} as any;

    if (search && search.trim()) {
      const searchConditions: any = [
        { name: ILike(`%${search}%`) },
        { website: ILike(`%${search}%`) },
        { profile_url: ILike(`%${search}%`) },
        { industry: ILike(`%${search}%`) },
        { address: ILike(`%${search}%`) },
        { description: ILike(`%${search}%`) },
        { headquarter_country: ILike(`%${search}%`) },
      ];
      if (!isNaN(Number(search))) {
        searchConditions.push({ staff_count: Number(search) });
        searchConditions.push({ founded: Number(search) });
      }
      whereCondition = searchConditions.map((condition: any) => ({
        ...whereCondition,
        ...condition,
      }));
    }

    if (country_id || selectedCountry) {
      whereCondition.countryId =
        parseInt(country_id) || parseInt(selectedCountry);
    }

    if (sector_id || selectedSector) {
      whereCondition.sectorId = parseInt(sector_id) || parseInt(selectedSector);
    }
    if (startDate && endDate) {
      whereCondition.created_at =
        startDate === endDate
          ? MoreThanOrEqual(`${startDate}T00:00:00.000Z`)
          : Between(`${startDate}T00:00:00.000Z`, `${endDate}T23:59:59.999Z`);
    } else if (startDate) {
      whereCondition.created_at = MoreThanOrEqual(`${startDate}T00:00:00.000Z`);
    } else if (endDate) {
      whereCondition.created_at = LessThanOrEqual(`${endDate}T23:59:59.999Z`);
    }

    if (remote) {
      whereCondition.job_location_type = remote;
    }

    if (jobType) {
      whereCondition.job_type = jobType;
    }

    if (experienceLevel) {
      whereCondition.experience_level = experienceLevel;
    }

    const [jobs, total] = await this.jobRepository.findAndCount({
      where: whereCondition,
      order: { created_at: 'DESC' },
      take: limit,
      skip: skip,
      relations: ['company'],
    });

    let sr_count = 0,
      direct_count = 0,
      unknown_count = 0;
    if (Array.isArray(whereCondition)) {
      sr_count = await this.jobRepository.count({
        where: whereCondition.map((cond) => ({ ...cond, sectorId: 2 })),
      });
      direct_count = await this.jobRepository.count({
        where: whereCondition.map((cond) => ({ ...cond, sectorId: 1 })),
      });
      unknown_count = await this.jobRepository.count({
        where: whereCondition.map((cond) => ({ ...cond, sectorId: IsNull() })),
      });
    } else {
      sr_count = await this.jobRepository.count({
        where: { ...whereCondition, sectorId: 2 },
      });
      direct_count = await this.jobRepository.count({
        where: { ...whereCondition, sectorId: 1 },
      });
      unknown_count = await this.jobRepository.count({
        where: { ...whereCondition, sectorId: IsNull() },
      });
    }

    const responseData = {
      jobPosts: jobs,
      totalCount: total,
      sr_count:
        !sector_id ||
        sector_id === '' ||
        sector_id === undefined ||
        sector_id === null
          ? sr_count
          : sector_id === '2'
            ? sr_count
            : 0,
      direct_count:
        !sector_id ||
        sector_id === '' ||
        sector_id === undefined ||
        sector_id === null
          ? direct_count
          : sector_id === '1'
            ? direct_count
            : 0,
      unknown_count:
        !sector_id ||
        sector_id === '' ||
        sector_id === undefined ||
        sector_id === null
          ? unknown_count
          : sector_id === 'null'
            ? unknown_count
            : 0,
    };

    return responseData;
  }

  async searchJobPostsOfCompany(queryParams: searchJobPostsOfCompanyDto) {
    try {
      const { searchTerm, company_id } = queryParams;

      const ilikeCondition = searchTerm
        ? [
            { title: ILike(`%${searchTerm}%`), companyId: company_id },
            { industry: ILike(`%${searchTerm}%`), companyId: company_id },
            {
              job_location_city: ILike(`%${searchTerm}%`),
              companyId: company_id,
            },
            {
              job_location_state: ILike(`%${searchTerm}%`),
              companyId: company_id,
            },
            { description: ILike(`%${searchTerm}%`), companyId: company_id },
            {
              job_posting_link: ILike(`%${searchTerm}%`),
              companyId: company_id,
            },
          ]
        : [{ companyId: company_id }];

      const data = await this.jobRepository.find({
        where: ilikeCondition,
      });
      return data;
    } catch (error) {
      console.log('Error finding all companies:', error.message);
      throw new InternalServerErrorException(
        `Failed to retrieve all companies: ${error.message}`,
      );
    }
  }
  async searchJobPosts(searchTerm?: string) {
    try {
      const query = this.jobRepository
        .createQueryBuilder('job')
        .leftJoinAndSelect('job.company', 'company')
        .leftJoinAndSelect('job.person', 'person');

      if (searchTerm) {
        query.andWhere(
          `
            job.title ILIKE :term
            OR job.industry ILIKE :term
            OR job.job_location_city ILIKE :term
            OR job.job_location_state ILIKE :term
            OR job.job_location_type::text ILIKE :term
            OR job.description ILIKE :term
            OR job.job_posting_link ILIKE :term
          `,
          { term: `%${searchTerm}%` },
        );
      }

      const data = await query.getMany();

      return data.map((job) => ({
        ...job,
        company: job.company
          ? {
              id: job.company.id,
              name: job.company.name,
              profile_url: job.company.profile_url,
            }
          : null,
        person: job.person
          ? {
              id: job.person.id,
              full_name: job.person.full_name,
              profile_url: job.person.profile_url,
            }
          : null,
      }));
    } catch (err) {
      throw new InternalServerErrorException(
        err.message || 'Some error occurred while retrieving job posts.',
      );
    }
  }
}
