import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebsiteManualRequest } from './website_manual_requests.entity';
import { CreateWebsiteManualRequestDto } from './dto/create-website-manual-request.dto';
import { EmailService } from '../email/email.service';
import { S3bucketService } from '../s3bucket/s3bucket.service';

@Injectable()
export class WebsiteManualRequestsService {
  constructor(
    @InjectRepository(WebsiteManualRequest)
    private readonly websiteManualRequestRepository: Repository<WebsiteManualRequest>,
    private readonly emailService: EmailService,
    private readonly s3bucketService: S3bucketService,
  ) {}

  async createManualRequest(
    createDto: CreateWebsiteManualRequestDto,
  ): Promise<WebsiteManualRequest> {
    try {
      // Create the manual request record
      const manualRequest = this.websiteManualRequestRepository.create(createDto);
      const savedRequest = await this.websiteManualRequestRepository.save(manualRequest);

      // Send email with file attachment if URLs are provided
      if (createDto.s3_url || createDto.file_url) {
        await this.sendManualRequestEmail(savedRequest);
      }

      return savedRequest;
    } catch (error) {
      console.error('Error creating manual request:', error);
      throw new InternalServerErrorException('Failed to create manual request');
    }
  }

  async getAllManualRequests(): Promise<WebsiteManualRequest[]> {
    try {
      return await this.websiteManualRequestRepository.find({
        order: { created_at: 'DESC' },
      });
    } catch (error) {
      console.error('Error fetching manual requests:', error);
      throw new InternalServerErrorException('Failed to fetch manual requests');
    }
  }

  async getManualRequestById(id: string): Promise<WebsiteManualRequest> {
    try {
      const request = await this.websiteManualRequestRepository.findOne({
        where: { id },
      });

      if (!request) {
        throw new NotFoundException(`Manual request with ID ${id} not found`);
      }

      return request;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error fetching manual request by ID:', error);
      throw new InternalServerErrorException('Failed to fetch manual request');
    }
  }

  private async sendManualRequestEmail(request: WebsiteManualRequest): Promise<void> {
    try {
      const subject = `Manual Request: ${request.manual_interest} - ${request.first_name} ${request.last_name}`;

      const emailBody = `
        <h2>New Manual Request Received</h2>
        <p>A new manual request has been submitted with the following details:</p>

        <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Name:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.first_name} ${request.last_name}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Job Title:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.job_title}</td>
          </tr>
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Company:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.company}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Email:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.email}</td>
          </tr>
          <tr style="background-color: #f5f5f5;">
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Manual Interest:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.manual_interest}</td>
          </tr>
          <tr>
            <td style="border: 1px solid #ddd; padding: 12px; font-weight: bold;">Request Date:</td>
            <td style="border: 1px solid #ddd; padding: 12px;">${request.created_at.toLocaleDateString()}</td>
          </tr>
        </table>

        ${request.s3_url ? `<p><strong>S3 File URL:</strong> ${request.s3_url}</p>` : ''}
        ${request.file_url ? `<p><strong>File URL:</strong> ${request.file_url}</p>` : ''}

        <p>Please find the requested file attached to this email.</p>

        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          This email was automatically generated from the Website Manual Request system.
        </p>
      `;

      // Prepare file URLs for attachment
      const fileUrls: string[] = [];
      if (request.s3_url) {
        fileUrls.push(request.s3_url);
      }
      if (request.file_url && request.file_url !== request.s3_url) {
        fileUrls.push(request.file_url);
      }

      // Send email with attachments
      await this.emailService.sendEmailWithAttachments({
        to: [request.email],
        subject,
        body: emailBody,
        fileUrls,
      });

      // Update the request to mark email as sent
      await this.websiteManualRequestRepository.update(request.id, {
        email_sent: true,
        email_sent_at: new Date(),
        email_error: null,
      });

      console.log(`Manual request email sent successfully to: ${request.email}`);
    } catch (error) {
      console.error('Error sending manual request email:', error);

      // Update the request to mark email as failed
      await this.websiteManualRequestRepository.update(request.id, {
        email_sent: false,
        email_error: error.message || 'Unknown error occurred while sending email',
      });

      // Don't throw the error to prevent the request creation from failing
      // The request is still saved, but email sending failed
    }
  }

  async resendEmail(id: string): Promise<WebsiteManualRequest> {
    try {
      const request = await this.getManualRequestById(id);

      if (!request.s3_url && !request.file_url) {
        throw new InternalServerErrorException('No file URLs available for this request');
      }

      await this.sendManualRequestEmail(request);

      return await this.getManualRequestById(id);
    } catch (error) {
      console.error('Error resending email:', error);
      throw new InternalServerErrorException('Failed to resend email');
    }
  }

  async getFailedEmailRequests(): Promise<WebsiteManualRequest[]> {
    try {
      return await this.websiteManualRequestRepository.find({
        where: { email_sent: false },
        order: { created_at: 'DESC' },
      });
    } catch (error) {
      console.error('Error fetching failed email requests:', error);
      throw new InternalServerErrorException('Failed to fetch failed email requests');
    }
  }
}
