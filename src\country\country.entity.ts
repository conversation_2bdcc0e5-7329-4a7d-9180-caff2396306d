import { Company } from 'src/company/company.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { People } from 'src/people/people.entity';
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';

@Entity()
export class Country {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  code: string;

  @Column()
  timezone: string;

  @Column()
  flag: string;

  @Column()
  region: string;

  @OneToMany(() => Company, (company) => company.country)
  companies: Company[];

  @OneToMany(() => People, (people) => people.country)
  people: People[];

  @OneToMany(() => Jobs, (job) => job.country)
  jobs: Jobs[];
}
