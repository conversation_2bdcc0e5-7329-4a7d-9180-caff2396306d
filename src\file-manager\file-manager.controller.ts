import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { FileManagerService } from './file-manager.service';
import {
  CreateFolderDto,
  RenameFileDto,
  MoveToTrashDto,
  UploadFileDto,
  ListFilesDto,
} from './dto';
import { FileManager } from './file-manager.entity';

// Note: You'll need to implement your authentication guard
// import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('file-manager')
@ApiTags('File Manager')
@ApiBearerAuth()
// @UseGuards(JwtAuthGuard) // Uncomment when you have auth guard
export class FileManagerController {
  constructor(private readonly fileManagerService: FileManagerService) {}

  @Post('folders')
  @ApiOperation({ summary: 'Create a new folder' })
  @ApiResponse({
    status: 201,
    description: 'Folder created successfully',
    type: FileManager,
  })
  async createFolder(
    @Request() req: any, // Replace with proper user type
    @Body() createFolderDto: CreateFolderDto,
  ): Promise<FileManager> {
    // For now, using a mock user ID. Replace with actual user from JWT token
    const userId = req.user?.id || 1;
    return this.fileManagerService.createFolder(userId, createFolderDto);
  }

  @Get('files')
  @ApiOperation({ summary: 'List files and folders' })
  @ApiResponse({
    status: 200,
    description: 'Files and folders retrieved successfully',
    type: [FileManager],
  })
  async listFiles(
    @Request() req: any,
    @Query() listFilesDto: ListFilesDto,
  ): Promise<FileManager[]> {
    const userId = req.user?.id || 1;
    return this.fileManagerService.listFiles(userId, listFilesDto);
  }

  @Post('files/upload-url')
  @ApiOperation({ summary: 'Get pre-signed URL for file upload' })
  @ApiResponse({
    status: 200,
    description: 'Upload URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        uploadUrl: { type: 'string' },
        fileId: { type: 'number' },
      },
    },
  })
  async getUploadUrl(
    @Request() req: any,
    @Body() uploadFileDto: UploadFileDto,
  ): Promise<{ uploadUrl: string; fileId: number }> {
    const userId = req.user?.id || 1;
    return this.fileManagerService.getUploadUrl(userId, uploadFileDto);
  }

  @Get('files/:id/download-url')
  @ApiOperation({ summary: 'Get pre-signed URL for file download' })
  @ApiParam({ name: 'id', description: 'File ID' })
  @ApiResponse({
    status: 200,
    description: 'Download URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        downloadUrl: { type: 'string' },
      },
    },
  })
  async getDownloadUrl(
    @Request() req: any,
    @Param('id', ParseIntPipe) fileId: number,
  ): Promise<{ downloadUrl: string }> {
    const userId = req.user?.id || 1;
    const downloadUrl = await this.fileManagerService.getDownloadUrl(fileId, userId);
    return { downloadUrl };
  }

  @Patch('files/:id/rename')
  @ApiOperation({ summary: 'Rename a file or folder' })
  @ApiParam({ name: 'id', description: 'File or folder ID' })
  @ApiResponse({
    status: 200,
    description: 'File or folder renamed successfully',
    type: FileManager,
  })
  async renameFile(
    @Request() req: any,
    @Param('id', ParseIntPipe) fileId: number,
    @Body() renameFileDto: RenameFileDto,
  ): Promise<FileManager> {
    const userId = req.user?.id || 1;
    return this.fileManagerService.renameFile(fileId, userId, renameFileDto);
  }

  @Post('files/trash')
  @ApiOperation({ summary: 'Move files/folders to trash' })
  @ApiResponse({
    status: 200,
    description: 'Files moved to trash successfully',
  })
  async moveToTrash(
    @Request() req: any,
    @Body() moveToTrashDto: MoveToTrashDto,
  ): Promise<{ message: string }> {
    const userId = req.user?.id || 1;
    await this.fileManagerService.moveToTrash(userId, moveToTrashDto);
    return { message: 'Files moved to trash successfully' };
  }

  @Patch('files/:id/restore')
  @ApiOperation({ summary: 'Restore file/folder from trash' })
  @ApiParam({ name: 'id', description: 'File or folder ID' })
  @ApiResponse({
    status: 200,
    description: 'File restored from trash successfully',
    type: FileManager,
  })
  async restoreFromTrash(
    @Request() req: any,
    @Param('id', ParseIntPipe) fileId: number,
  ): Promise<FileManager> {
    const userId = req.user?.id || 1;
    return this.fileManagerService.restoreFromTrash(fileId, userId);
  }

  @Delete('files/:id/permanent')
  @ApiOperation({ summary: 'Permanently delete a file/folder' })
  @ApiParam({ name: 'id', description: 'File or folder ID' })
  @ApiResponse({
    status: 200,
    description: 'File permanently deleted successfully',
  })
  async permanentlyDelete(
    @Request() req: any,
    @Param('id', ParseIntPipe) fileId: number,
  ): Promise<{ message: string }> {
    const userId = req.user?.id || 1;
    await this.fileManagerService.permanentlyDelete(fileId, userId);
    return { message: 'File permanently deleted successfully' };
  }

  @Get('folders/:id/breadcrumb')
  @ApiOperation({ summary: 'Get folder breadcrumb path' })
  @ApiParam({ name: 'id', description: 'Folder ID' })
  @ApiResponse({
    status: 200,
    description: 'Breadcrumb path retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          name: { type: 'string' },
        },
      },
    },
  })
  async getFolderBreadcrumb(
    @Request() req: any,
    @Param('id', ParseIntPipe) folderId: number,
  ): Promise<Array<{ id: number; name: string }>> {
    // This would be implemented in the service
    // For now, returning a mock response
    return [
      { id: 0, name: 'Root' },
      { id: folderId, name: 'Current Folder' },
    ];
  }
}
