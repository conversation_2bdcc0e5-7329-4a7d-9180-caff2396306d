import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CountryService } from './country.service';
import { CreateCountryDto } from './dto/createCountry.dto';
import { UpdateCountryDto } from './dto/updateCountry.dto';

@Controller('country')
@ApiTags('country')
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create Country' })
  async createCountry(@Body() createCountryDTO: CreateCountryDto) {
    const { name, code, timezone, flag, region } = createCountryDTO;
    return this.countryService.createCountry(
      name,
      code,
      timezone,
      flag,
      region,
    );
  }

  @Put('update')
  @ApiOperation({ summary: 'Update Country' })
  async updateCountry(@Body() updateCountryDTO: UpdateCountryDto) {
    const { id, name, code, timezone, flag, region } = updateCountryDTO;
    return this.countryService.updateCountry(
      id,
      name,
      code,
      timezone,
      flag,
      region,
    );
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete Country' })
  async deleteCountry(@Param('id') id: number) {
    return this.countryService.deleteCountry(id);
  }

  @Get('findAll')
  @ApiOperation({ summary: 'Get All Countries' })
  async findAll() {
    return this.countryService.findAll();
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Get Country by ID' })
  async findOne(@Param('id') id: number) {
    return this.countryService.findOne(id);
  }

  @Get('findByName')
  @ApiOperation({ summary: 'Get Country by Name' })
  async findByName(@Query('name') name: string) {
    return this.countryService.findByName(name);
  }

  @Get('getAllRegions')
  @ApiOperation({ summary: 'Get All Regions' })
  async getAllRegions() {
    return this.countryService.getAllRegions();
  }

  @Get('getCountriesAndSector')
  @ApiOperation({ summary: 'Get All Countries and Sectors' })
  async getCountriesAndSector() {
    return this.countryService.getCountriesAndSector();
  }

  @Get('getAllCountries')
  @ApiOperation({ summary: 'Get All Countries' })
  async getAllCountries(@Query('region') region?: string) {
    return this.countryService.getAllCountries(region);
  }
}
