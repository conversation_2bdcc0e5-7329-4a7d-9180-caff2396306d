import { Company } from 'src/company/company.entity';
import { Country } from 'src/country/country.entity';
import { JobApplications } from 'src/job_applications/job_applications.entity';
import { People } from 'src/people/people.entity';
import { Sector } from 'src/sector/sector.entity';
import { Users } from 'src/users/users.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Jobs {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'text',
    nullable: true,
    default: null,
  })
  title: string;

  @Column({
    type: 'text',
    nullable: true,
    default: null,
  })
  description: string;

  @Column({
    type: 'enum',
    enum: ['ACTIVE', 'INACTIVE', 'DRAFT', 'ARCHIVED'],
    default: 'DRAFT',
  })
  status: string;

  @Column({
    nullable: true,
    default: null,
    type: 'text',
  })
  job_posting_link: string;

  @Column({ nullable: true })
  job_posting_date: Date;

  @Column({ nullable: true })
  job_closing_date: Date;

  @Column({
    nullable: true,
    type: 'enum',
    enum: ['MANUAL', 'JOB_POST_SCRAPPER', 'COMPANY_PROFILE_SCRAPPER'],
    default: 'JOB_POST_SCRAPPER',
  })
  job_source: string;

  @Column({
    type: 'enum',
    enum: [
      'INTERN',
      'ENTRY_LEVEL',
      'ASSOCIATE',
      'MID_SENIOR_LEVEL',
      'SENIOR_LEVEL',
      'DIRECTOR',
      'EXECUTIVE',
    ],
    default: null,
    nullable: true,
  })
  experience_level: string;

  @Column({
    type: 'enum',
    enum: ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'TEMPORARY', 'INTERNSHIP'],
    default: null,
    nullable: true,
  })
  job_type: string;

  @Column({
    type: 'enum',
    enum: ['REMOTE', 'ONSITE', 'HYBRID'],
    default: null,
    nullable: true,
  })
  job_location_type: string;

  @Column({ nullable: true })
  SR_specified_industry: string;

  @Column({ nullable: true })
  applicants: string;

  @Column({ nullable: true })
  job_location_city: string;

  @Column({ nullable: true })
  job_location_state: string;

  @Column({ nullable: true })
  job_location_country: string;

  @Column({ nullable: true })
  job_location_zip: string;

  @Column({
    type: 'enum',
    enum: [
      'USD',
      'PKR',
      'EUR',
      'GBP',
      'AED',
      'SAR',
      'QAR',
      'OMR',
      'KWD',
      'BHD',
      'CAD',
      'AUD',
      'JPY',
      'CNY',
      'INR',
      'BDT',
      'LKR',
      'NPR',
      'AFN',
      'IRR',
      'IQD',
      'LYD',
      'EGP',
      'SYP',
      'YER',
      'JOD',
      'ILS',
      'TRY',
      'UAH',
      'RUB',
      'KZT',
      'UZS',
      'AZN',
      'GEL',
      'AMD',
      'BYN',
      'PLN',
      'CZK',
      'HUF',
      'HRK',
      'RON',
      'BGN',
      'DKK',
      'SEK',
      'NOK',
      'ISK',
      'CHF',
      'BAM',
      'MKD',
      'ALL',
      'RSD',
      'BRL',
      'ARS',
      'CLP',
      'COP',
      'MXN',
      'PEN',
      'UYU',
      'VEF',
      'BOB',
      'PYG',
      'GTQ',
      'HNL',
      'NIO',
      'CRC',
      'PAB',
      'BSD',
      'JMD',
      'TTD',
      'XCD',
      'DOP',
      'HTG',
      'CUP',
      'CUC',
      'ANG',
      'AWG',
      'BBD',
      'BMD',
      'KYD',
      'SBD',
      'FJD',
      'TOP',
      'VUV',
    ],
    default: null,
    nullable: true,
  })
  currency: string;

  @Column({
    default: null,
    nullable: true,
  })
  salary_min: number;

  @Column({
    default: null,
    nullable: true,
  })
  salary_max: number;

  @Column({
    type: 'enum',
    enum: ['HOURLY', 'DAILY', 'WEEKLY', 'BIWEEKLY', 'MONTHLY', 'ANNUALLY'],
    default: null,
    nullable: true,
  })
  salary_period: string;

  @Column({
    default: null,
    nullable: true,
  })
  industry: string;

  @Column({
    default: null,
    nullable: true,
  })
  skill_required: string;

  @Column({
    default: null,
    nullable: true,
    type: 'simple-array',
  })
  skills: string[];

  @Column({ nullable: true })
  scrapper_profile_name: string;

  @ManyToOne(() => Company, (company) => company.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  company: Company;

  @Column({
    default: null,
    nullable: true,
  })
  companyId: number;

  @ManyToOne(() => People, (person) => person.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  person: People;

  @Column({
    default: null,
    nullable: true,
  })
  personId: number;

  @ManyToOne(() => Users, (user) => user.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  user: Users;

  @Column({
    default: null,
    nullable: true,
  })
  userId: string;

  @ManyToOne(() => Sector, (sector) => sector.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  sector: Sector;

  @Column({
    default: null,
    nullable: true,
  })
  sectorId: number;

  @ManyToOne(() => Country, (country) => country.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  country: Country;

  @Column({
    default: null,
    nullable: true,
  })
  countryId: number;

  @OneToMany(
    () => JobApplications,
    (job_applications) => job_applications.job,
    {
      nullable: true,
    },
  )
  job_applications: JobApplications[]; // ACM user who added this person

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
