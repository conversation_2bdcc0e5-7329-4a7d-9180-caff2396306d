import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class RoleSequenceDto {
  @ApiProperty({ description: 'Name of the role sequence', example: 'Admin' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the role sequence',
    example: 'Administrator role',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Status of the role sequence',
    example: 'active',
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'User ID associated with the role sequence',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;
}

export class UpdateRoleSequenceDto {
  @ApiProperty({ description: 'Name of the role sequence', example: 'Admin' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Description of the role sequence',
    example: 'Administrator role',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Status of the role sequence',
    example: 'active',
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    description: 'User ID associated with the role sequence',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;
}
