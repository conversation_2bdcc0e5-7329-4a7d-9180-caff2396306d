import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Country } from 'src/country/country.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { People } from 'src/people/people.entity';
import { Sector } from 'src/sector/sector.entity';
import { Users } from 'src/users/users.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';

@Entity()
export class Company {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 500, nullable: true })
  @Index({ unique: false })
  public_id: string;

  @Column({ length: 500, nullable: true })
  @Index()
  company_id: string;

  @Column()
  @Index({ unique: false })
  name: string;

  @Column({ nullable: true })
  @Index()
  profile_url: string;

  @Column({ nullable: true })
  profile_url_encoded: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  cover_photo: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  tagline: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  company_country: string;

  @Column({ type: 'int', nullable: true })
  staff_count: number;

  @Column({ type: 'int', nullable: true })
  staff_count_range_start: number;

  @Column({ type: 'int', nullable: true })
  staff_count_range_end: number;

  @Column({ type: 'int', nullable: true })
  followers_count: number;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  founded: string;

  @Column({ nullable: true, type: 'simple-array' })
  @Index()
  employee_benefits: string[];

  @Column({ type: 'text', nullable: true })
  @Index()
  industry: string;

  @Column('simple-array', { nullable: true })
  specialities: string[];

  @Column({ nullable: true })
  @Index()
  company_email: string;

  @Column({ nullable: true })
  company_phone: string;

  @Column({ nullable: true })
  region: string;

  @Column({ type: 'int', nullable: true })
  scrapper_level: number;

  @Column({ nullable: true, default: false })
  is_scrapped_fully: boolean;

  @Column({ nullable: true })
  headquarter_country: string;

  @Column({ nullable: true })
  headquarter_city: string;

  @Column({ nullable: true })
  headquarter_geographic_area: string;

  @Column({ nullable: true })
  headquarter_line1: string;

  @Column({ nullable: true })
  headquarter_line2: string;

  @Column({ nullable: true })
  headquarter_postal_code: string;

  @Column({
    nullable: true,
    type: 'enum',
    enum: ['MANUAL', 'JOB_POST_SCRAPPER', 'COMPANY_PROFILE_SCRAPPER'],
    default: 'JOB_POST_SCRAPPER',
  })
  company_source: string;

  @ManyToOne(() => Users, (users) => users.companies, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'userId' })
  user: Users;

  @Column({ nullable: true })
  userId: string;

  @ManyToOne(() => Sector, (sector) => sector.companies, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'sectorId' })
  sector: Sector;

  @Column({ nullable: true })
  sectorId: number;

  @ManyToOne(() => Country, (country) => country.companies, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'countryId' })
  country: Country;

  @Column({ nullable: true })
  scrapper_profile_name: string;

  @Column({ type: 'int', nullable: true })
  @Index()
  countryId: number;

  @OneToMany(() => People, (people) => people.company)
  people: People[];

  @OneToMany(() => PeopleAssignment, (assignment) => assignment.company)
  userAssigned: PeopleAssignment[];

  @OneToMany(() => Jobs, (jobs) => jobs.company)
  jobs: Jobs[];

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
