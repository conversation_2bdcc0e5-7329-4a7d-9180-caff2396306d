import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

export class AddEmailFromSpreadsheetDto {
  @IsString()
  userId: string;

  @IsArray()
  businessEmails: Array<{
    id: number;
    email: string;
    is_default: boolean;
    is_replaced: boolean;
    isReplacedBy: number;
    person_id: number;
    websiteLink: string;
  }>;
}

export class AddReplacementEmailsDto {
  @IsString()
  userId: string;
  person_id: string;
  @IsArray()
  replaced_emails: Array<{
    id: number;
    email: string;
    is_default: boolean;
    is_replaced: boolean;
    isReplacedBy: number;
    personId: number;
    websiteLink: string;
  }>;
}

export class MarkAsEmailNotFoundDto {
  @IsString()
  userId: string;
  @IsArray()
  persons: number[];
}
