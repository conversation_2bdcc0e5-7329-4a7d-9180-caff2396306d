import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TwillioService } from './twillio.service';
import { TwillioController } from './twillio.controller';
import { WhatsAppMessage } from './whatsapp-message.entity';
import { TwillioGateway } from './twillio.gateway';
import { MessagesModule } from '../messages/messages.module';
import { EmailModule } from 'src/email/email.module';

@Module({
  imports: [TypeOrmModule.forFeature([WhatsAppMessage]), MessagesModule, EmailModule],
  providers: [TwillioService, TwillioGateway],
  controllers: [TwillioController],
  exports: [TypeOrmModule, TwillioService, TwillioGateway],
})
export class TwillioModule {}
