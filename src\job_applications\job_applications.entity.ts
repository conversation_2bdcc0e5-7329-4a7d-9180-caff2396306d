import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('job_applications')
export class JobApplications {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ['PENDING', 'ACCEPTED', 'REJECTED'],
    default: 'PENDING',
  })
  @Index()
  status: string;

  @Column({
    type: 'enum',
    enum: ['APPLIED', 'INTERVIEWED', 'SELECTED', 'REJECTED', 'FAVOURITES'],
    default: 'APPLIED',
  })
  @Index()
  action_type: string;
  // many applications can be for one job
  @ManyToOne(() => Jobs, (job) => job.job_applications, { nullable: false })
  job: Jobs;

  @Column({ type: 'int', nullable: false })
  @Index()
  jobId: number;

  // many applications can be for one job
  @ManyToOne(() => Users, (user) => user.job_applications, { nullable: false })
  user: Users;

  @Column({ type: 'varchar', nullable: false })
  @Index()
  userId: string;
}
