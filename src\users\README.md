# Users Module Test Suite

This directory contains comprehensive Jest test cases for the Users module of the Ultimate CRM backend application.

## Test Files Overview

### Unit Tests

1. **`users.service.spec.ts`** - Tests for UsersService
   - User creation, verification, and management
   - Password hashing and validation
   - Email verification workflows
   - User filtering and search functionality
   - Error handling and edge cases

2. **`users.controller.spec.ts`** - Tests for UsersController
   - HTTP endpoint testing
   - Request/response validation
   - Authentication and authorization flows
   - Input validation and error responses

3. **`auth.service.spec.ts`** - Tests for AuthService
   - JWT token generation
   - Email verification code sending
   - Password comparison utilities

4. **`users.entity.spec.ts`** - Tests for Users Entity
   - Entity structure validation
   - Enum value validation
   - Data type verification
   - Relationship testing

5. **`users.module.spec.ts`** - Integration tests for UsersModule
   - Module compilation and dependency injection
   - Service integration testing
   - Configuration validation

6. **`dto/dto.spec.ts`** - Tests for Data Transfer Objects
   - Input validation testing
   - DTO transformation testing
   - Validation rule verification

### End-to-End Tests

7. **`users.e2e.spec.ts`** - End-to-end API testing
   - Full HTTP request/response cycle testing
   - Database integration testing
   - Real-world scenario simulation

### Test Configuration

8. **`jest.config.js`** - Jest configuration for Users module
9. **`test-setup.ts`** - Global test setup and utilities

## Running Tests

### Prerequisites

Make sure you have the following installed:
- Node.js (v16 or higher)
- npm or yarn
- All project dependencies installed

### Running All Tests

```bash
# Run all tests in the project
npm test

# Run tests with coverage
npm run test:cov

# Run tests in watch mode
npm run test:watch
```

### Running Users Module Tests Only

```bash
# Run only users module tests
npm test -- --testPathPattern=users

# Run with coverage for users module only
npm test -- --testPathPattern=users --coverage

# Run specific test file
npm test users.service.spec.ts

# Run tests matching a pattern
npm test -- --testNamePattern="createUser"
```

### Running End-to-End Tests

```bash
# Run e2e tests
npm run test:e2e

# Run users e2e tests specifically
npm test users.e2e.spec.ts
```

## Test Coverage

The test suite aims for high coverage across:

- **Statements**: 90%+
- **Branches**: 85%+
- **Functions**: 90%+
- **Lines**: 90%+

### Viewing Coverage Reports

After running tests with coverage:

```bash
# Open HTML coverage report
open coverage/lcov-report/index.html

# View coverage in terminal
npm run test:cov
```

## Test Structure

### Service Tests

Each service test follows this pattern:

```typescript
describe('ServiceName', () => {
  let service: ServiceName;
  let mockDependency: MockType;

  beforeEach(async () => {
    // Setup test module
  });

  describe('methodName', () => {
    it('should handle success case', async () => {
      // Test implementation
    });

    it('should handle error case', async () => {
      // Test implementation
    });
  });
});
```

### Controller Tests

Controller tests focus on HTTP layer testing:

```typescript
describe('ControllerName', () => {
  let controller: ControllerName;
  let service: ServiceName;

  beforeEach(async () => {
    // Setup test module with mocked services
  });

  describe('endpoint', () => {
    it('should return expected response', async () => {
      // Test HTTP endpoint behavior
    });
  });
});
```

### E2E Tests

End-to-end tests simulate real API calls:

```typescript
describe('Module (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Setup test application
  });

  it('/endpoint (POST)', () => {
    return request(app.getHttpServer())
      .post('/endpoint')
      .send(testData)
      .expect(201);
  });
});
```

## Test Data and Mocks

### Mock Data

Test utilities provide consistent mock data:

```typescript
const mockUser = testUtils.createMockUser();
const mockCreateUserDto = testUtils.createMockCreateUserDto();
```

### Database Mocking

For unit tests, database operations are mocked:

```typescript
const mockRepository = {
  findOne: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};
```

For integration tests, an in-memory SQLite database is used.

## Custom Matchers

The test suite includes custom Jest matchers:

```typescript
expect('<EMAIL>').toBeValidEmail();
expect('123e4567-e89b-12d3-a456-************').toBeValidUUID();
expect(jwtToken).toBeValidJWT();
```

## Debugging Tests

### Running Tests in Debug Mode

```bash
# Debug specific test
npm run test:debug -- --testNamePattern="specific test"

# Debug with VS Code
# Set breakpoints and use "Jest Debug" configuration
```

### Common Issues

1. **Database Connection Issues**: Ensure test database is properly configured
2. **Mock Issues**: Verify all dependencies are properly mocked
3. **Async Issues**: Ensure proper async/await usage in tests
4. **Environment Variables**: Check test environment variables are set

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Clear Naming**: Test names should clearly describe what is being tested
3. **Arrange-Act-Assert**: Follow the AAA pattern
4. **Mock External Dependencies**: Don't rely on external services in tests
5. **Test Edge Cases**: Include error scenarios and boundary conditions

## Contributing

When adding new features to the Users module:

1. Write tests first (TDD approach)
2. Ensure all tests pass
3. Maintain or improve test coverage
4. Update this documentation if needed

## Troubleshooting

### Common Test Failures

1. **Module compilation errors**: Check imports and dependencies
2. **Database errors**: Verify test database configuration
3. **Timeout errors**: Increase test timeout for slow operations
4. **Mock errors**: Ensure mocks match actual service interfaces

### Getting Help

If you encounter issues with the test suite:

1. Check the test output for specific error messages
2. Verify your environment setup
3. Review the test documentation
4. Check for similar issues in the project's issue tracker
