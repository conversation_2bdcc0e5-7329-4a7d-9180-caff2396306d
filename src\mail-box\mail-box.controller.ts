import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { MailBoxService } from './mail-box.service';
import { MailBoxDto } from './dto/mailBox.dto';

@ApiTags('mail-box')
@Controller('mail-box')
export class MailBoxController {
  constructor(private readonly mailBoxService: MailBoxService) {}

  @ApiOperation({
    summary: 'Get all mailboxes',
  })
  @Post('create')
  async create(@Body() mailBoxDto: MailBoxDto) {
    return await this.mailBoxService.create(mailBoxDto);
  }

  @ApiOperation({
    summary: 'Get all mailboxes',
  })
  @Get('all')
  async findAll(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('searchString') searchString: string,
  ) {
    return await this.mailBoxService.findAll(page, pageSize, searchString);
  }

  // updateAssignEmailToBd
  @Put('update-assign-email-to-bd')
  @ApiOperation({
    summary: 'Update assign email to bd',
  })
  async updateAssignEmailToBd(
    @Body('id') id: number,
    @Body('assigneeId') assigneeId: string,
  ) {
    if (!id || !assigneeId) {
      throw new Error('id and assigneeId are required');
    }
    return await this.mailBoxService.updateAssignEmailToBd(id, assigneeId);
  }

  @Get('email')
  @ApiOperation({
    summary: 'Get mailbox by email and type',
  })
  @ApiQuery({
    name: 'email',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
  })
  async findByEmail(
    @Query('email') email: string,
    @Query('type') type: string,
    @Query('category') category: string,
    @Query('pageSize') pageSize: string,
  ) {
    return await this.mailBoxService.findByEmail(
      email,
      type,
      category,
      pageSize,
    );
  }

  // updateProspectStatus
  @Put('update-prospect-status')
  @ApiOperation({
    summary: 'Update prospect status',
  })
  @ApiBody({
    description: 'Update prospect status',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', description: 'Email address' },
        status: { type: 'string', description: 'New status' },
      },
      required: ['email', 'status'],
    },
  })
  async updateProspectStatus(
    @Body('email') email: string,
    @Body('status') status: string,
  ) {
    if (!email || !status) {
      throw new Error('email and status are required');
    }
    return await this.mailBoxService.updateProspectStatus(email, status);
  }

  @ApiOperation({
    summary: 'Get mailbox by id',
  })
  @Get(':id')
  async findById(@Param('id') id: number) {
    return await this.mailBoxService.findById(id);
  }

  // getMyTrialLeads
  @Get('my-trial-leads/:assigneeId')
  @ApiOperation({
    summary: 'Get my trial leads',
  })
  @ApiQuery({
    name: 'assigneeId',
    required: false,
    type: String,
  })
  async getMyTrialLeads(
    @Param('assigneeId') assigneeId: string,
    @Query('pageSize') pageSize: string,
  ) {
    return await this.mailBoxService.getMyTrialLeads(assigneeId, pageSize);
  }
  @ApiOperation({
    summary: 'Update mailbox by id',
  })
  @Put(':id')
  async update(@Param('id') id: number, @Body() mailBoxDto: MailBoxDto) {
    return await this.mailBoxService.update(id, mailBoxDto);
  }

  @ApiOperation({
    summary: 'Update ReadStatus by id',
  })
  @Put('update-read-status/:id')
  async updateReadStatus(@Param('id') id: number) {
    return await this.mailBoxService.updateReadStatusById(id);
  }

  @ApiOperation({
    summary: 'Delete mailbox by id',
  })
  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.mailBoxService.delete(id);
  }

  @ApiOperation({
    summary: 'Get mailbox by type',
  })
  @Get('type/:type')
  async findByType(@Param('type') type: string) {
    return await this.mailBoxService.findByType(type);
  }

  @ApiOperation({
    summary: 'Delete all mailboxes',
  })
  @Delete('delete-all')
  async deleteAll() {
    return await this.mailBoxService.deleteAll();
  }

  // saveAsDraft
  @Post('save-as-draft')
  @ApiOperation({
    summary: 'Save email as draft',
  })
  @ApiBody({
    description: 'Save email as draft',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', description: 'Email address' },
        type: { type: 'string', description: 'Type of email' },
        category: { type: 'string', description: 'Category of email' },
        sender: { type: 'string', description: 'Sender email address' },
        recipient: { type: 'string', description: 'Recipient email address' },
        subject: { type: 'string', description: 'Email subject' },
        body: { type: 'string', description: 'Email body' },
        attachments: {
          type: 'array',
          items: { type: 'string', description: 'Attachment URL' },
        },
        cc: {
          type: 'array',
          items: { type: 'string', description: 'CC email addresses' },
        },
        bcc: {
          type: 'array',
          items: { type: 'string', description: 'BCC email addresses' },
        },
        reply_to: {
          type: 'array',
          items: { type: 'string', description: 'Reply-to email addresses' },
        },
      },
      required: ['email', 'subject', 'body'],
    },
  })
  async saveAsDraft(
    @Body('email') email: string,
    @Body('type') type: string,
    @Body('category') category: string,
    @Body('sender') sender: string,
    @Body('recipient') recipient: string,
    @Body('subject') subject: string,
    @Body('body') body: string,
    @Body('attachments') attachments: string[],
    @Body('cc') cc: string[],
    @Body('bcc') bcc: string[],
    @Body('reply_to') reply_to: string,
  ): Promise<any> {
    if (!email || !subject || !body) {
      throw new Error('email, subject and body are required');
    }
    return await this.mailBoxService.saveAsDraft(
      email,
      type,
      sender,
      recipient,
      subject,
      body,
      attachments,
      cc,
      bcc,
      reply_to,
    );
  }

  // scheduleEmail
  @Post('schedule-email')
  @ApiOperation({
    summary: 'Schedule an email',
  })
  @ApiBody({
    description: 'Schedule an email',
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', description: 'Email address' },
        type: { type: 'string', description: 'Type of email' },
        sender: { type: 'string', description: 'Sender email address' },
        recipient: { type: 'string', description: 'Recipient email address' },
        subject: { type: 'string', description: 'Email subject' },
        body: { type: 'string', description: 'Email body' },
        attachments: {
          type: 'array',
          items: { type: 'string', description: 'Attachment URL' },
        },
        cc: {
          type: 'array',
          items: { type: 'string', description: 'CC email addresses' },
        },
        bcc: {
          type: 'array',
          items: { type: 'string', description: 'BCC email addresses' },
        },
        reply_to: {
          type: 'array',
          items: { type: 'string', description: 'Reply-to email addresses' },
        },
        schedule_date: {
          type: 'string',
          format: 'date-time',
          description: 'Scheduled date and time',
        },
      },
      required: ['email', 'type', 'sender', 'recipient', 'schedule_date'],
    },
  })
  async scheduleEmail(
    @Body('email') email: string,
    @Body('type') type: string,
    @Body('sender') sender: string,
    @Body('recipient') recipient: string,
    @Body('subject') subject?: string,
    @Body('body') body?: string,
    @Body('attachments') attachments?: string[],
    @Body('cc') cc?: string[],
    @Body('bcc') bcc?: string[],
    @Body('reply_to') reply_to?: string,
    @Body('schedule_date') schedule_date?: Date,
  ): Promise<any> {
    if (!email || !type || !sender || !recipient || !schedule_date) {
      throw new Error(
        'email, type, sender, recipient, and schedule_date are required',
      );
    }
    return await this.mailBoxService.scheduleEmail(
      email,
      type,
      sender,
      recipient,
      subject,
      body,
      attachments,
      cc,
      bcc,
      reply_to,
      schedule_date,
    );
  }
}
