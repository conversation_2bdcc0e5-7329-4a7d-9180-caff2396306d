import { People } from 'src/people/people.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
@Entity()
export class Languages {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: ['BASIC', 'INTERMEDIATE', 'ADVANCED', 'FLUENT', 'NATIVE'],
    default: 'FLUENT',
  })
  proficiency_level: string;

  @ManyToOne(() => People, (person) => person.languages)
  person: People;

  @Column({
    type: 'int',
    nullable: true,
  })
  personId: number;
}
