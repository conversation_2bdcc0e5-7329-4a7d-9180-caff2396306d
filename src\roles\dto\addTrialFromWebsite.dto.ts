import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsBoolean } from 'class-validator';

export class AddTrialFromWebsiteDto {
  // Step 1
  @ApiProperty({ description: 'Main title of the role', required: false })
  @IsString()
  @IsOptional()
  roleMainTitle?: string;

  @ApiProperty({
    description: 'Relevant titles for the role',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  roleRelevantTitles?: string[];

  @ApiProperty({ description: 'Location', required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({ description: 'Postal code', required: false })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiProperty({ description: 'Radius in miles', required: false })
  @IsString()
  @IsOptional()
  radiusMiles?: string;

  @ApiProperty({ description: 'Willing to relocate', required: false })
  @IsBoolean()
  @IsOptional()
  willingToRelocate?: boolean;

  @ApiProperty({ description: 'Open to work', required: false })
  @IsString()
  @IsOptional()
  openToWork?: string;

  // Step 2
  @ApiProperty({ description: 'Minimum salary', required: false })
  @IsString()
  @IsOptional()
  salaryMin?: string;

  @ApiProperty({ description: 'Maximum salary', required: false })
  @IsString()
  @IsOptional()
  salaryMax?: string;

  @ApiProperty({
    description: 'Essential qualifications',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  essentialQualifications?: string[];

  @ApiProperty({
    description: 'Essential requirements',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  essentialRequirements?: string[];

  @ApiProperty({
    description: 'Desirable or preferred requirements',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  desireableOrPreferredRequirements?: string[];

  @ApiProperty({
    description: 'Companies of interest',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  companiesOfInterest?: string[];

  @ApiProperty({
    description: 'Major reason for rejected CVs',
    required: false,
  })
  @IsString()
  @IsOptional()
  majorReasonForRejectedCVs?: string;

  @ApiProperty({
    description: 'Who do we want',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  whoDoWeWant?: string[];

  @ApiProperty({
    description: 'Who do we not want',
    type: [String],
    required: false,
  })
  @IsArray()
  @IsOptional()
  whoDoWeNotWant?: string[];

  @ApiProperty({ description: 'Contract or permanent', required: false })
  @IsString()
  @IsOptional()
  contractPermanent?: string;

  @ApiProperty({ description: 'Role industry', required: false })
  @IsString()
  @IsOptional()
  roleIndustry?: string;

  // Step 3
  @ApiProperty({ description: 'Full name', required: false })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ description: 'Job title', required: false })
  @IsString()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({ description: 'Business email', required: false })
  @IsString()
  @IsOptional()
  businessEmail?: string;

  @ApiProperty({ description: 'Company phone', required: false })
  @IsString()
  @IsOptional()
  companyPhone?: string;

  @ApiProperty({ description: 'Company website', required: false })
  @IsString()
  @IsOptional()
  companyWebsite?: string;

  @ApiProperty({ description: 'Company industry', required: false })
  @IsString()
  @IsOptional()
  companyIndustry?: string;

  // companyName?: string;
  @ApiProperty({ description: 'Company name', required: false })
  @IsString()
  @IsOptional()
  companyName?: string;

  @ApiProperty({ description: 'Country', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  // attachments
  @ApiProperty({
    description: 'Attachments (e.g., CV, cover letter)',
    type: [String],
    required: false,
  })
  @IsString()
  @IsOptional()
  attachments?: string; // Assuming these are URLs or file paths
}
