<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queue Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .queue-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .queue-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .queue-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-transform: uppercase;
        }
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-disconnected { background-color: #ff9800; }
        .refresh-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background-color: #1976D2;
        }
        .last-updated {
            text-align: center;
            color: #666;
            margin-top: 20px;
        }
        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Queue Dashboard</h1>
            <button class="refresh-btn" onclick="loadQueueStats()">Refresh</button>
        </div>
        
        <div id="error-container"></div>
        <div id="queue-container" class="queue-grid"></div>
        <div id="last-updated" class="last-updated"></div>
    </div>

    <script>
        async function loadQueueStats() {
            try {
                const response = await fetch('/queue/stats');
                const data = await response.json();
                
                document.getElementById('error-container').innerHTML = '';
                
                if (Array.isArray(data)) {
                    displayQueues(data);
                } else {
                    throw new Error('Invalid response format');
                }
                
                document.getElementById('last-updated').textContent = 
                    `Last updated: ${new Date().toLocaleTimeString()}`;
                    
            } catch (error) {
                document.getElementById('error-container').innerHTML = 
                    `<div class="error-message">Error loading queue stats: ${error.message}</div>`;
                console.error('Error loading queue stats:', error);
            }
        }

        function displayQueues(queues) {
            const container = document.getElementById('queue-container');
            container.innerHTML = '';
            
            queues.forEach(queue => {
                const card = document.createElement('div');
                card.className = 'queue-card';
                
                const statusClass = queue.status === 'connected' ? 'status-connected' : 
                                  queue.status === 'error' ? 'status-error' : 'status-disconnected';
                
                card.innerHTML = `
                    <div class="queue-name">
                        <span class="status-indicator ${statusClass}"></span>
                        ${queue.name}
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Waiting:</span>
                        <span class="stat-value">${queue.waiting || 0}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Active:</span>
                        <span class="stat-value">${queue.active || 0}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Completed:</span>
                        <span class="stat-value">${queue.completed || 0}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Failed:</span>
                        <span class="stat-value">${queue.failed || 0}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Delayed:</span>
                        <span class="stat-value">${queue.delayed || 0}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Total:</span>
                        <span class="stat-value">${queue.total || 0}</span>
                    </div>
                    ${queue.error ? `<div style="color: red; margin-top: 10px; font-size: 12px;">${queue.error}</div>` : ''}
                `;
                
                container.appendChild(card);
            });
        }

        // Load stats on page load
        loadQueueStats();
        
        // Auto-refresh every 5 seconds
        setInterval(loadQueueStats, 5000);
    </script>
</body>
</html>
