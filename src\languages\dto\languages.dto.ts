import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsString } from 'class-validator';

export class LanguageDto {
  @ApiProperty({
    description: 'The name of the language',
    example: 'English',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The level of the language',
    example: 'Fluent',
    required: true,
    type: 'enum',
    enum: ['BASIC', 'INTERMEDIATE', 'ADVANCED', 'FLUENT', 'NATIVE'],
  })
  @IsEnum(['BASIC', 'INTERMEDIATE', 'ADVANCED', 'FLUENT', 'NATIVE'])
  @IsString()
  proficiency_level: string;

  @ApiProperty({
    description: 'The id of the person',
    example: 1,
    required: true,
  })
  @IsNumber()
  personId: number;
}

export class UpdateLanguagesDto extends PartialType(LanguageDto) {
  @ApiProperty({
    description: 'The id of the language',
    example: 1,
    required: true,
  })
  @IsNumber()
  id: number;
}
