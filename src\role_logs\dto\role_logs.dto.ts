import { Transform } from "class-transformer"
import { IsOptional } from "class-validator"


export class GetTrialLogsByRoleIdDto {
    @IsOptional()
      roleNumber?: string

    @IsOptional()
      clientNumber?: string

    @IsOptional()
      roleDate?: string

    @IsOptional()
      endDate?: string

    @IsOptional()
    @Transform(({ value }) => value === 'true')
      isAdvance?: boolean

    @IsOptional()
    @Transform(({ value }) => value === 'true')
      isPrevious?: boolean

    @IsOptional()
      bdUserId?: string

    @IsOptional()
      serviceId?: string

    @IsOptional()
      userId?: string

    @IsOptional()
      acmUserId?: string

    @IsOptional()
      roleId?: string

    @IsOptional()
      searchString?: string

    @IsOptional()
      page?: string

    @IsOptional()
      pageSize?: string

}