import {
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleCandidate } from './role_candidates.entity';
import { Repository } from 'typeorm';
import { RoleCandidateDto } from './dto/role_candidate.dto';
import { Roles } from 'src/roles/roles.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Qualifications } from 'src/qualifications/qualifications.entity';
import { Languages } from 'src/languages/langauges.entity';
import { PersonSkill } from 'src/skills/skills.entity';
import { QualificationsDto } from 'src/qualifications/dto/qualifications.dto';
import { ExperienceDto } from 'src/experience/dto/experience.dto';
import { PersonSkillDto } from 'src/skills/dto/person-skill.dto';
import { PersonEmailDto } from 'src/emails/dto/person-email.dto';
import { PersonPhoneDto } from 'src/phone/dto/person-phone.dto';
import { LanguageDto } from 'src/languages/dto/languages.dto';
import { PersonSource } from 'src/people/dto/people.enums';
import { S3bucketService } from 'src/s3bucket/s3bucket.service';

@Injectable()
export class RoleCandidatesService {
  constructor(
    @InjectRepository(RoleCandidate)
    private readonly roleCandidateRepository: Repository<RoleCandidate>,
    @InjectRepository(Roles)
    private readonly rolesRepository: Repository<Roles>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private readonly personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(PersonPhone)
    private readonly personPhoneRepository: Repository<PersonPhone>,
    @InjectRepository(Qualifications)
    private readonly qualificationsRepository: Repository<Qualifications>,
    @InjectRepository(Languages)
    private readonly languagesRepository: Repository<Languages>,
    @InjectRepository(PersonSkill)
    private readonly personSkillRepository: Repository<PersonSkill>,
    private readonly s3BucketService: S3bucketService,
  ) {}

  async createRoleCandidate(
    roleCandidateData: RoleCandidateDto,
  ): Promise<RoleCandidate> {
    try {
      // find role
      const role = await this.rolesRepository.findOneBy({
        id: roleCandidateData.roleId,
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }

      // create and save role candidate
      const roleCandidate =
        this.roleCandidateRepository.create(roleCandidateData);
      roleCandidate.role = role; // associate the role
      return await this.roleCandidateRepository.save({ ...roleCandidate });
    } catch (error) {
      console.error('Error creating role candidate:', error);
      throw new InternalServerErrorException('Failed to create role candidate');
    }
  }

  async addLinkedinToRole(
    businessEmail: string[],
    businessNumber: string[],
    personalEmail: string[],
    personalNumber: string[],
    profile_url: string,
    profileType: string,
    roleId: number,
    userId: string,
    clientId: number,
    prospectId: number,
    is_willing_to_relocate?: boolean,
  ): Promise<RoleCandidate> {
    try {
      // Step 1: Fetch role
      const role = await this.rolesRepository.findOneBy({ id: Number(roleId) });
      if (!role) throw new NotFoundException('Role not found');

      // Step 2: Check for existing profile
      let profile = await this.peopleRepository.findOneBy({ profile_url });

      // check if Role Candidate already exists
      if (profile !== null) {
        const existingRoleCandidate =
          await this.roleCandidateRepository.findOne({
            where: {
              candidateId: profile?.id,
              roleId: Number(roleId),
            },
          });
        console.log('Checking for existing role candidate:', {
          candidateId: profile?.id,
          roleId: Number(roleId),
        });
        console.log('existing Profile:', existingRoleCandidate);
        if (existingRoleCandidate) {
          return existingRoleCandidate; // Return existing RoleCandidate if found
        }
      }

      // Step 3: Create profile if not found
      if (!profile) {
        profile = this.peopleRepository.create({
          profile_url,
          userId,
          person_type: 'CANDIDATE',
          profile_source: PersonSource.LINKEDIN,
        });
        profile = await this.peopleRepository.save(profile);
      }

      // Step 4: Create RoleCandidate
      // console.log('creating new role candidate:', profile);
      const roleCandidate = this.roleCandidateRepository.create({
        source_type: 'LINKEDIN',
        profile_url,
        profile_source: PersonSource.LINKEDIN,
        li_type: profileType,
        is_willing_to_relocate: is_willing_to_relocate ?? false,
        is_accepted: false,
        candidate_status: 'PENDING',
        role,
        roleId: Number(roleId),
        clientId,
        prospectId,
        userId,
        candidateId: profile.id,
      });

      // Step 5: Add contact details in parallel
      await Promise.all([
        this.addEmails(profile.id, businessEmail, 'BUSINESS'),
        this.addEmails(profile.id, personalEmail, 'PERSONAL'),
        this.addPhones(profile.id, businessNumber, 'BUSINESS'),
        this.addPhones(profile.id, personalNumber, 'PERSONAL'),
      ]);

      // Step 6: Save and return
      return await this.roleCandidateRepository.save(roleCandidate);
    } catch (error) {
      console.error('Error adding LinkedIn to role:', error);
      throw new InternalServerErrorException({
        error: error.message,
        message: error.message,
      });
    }
  }

  // ✅ Email Helper
  private async addEmails(
    personId: number,
    emails: string[],
    type: 'BUSINESS' | 'PERSONAL',
  ): Promise<void> {
    if (emails.length === 0) return;

    const lowerEmails = emails.map((e) => e.trim().toLowerCase());
    const uniqueEmails = [...new Set(lowerEmails)];

    for (const email of uniqueEmails) {
      try {
        const exists = await this.personEmailRepository.findOneBy({ email });
        if (exists) continue; // Skip globally existing emails

        const entity = this.personEmailRepository.create({
          email,
          email_type: type,
          personId,
        });

        await this.personEmailRepository.save(entity);
      } catch (err) {
        // Log and skip duplicates (race conditions or missed edge cases)
        if (err.code === '23505') continue;
        console.error('Error adding email:', email, err);
      }
    }
  }

  // ✅ Phone Helper
  private async addPhones(
    personId: number,
    phones: string[],
    type: 'BUSINESS' | 'PERSONAL',
  ): Promise<void> {
    if (phones.length === 0) return;

    const normalizedPhones = phones.map((p) => p.trim());
    const uniquePhones = [...new Set(normalizedPhones)];

    for (const phone of uniquePhones) {
      try {
        const exists = await this.personPhoneRepository.findOneBy({
          phone_number: phone,
        });
        if (exists) continue;

        const entity = this.personPhoneRepository.create({
          phone_number: phone,
          phone_type: type,
          personId,
        });

        await this.personPhoneRepository.save(entity);
      } catch (err) {
        if (err.code === '23505') continue;
        console.error('Error adding phone:', phone, err);
      }
    }
  }

  async getRoleCandidatesV1(
    roleId: number,
    userId: string,
    clientId?: number,
    type?: string,
    source?: string,
  ): Promise<RoleCandidate[]> {
    try {
      const where = {};
      // if source is null, fetch all role candidates, if source is linkedin, fetch only linkedin role candidates, else fetch only cv role candidates
      if (source){
        if (source === 'LINKEDIN') where['source_type'] = 'LINKEDIN';
        if (source === 'JOB_BOARD') where['source_type'] = 'CV';
        if (source === 'JOB_POSTING') where['source_type'] = 'JOB_POSTING';
      }

      if (roleId) where['roleId'] = roleId;
      // if (userId) where['userId'] = userId;
      if (clientId) where['clientId'] = clientId;
      if (type) where['source_type'] = type;
      const candidates = await this.roleCandidateRepository.find({
        where,
        relations: [
          'candidate',
          'candidate.emails',
          'candidate.phones',
          'candidate.qualifications',
          'candidate.experiences',
          'candidate.skills',
          'candidate.languages',
          'user',
        ],
      });
      return candidates;
    } catch (error) {
      console.error('Error fetching role candidates:', error);
      throw new InternalServerErrorException('Failed to fetch role candidates');
    }
  }

  async getRoleCandidates(
    roleId: number,
    userId: string,
    clientId?: number,
    type?: string,
    page = 1,
    limit = 10,
  ): Promise<{ data: RoleCandidate[]; total: number }> {
    try {
      const where = {};
      if (roleId) where['roleId'] = roleId;
      if (clientId) where['clientId'] = clientId;
      if (type) where['source_type'] = type;

      const [data, total] = await this.roleCandidateRepository.findAndCount({
        where,
        relations: [
          'candidate',
          'candidate.emails',
          'candidate.phones',
          'candidate.qualifications',
          'candidate.experiences',
          'candidate.skills',
          'candidate.languages',
          'user',
        ],
        skip: (page - 1) * limit,
        take: limit,
        order: { id: 'DESC' },
      });

      return { data, total };
    } catch (error) {
      console.error('Error fetching role candidates:', error);
      throw new InternalServerErrorException('Failed to fetch role candidates');
    }
  }

  async changeRoleCandidateStatus(
    profile_url: string,
    roleId: number,
    userId: string,
  ): Promise<RoleCandidate[]> {
    try {
      // Step 1: Fetch role candidate
      const roleCandidate = await this.roleCandidateRepository.findOneBy({
        profile_url,
        roleId,
      });
      if (!roleCandidate)
        throw new NotFoundException('Role candidate not found');

      // Step 2: Update status
      roleCandidate.candidate_status = 'PENDING';
      await this.roleCandidateRepository.save(roleCandidate);

      // Step 3: Fetch updated candidates
      const updatedCandidates = await this.roleCandidateRepository.find({
        where: { roleId, userId },
        relations: [
          'candidate',
          'candidate.emails',
          'candidate.phones',
          'candidate.qualifications',
          'candidate.experiences',
          'candidate.skills',
          'candidate.languages',
        ],
      });

      return updatedCandidates;
    } catch (error) {
      console.error('Error changing role candidate status:', error);
      throw new InternalServerErrorException(
        'Failed to change role candidate status',
      );
    }
  }

  async deleteRoleCandidate(
    id: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const roleCandidate = await this.roleCandidateRepository.findOneBy({
        id,
      });

      if (!roleCandidate) {
        throw new NotFoundException('Role candidate not found');
      }

      await this.roleCandidateRepository.delete(id);

      return {
        success: true,
        message: 'Role candidate deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting role candidate:', error);
      throw new InternalServerErrorException('Failed to delete role candidate');
    }
  }

  async addCvToRole(
    data: any,
    file: Express.Multer.File,
  ): Promise<RoleCandidate> {
    try {
      const {
        cv_status,
        is_accepted,
        userId,
        clientId,
        roleId,
        fullName,
        phoneNumber,
        email,
        current_title,
        domain,
        industry,
        city,
        country,
        postal_code,
        street,
        radius,
        salaryMin,
        education,
        experience,
        skills,
        monthBack,
        source,
        cv_text,
      } = data;

      // Step 1: Fetch role
      const role = await this.rolesRepository.findOneBy({ id: Number(roleId) });
      if (!role) throw new NotFoundException('Role not found');

      // Step 2: Upload file to S3 (if present)
      let fileUrl: string | undefined = undefined;
      if (file) {
        fileUrl = await this.s3BucketService.uploadFile(file);
      }

      // Step 3: Check for existing person by profile_url (fileUrl as unique marker)
      let person = await this.peopleRepository.findOneBy({
        profile_url: fileUrl,
      });

      // Step 4: Create person if not found
      if (!person) {
        const [firstName, ...rest] = fullName.split(' ');
        const lastName = rest.join(' ') || '';
        person = this.peopleRepository.create({
          first_name: firstName,
          last_name: lastName,
          userId,
          current_title,
          person_type: 'CANDIDATE',
          domain,
          industry,
          location: `${street}, ${city}, ${postal_code}, ${country}`,
          cv_text: cv_text || '',
          cv_path: fileUrl,
          headline: current_title,
          profile_source: PersonSource.CVL,
          profile_url: fileUrl,
        });
        person = await this.peopleRepository.save(person);
      }

      // Step 5: Check if roleCandidate already exists
      const existing = await this.roleCandidateRepository.findOne({
        where: { profile_url: fileUrl, roleId: Number(roleId) },
      });
      if (existing) return existing;

      // Step 6: Create RoleCandidate
      const roleCandidate = this.roleCandidateRepository.create({
        source_type: 'CV',
        profile_url: fileUrl,
        profile_source: source,
        is_willing_to_relocate: false,
        is_accepted: is_accepted,
        candidate_status: cv_status,
        role,
        roleId: Number(roleId),
        clientId,
        prospectId: null,
        userId,
        candidateId: person.id,
        radius_miles: radius,
        salary: salaryMin,
        months_back: monthBack,
      });

      // add phone number to person_phone and email to person_email
      // await this.addPhones(person.id, [phoneNumber], 'PERSONAL');
      // await this.addEmails(person.id, [email], 'PERSONAL');

      if (skills && skills.length > 0) {
        const skillEntities = skills.map((skill: string) => {
          return this.personSkillRepository.create({
            skill_name: skill,
            personId: person.id,
          });
        });
        await this.personSkillRepository.save(skillEntities);
      }

      // Step 7: Save and return
      return await this.roleCandidateRepository.save(roleCandidate);
    } catch (error) {
      console.error('Error in addCvToRole:', error);
      throw new InternalServerErrorException({
        message: error.messag || 'Failed to add CV to role',
        error: error.message,
      });
    }
  }

  async getPendingLinkedinProfile(): Promise<any> {
    // total, scrapper_number, 
    // numnber/total/rem
    // get all pending Ids, Ids loop mod rem =0  
    try {
      const pendingProfile = await this.roleCandidateRepository.findOne({
        where: { candidate_status: 'PENDING', profile_source: 'LINKEDIN' },
      });

      if (!pendingProfile) {
        throw new NotFoundException('No pending LinkedIn profiles found');
      }

      // update the status to 'IN_PROGRESS'
      pendingProfile.candidate_status = 'IN_PROGRESS';
      await this.roleCandidateRepository.save(pendingProfile);

      return {
        profile: pendingProfile,
      };
    } catch (error) {
      // console.error('Error fetching pending LinkedIn profiles:', error);
      throw new InternalServerErrorException(
        'Failed to fetch pending LinkedIn profiles',
      );
    }
  }

  async getRoleCandidateCount(
    roleId: number,
  ): Promise<{
    linkedin_count: number;
    cv_count: number;
    total_count: number;
  }> {
    try {
      const counts = await this.roleCandidateRepository
        .createQueryBuilder('candidate')
        .select('candidate.source_type', 'source_type')
        .addSelect('COUNT(*)', 'count')
        .where('candidate.roleId = :roleId', { roleId })
        .andWhere('candidate.source_type IN (:...sources)', {
          sources: ['LINKEDIN', 'CV'],
        })
        .groupBy('candidate.source_type')
        .getRawMany();

      const countMap = counts.reduce(
        (acc, curr) => {
          const source = curr.source_type;
          const count = parseInt(curr.count, 10);
          if (source === 'LINKEDIN') acc.linkedin_count = count;
          if (source === 'CV') acc.cv_count = count;
          return acc;
        },
        { linkedin_count: 0, cv_count: 0 },
      );

      // Calculate total count
      countMap.total_count = countMap.linkedin_count + countMap.cv_count;
      return countMap;
    } catch (error) {
      console.error('Error fetching role candidate count:', error);
      throw new InternalServerErrorException(
        'Failed to fetch role candidate count',
      );
    }
  }

  // 360 and Pre qualification handling
  async get360AndPreQualificationCandidates(
    roleId: number,
  ): Promise<RoleCandidate[]> {
    try {
      const candidates = await this.roleCandidateRepository.find({
        where: {
          roleId,
        },
        relations: [
          'role',
          'candidate',
          'candidate.emails',
          'candidate.phones',
        ],
      });

      if (!candidates || candidates.length === 0) {
        throw new NotFoundException('No candidates found for this role');
      }

      return candidates;
    } catch (error) {
      console.error(
        'Error fetching 360 and Pre-qualification candidates:',
        error,
      );
      throw new InternalServerErrorException(
        'Failed to fetch 360 and Pre-qualification candidates',
      );
    }
  }

  // Mark Profile Ready to send connection request
  async markProfileReadyForConnection(
    roleCandidateId: number,
  ): Promise<RoleCandidate> {
    try {
      const roleCandidate = await this.roleCandidateRepository.findOne({
        where: { id: roleCandidateId },
        relations: ['candidate', 'role'],
      });

      if (!roleCandidate) {
        throw new NotFoundException('Role candidate not found');
      }

      // Update the LinkedIn connection send status to READY_TO_SEND
      roleCandidate.li_connection_send_status = 'READY_TO_SEND';

      return await this.roleCandidateRepository.save(roleCandidate);
    } catch (error) {
      console.error('Error marking profile ready for connection:', error);
      throw new InternalServerErrorException(
        'Failed to mark profile ready for connection',
      );
    }
  }

  // get one ready to send connection request profile
  async getOneReadyToSendConnectionProfile(): Promise<RoleCandidate> {
    try {
      const readyProfile = await this.roleCandidateRepository.findOne({
        where: {
          li_connection_send_status: 'READY_TO_SEND',
          source_type: 'LINKEDIN'
        },
        relations: [
          'candidate',
          'candidate.emails',
          'candidate.phones',
          'role',
          'user',
        ],
        order: { created_at: 'ASC' }, // Get the oldest ready profile first
      });

      if (!readyProfile) {
        throw new NotFoundException('No profiles ready to send connection request found');
      }

      // Update status to indicate it's being processed
      readyProfile.li_connection_send_status = 'SENT';
      await this.roleCandidateRepository.save(readyProfile);

      return readyProfile;
    } catch (error) {
      console.error('Error fetching ready to send connection profile:', error);
      throw new InternalServerErrorException(
        'Failed to fetch ready to send connection profile',
      );
    }
  }

  // update connection request status
  async updateConnectionRequestStatus(
    roleCandidateId: number,
    connectionStatus: 'SENT' | 'NOT_SENT' | 'READY_TO_SEND' | 'RECEIVED' | 'NOT_RECEIVED',
    responseStatus?: string,
  ): Promise<RoleCandidate> {
    try {
      const roleCandidate = await this.roleCandidateRepository.findOne({
        where: { id: roleCandidateId },
        relations: ['candidate', 'role'],
      });

      if (!roleCandidate) {
        throw new NotFoundException('Role candidate not found');
      }

      // Update the LinkedIn connection send status
      roleCandidate.li_connection_send_status = connectionStatus;

      // Update response status if provided
      if (responseStatus) {
        roleCandidate.li_connection_response_status = responseStatus;
      }

      return await this.roleCandidateRepository.save(roleCandidate);
    } catch (error) {
      console.error('Error updating connection request status:', error);
      throw new InternalServerErrorException(
        'Failed to update connection request status',
      );
    }
  }

}
