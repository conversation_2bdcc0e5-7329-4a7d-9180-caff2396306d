import {
  IsString,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ption<PERSON>,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ProspectStatus } from 'src/people/dto/people.enums';

export class MailBoxDto {
  @ApiProperty({ description: 'Name of the mailbox', example: '<PERSON>' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Email address of the mailbox',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Type of the mailbox',
    example: 'INBOX',
    enum: [
      'INBOX',
      'SENT',
      'DRAFT',
      'TRASH',
      'SPAM',
      'ARCHIVED',
      'IMPORTANT',
      'STARRED',
      'UNREAD',
      'READ',
      'BOUNCE',
      'REPLIED',
      'FORWARDED',
      'DELETED',
      'BLOCKED',
      'UNSUBSCRIBED',
      'SPAM_REPORT',
      'PHISHING_REPORT',
    ],
  })
  @IsEnum([
    'INBOX',
    'SENT',
    'DRAFT',
    'TRASH',
    'SPAM',
    'ARCHIVED',
    'IMPORTANT',
    'STARRED',
    'UNREAD',
    'READ',
    'BOUNCE',
    'REPLIED',
    'FORWARDED',
    'DELETED',
    'BLOCKED',
    'UNSUBSCRIBED',
    'SPAM_REPORT',
    'PHISHING_REPORT',
  ])
  @IsNotEmpty()
  type: string;

  @ApiProperty({ description: 'Subject of the email', example: 'Hello World' })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiProperty({
    description: 'Message of the email',
    example: 'This is a test message',
  })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiProperty({
    description: 'Status of the email',
    example: 'sent',
    enum: ProspectStatus,
    default: ProspectStatus.CONTACTED,
  })
  @IsEnum(ProspectStatus)
  @IsOptional()
  status?: ProspectStatus;

  @ApiProperty({
    description: 'Attachment of the email',
    example: 'attachment.txt',
  })
  @IsString()
  @IsOptional()
  attachment?: string;

  @ApiProperty({
    description: 'Date of the email',
    example: '2023-10-01T00:00:00.000Z',
  })
  @IsString()
  @IsOptional()
  date?: string;

  @ApiProperty({ description: 'Sender of the email', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  sender?: string;

  @ApiProperty({
    description: 'Recipient of the email',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  recipient?: string;

  @ApiProperty({ description: 'CC of the email', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  cc?: string;

  @ApiProperty({ description: 'BCC of the email', example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  bcc?: string;

  @ApiProperty({
    description: 'Reply to of the email',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  reply_to?: string;

  @ApiProperty({
    description: 'Reason for status',
    example: 'User marked as spam',
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({
    description: 'Category of the email',
    example: 'Auto-Reply',
    enum: ['Auto-Reply', 'Potential Reply', 'Bounce Back', 'Unsubscribe'],
  })
  @IsEnum(['Auto-Reply', 'Potential Reply', 'Bounce Back', 'Unsubscribe'])
  @IsOptional()
  category?: string;

  @ApiProperty({
    description: 'Replacement email address',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  replacement_email?: string;

  @ApiProperty({
    description: 'Read status by user',
    example: false,
  })
  @IsOptional()
  read_by_user?: boolean;

  @ApiProperty({
    description: 'Read status by system',
    example: false,
  })
  @IsOptional()
  read_by_system?: boolean;
}

export class UpdateMailBoxDto extends MailBoxDto {
  @ApiProperty({ description: 'ID of the mailbox', example: 1 })
  @IsNotEmpty()
  id: number;
}
