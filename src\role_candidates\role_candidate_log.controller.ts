import { Body, Controller, Get, Param, Post, Delete } from '@nestjs/common';
import { RoleCandidateLogService } from './role_candidate_log.service';
import { RoleCandidateLog } from './role_candidate_log.entity';
import { CreateRoleCandidateLogDto } from './dto/create-role-candidate-log.dto';

@Controller('role-candidate-logs')
export class RoleCandidateLogController {
  constructor(private readonly logService: RoleCandidateLogService) {}

  @Post()
  async createLog(@Body() log: CreateRoleCandidateLogDto) {
    return this.logService.createLog(log);
  }

  @Get('role-candidate/:roleCandidateId')
  async getLogsByRoleCandidate(@Param('roleCandidateId') roleCandidateId: number) {
    return this.logService.getLogsByRoleCandidate(roleCandidateId);
  }

  @Get()
  async getAllLogs() {
    return this.logService.getAllLogs();
  }

  @Delete(':id')
  async deleteLog(@Param('id') id: number) {
    return this.logService.deleteLog(id);
  }
}
