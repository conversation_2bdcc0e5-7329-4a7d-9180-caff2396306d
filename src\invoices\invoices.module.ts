import { Module } from '@nestjs/common';
import { InvoicesService } from './invoices.service';
import { InvoicesController } from './invoices.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Invoices } from './invoices.entity';
import { StripeService } from 'src/stripe/stripe.service';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Invoices, Service, Users, People])],
  providers: [InvoicesService, StripeService],
  controllers: [InvoicesController],
})
export class InvoicesModule {}
