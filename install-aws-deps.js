const { execSync } = require('child_process');

console.log('Installing missing AWS SDK dependencies...');

try {
  // Install the missing AWS SDK packages
  execSync('npm install @aws-sdk/s3-request-presigner@^3.758.0 @aws-sdk/lib-storage@^3.758.0', { 
    stdio: 'inherit',
    cwd: __dirname 
  });
  
  console.log('✅ AWS SDK dependencies installed successfully!');
  console.log('You can now start the backend with: npm run start:dev');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  console.log('\n💡 Please run manually:');
  console.log('npm install @aws-sdk/s3-request-presigner@^3.758.0 @aws-sdk/lib-storage@^3.758.0');
}
