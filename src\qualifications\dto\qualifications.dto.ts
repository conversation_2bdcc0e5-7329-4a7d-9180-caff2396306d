import { IsNotEmpty, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class QualificationsDto {
  @ApiProperty({
    example: 'Bachelor of Science in Computer Science',
    description: 'The name of the title or qualification',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    example: '2023-01-01',
    description: 'The date the qualification was issued',
  })
  @IsString()
  @IsNotEmpty()
  start_date: string;

  @ApiProperty({
    example: '2023-01-01',
    description: 'The date the qualification was issued',
  })
  @IsString()
  @IsNotEmpty()
  end_date: string;

  @ApiProperty({
    example: 'Harvard University',
    description: 'The institution that issued the qualification',
  })
  @IsString()
  @IsNotEmpty()
  institution: string;

  @ApiProperty({
    example: 'CERTIFICATE',
    description:
      'The type of qualification (e.g., CERTIFICATE, DIPLOMA, DEGREE)',
  })
  @IsString()
  @IsNotEmpty()
  qualification_type: string;

  @ApiProperty({
    example: 'A brief description of the qualification',
    description: 'A brief description of the qualification',
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    example: false,
    description: 'Whether the qualification is verified',
  })
  @IsOptional()
  is_verified: boolean;
  @ApiProperty({
    example: false,
    description: 'Whether the qualification is current',
  })
  @IsOptional()
  is_current: boolean;
  @ApiProperty({
    example: '2025-01-01',
    description: 'The date the qualification expires',
  })
  @IsString()
  @IsOptional()
  date_expiry: string;

  @ApiProperty({
    example: 'Harvard University',
    description: 'The entity that issued the qualification',
  })
  @IsString()
  @IsOptional()
  issued_by: string;

  @ApiProperty({
    example: 1,
    description: 'The user id of the user that owns the qualification',
    type: Number,
  })
  @IsNumber()
  @IsOptional()
  personId: number;
}
