import { IsInt, IsString, IsDateString, IsOptional } from 'class-validator';

export class ScrapperStatsDto {
  @IsDateString()
  date: string;

  @IsInt()
  totalClicks: number;

  @IsInt()
  matchingCriteria: number;

  @IsInt()
  unmatchingCriteria: number;

  @IsInt()
  directCompanies: number;

  @IsInt()
  snrCompanies: number;

  @IsInt()
  existingCompanies: number;

  @IsInt()
  existingDirectCompanies: number;

  @IsInt()
  existingSnrCompanies: number;

  @IsInt()
  newCompanies: number;

  @IsInt()
  newDirectCompanies: number;

  @IsInt()
  newSnrCompanies: number;

  @IsString()
  region: string;

  @IsInt()
  loginErrors: number;

  @IsInt()
  jobClickErrors: number;

  @IsInt()
  NoMembersErrors: number;

  @IsOptional()
  scrapper_profile_name: string;
}
