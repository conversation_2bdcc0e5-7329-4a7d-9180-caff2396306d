import { Invoices } from 'src/invoices/invoices.entity';
import { People } from 'src/people/people.entity';
import { Roles } from 'src/roles/roles.entity';
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';

@Entity()
export class Service {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    nullable: false,
  })
  name: string;

  @Column({
    nullable: true,
  })
  description: string;

  @Column({
    nullable: true,
  })
  identity_number_start: number;

  @Column({
    nullable: true,
  })
  identity_number_end: number;

  @Column({
    nullable: true,
  })
  trial_number_fixed: number;

  @Column({
    nullable: true,
  })
  status: string;

  @Column({
    nullable: false,
  })
  service_tag: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @OneToMany(() => People, (people) => people.service)
  people: People[];

  @OneToMany(() => Roles, (roles) => roles.service)
  roles: Roles[];

  @OneToMany(() => Invoices, (invoices) => invoices.service)
  invoices: Invoices[];
}
