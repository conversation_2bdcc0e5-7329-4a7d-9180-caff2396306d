import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { Users } from 'src/users/users.entity';
import {
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('role_sequence')
export class RoleSequence {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  description: string;

  @Column({ type: 'varchar', nullable: true })
  status: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => Users, (user) => user.roleSequence, { nullable: true })
  user: Users;

  @Column({ nullable: true })
  userId: string;

  @OneToMany(
    () => SequenceSteps,
    (sequenceSteps) => sequenceSteps.roleSequence,
    { nullable: true },
  )
  sequenceSteps: SequenceSteps[];
}
