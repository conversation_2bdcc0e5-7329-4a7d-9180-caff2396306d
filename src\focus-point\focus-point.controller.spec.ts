import { Test, TestingModule } from '@nestjs/testing';
import { FocusPointController } from './focus-point.controller';

describe('FocusPointController', () => {
  let controller: FocusPointController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FocusPointController],
    }).compile();

    controller = module.get<FocusPointController>(FocusPointController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
