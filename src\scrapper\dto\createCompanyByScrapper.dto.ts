import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateCompanyByScrapperDto {
  @IsOptional()
  public_id?: string;

  @IsOptional()
  company_id?: string;

  @IsOptional()
  profile_url?: string;

  @IsOptional()
  profile_url_encoded?: string;

  @IsOptional()
  name?: string;

  @IsOptional()
  logo?: string;

  @IsOptional()
  cover?: string;

  @IsOptional()
  address?: string;

  @IsOptional()
  phone_number?: string;

  @IsOptional()
  website?: string;

  @IsOptional()
  tagline?: string;

  @IsOptional()
  staff_count?: number;

  @IsOptional()
  staff_count_range_start?: number;

  @IsOptional()
  staff_count_range_end?: number;

  @IsOptional()
  follower_count?: number;

  @IsOptional()
  actual_range?: number;

  @IsOptional()
  description?: string;

  @IsOptional()
  founded_on?: string;

  @IsOptional()
  headquarter_country?: string;

  @IsOptional()
  headquarter_city?: string;

  @IsOptional()
  headquarter_geographic_area?: string;

  @IsOptional()
  headquarter_line1?: string;

  @IsOptional()
  headquarter_line2?: string;

  @IsOptional()
  headquarter_postal_code?: string;

  @IsOptional()
  industry?: string;

  @IsOptional()
  specialities?: string[];

  @IsOptional()
  company_email?: string;

  @IsOptional()
  is_scrapped_fully?: boolean;

  @IsOptional()
  scrapper_level?: number;

  @IsOptional()
  user_id: string;

  @IsOptional()
  sector_id: number;

  @IsOptional()
  region: string;

  @IsOptional()
  scrapper_profile_name: string;
}

export class UpdateCompanyByScrapperDto extends CreateCompanyByScrapperDto {
  @IsNotEmpty()
  id: number;
}
