import { Test, TestingModule } from '@nestjs/testing';
import { WebsiteManualRequestsController } from './website_manual_requests.controller';

describe('WebsiteManualRequestsController', () => {
  let controller: WebsiteManualRequestsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebsiteManualRequestsController],
    }).compile();

    controller = module.get<WebsiteManualRequestsController>(WebsiteManualRequestsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
