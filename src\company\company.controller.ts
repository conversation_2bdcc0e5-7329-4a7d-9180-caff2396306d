import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { CompanyService } from './company.service';
import {
  AddCompanyWithLinksDto,
  CreateCompanyDto,
} from './dto/createCompnay.entity';
import { UpdateCompanyDTO } from './dto/updateCompany.dto';
import {
  GetAllCompaniesDto,
  GetCompaniesDto,
} from 'src/jobs/dto/createJob.dto';

@Controller('company')
@ApiTags('company')
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new company' })
  async createCompany(@Body() company: CreateCompanyDto) {
    return this.companyService.createCompany(company);
  }

  @Post('addCompanyWithLinks')
  @ApiOperation({ summary: 'Add companies with links' })
  async addCompanyWithLinks(@Body() linksData: AddCompanyWithLinksDto) {
    return this.companyService.addCompanyWithLinks(linksData);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a company' })
  async updateCompany(
    @Param('id') id: number,
    @Body() company: UpdateCompanyDTO,
  ) {
    return this.companyService.updateCompany(id, company);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a company' })
  async deleteCompany(@Param('id') id: number) {
    return this.companyService.deleteCompany(id);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all companies' })
  async findAll() {
    return this.companyService.findAll();
  }

  @Get('getAllCompanies')
  @ApiOperation({ summary: 'Get all companies with query' })
  async getAllCompanies(@Query() queryParams: GetAllCompaniesDto) {
    return this.companyService.getAllCompanies(queryParams);
  }

  @Get('getCompanies')
  @ApiOperation({ summary: 'Get all companies with query' })
  async getCompanies(@Query() queryParams: GetCompaniesDto) {
    return this.companyService.getCompanies(queryParams);
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Get a company by id' })
  async findOne(@Param('id') id: number) {
    return this.companyService.findOne(id);
  }

  @Get('find/name')
  @ApiOperation({ summary: 'Get a company by name' })
  async findByName(@Query('name') name: string) {
    return this.companyService.findByName(name);
  }

  @Get('find/public_id')
  @ApiOperation({ summary: 'Get a company by public_id' })
  async findByPublicId(@Query('public_id') public_id: string) {
    return this.companyService.findByPublicId(public_id);
  }

  @Get('find/profile_url')
  @ApiOperation({ summary: 'Get a company by profile_url' })
  async findByProfileUrl(@Query('profile_url') profile_url: string) {
    return this.companyService.findByProfileUrl(profile_url);
  }

  @Get('getUniqueIndustriesFromCompanies')
  @ApiOperation({ summary: 'Get unique industries from companies' })
  async getUniqueIndustriesFromCompanies() {
    return this.companyService.getUniqueIndustriesFromCompanies();
  }
  @Get('searchCompanies')
  @ApiOperation({ summary: 'Search companies' })
  async searchCompanies(@Query('searchTerm') searchTerm: string) {
    return this.companyService.searchCompanies(searchTerm);
  }
}
