import { Injectable, Logger } from '@nestjs/common';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { SequenceService } from 'src/sequence/sequence.service';
import { SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import {
  EmailWebhookPayload,
  WhatsAppWebhookPayload,
  SmsWebhookPayload,
  CallWebhookPayload,
  LinkedInWebhookPayload,
} from './webhooks.controller';

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);

  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    private readonly sequenceService: SequenceService,
  ) {}

  async processEmailWebhook(payload: EmailWebhookPayload): Promise<void> {
    const { candidateId, stepId, event, responseData } = payload;

    try {
      // Find the candidate sequence status
      const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
        candidateId,
        0, // We'll filter by stepId
      );
      const status = statuses.find(s => s.stepId === stepId);

      if (!status) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      // Update status based on event
      let newStatus: SequenceStepStatus;
      switch (event) {
        case 'delivered':
          newStatus = SequenceStepStatus.DELIVERED;
          break;
        case 'opened':
          newStatus = SequenceStepStatus.OPENED;
          break;
        case 'clicked':
          newStatus = SequenceStepStatus.CLICKED;
          break;
        case 'replied':
          newStatus = SequenceStepStatus.REPLIED;
          // Mark as completed when replied
          await this.candidateSequenceStatusService.markStepCompleted(
            candidateId,
            stepId,
            JSON.stringify(responseData),
          );
          // Trigger next steps
          await this.sequenceService.processNextSteps(candidateId, stepId);
          return;
        case 'bounced':
        case 'spam':
          newStatus = SequenceStepStatus.FAILED;
          break;
        default:
          this.logger.warn(`Unknown email event: ${event}`);
          return;
      }

      await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
        webhookEvent: event,
        webhookTimestamp: payload.timestamp,
        webhookData: responseData,
      });

      this.logger.log(`Email webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process email webhook: ${error.message}`);
      throw error;
    }
  }

  async processWhatsAppWebhook(payload: WhatsAppWebhookPayload): Promise<void> {
    const { candidateId, stepId, event, responseData } = payload;

    try {
      const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
        candidateId,
        0,
      );
      const status = statuses.find(s => s.stepId === stepId);

      if (!status) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      let newStatus: SequenceStepStatus;
      switch (event) {
        case 'delivered':
          newStatus = SequenceStepStatus.DELIVERED;
          break;
        case 'read':
          newStatus = SequenceStepStatus.OPENED;
          break;
        case 'replied':
          newStatus = SequenceStepStatus.REPLIED;
          await this.candidateSequenceStatusService.markStepCompleted(
            candidateId,
            stepId,
            JSON.stringify(responseData),
          );
          await this.sequenceService.processNextSteps(candidateId, stepId);
          return;
        default:
          this.logger.warn(`Unknown WhatsApp event: ${event}`);
          return;
      }

      await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
        webhookEvent: event,
        webhookTimestamp: payload.timestamp,
        webhookData: responseData,
      });

      this.logger.log(`WhatsApp webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process WhatsApp webhook: ${error.message}`);
      throw error;
    }
  }

  async processSmsWebhook(payload: SmsWebhookPayload): Promise<void> {
    const { candidateId, stepId, event, responseData } = payload;

    try {
      const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
        candidateId,
        0,
      );
      const status = statuses.find(s => s.stepId === stepId);

      if (!status) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      let newStatus: SequenceStepStatus;
      switch (event) {
        case 'delivered':
          newStatus = SequenceStepStatus.DELIVERED;
          break;
        case 'replied':
          newStatus = SequenceStepStatus.REPLIED;
          await this.candidateSequenceStatusService.markStepCompleted(
            candidateId,
            stepId,
            JSON.stringify(responseData),
          );
          await this.sequenceService.processNextSteps(candidateId, stepId);
          return;
        case 'failed':
          newStatus = SequenceStepStatus.FAILED;
          break;
        default:
          this.logger.warn(`Unknown SMS event: ${event}`);
          return;
      }

      await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
        webhookEvent: event,
        webhookTimestamp: payload.timestamp,
        webhookData: responseData,
      });

      this.logger.log(`SMS webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process SMS webhook: ${error.message}`);
      throw error;
    }
  }

  async processCallWebhook(payload: CallWebhookPayload): Promise<void> {
    const { candidateId, stepId, event, responseData } = payload;

    try {
      const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
        candidateId,
        0,
      );
      const status = statuses.find(s => s.stepId === stepId);

      if (!status) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      let newStatus: SequenceStepStatus;
      switch (event) {
        case 'answered':
          newStatus = SequenceStepStatus.COMPLETED;
          await this.candidateSequenceStatusService.markStepCompleted(
            candidateId,
            stepId,
            JSON.stringify(responseData),
          );
          await this.sequenceService.processNextSteps(candidateId, stepId);
          return;
        case 'no_answer':
        case 'busy':
          newStatus = SequenceStepStatus.DELIVERED; // Call was attempted
          break;
        case 'failed':
          newStatus = SequenceStepStatus.FAILED;
          break;
        default:
          this.logger.warn(`Unknown call event: ${event}`);
          return;
      }

      await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
        webhookEvent: event,
        webhookTimestamp: payload.timestamp,
        webhookData: responseData,
      });

      this.logger.log(`Call webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process call webhook: ${error.message}`);
      throw error;
    }
  }

  async processLinkedInWebhook(payload: LinkedInWebhookPayload): Promise<void> {
    const { candidateId, stepId, event, responseData } = payload;

    try {
      const statuses = await this.candidateSequenceStatusService.getCandidateSequenceStatus(
        candidateId,
        0,
      );
      const status = statuses.find(s => s.stepId === stepId);

      if (!status) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      let newStatus: SequenceStepStatus;
      switch (event) {
        case 'delivered':
          newStatus = SequenceStepStatus.DELIVERED;
          break;
        case 'viewed':
          newStatus = SequenceStepStatus.OPENED;
          break;
        case 'replied':
        case 'connection_accepted':
          newStatus = SequenceStepStatus.REPLIED;
          await this.candidateSequenceStatusService.markStepCompleted(
            candidateId,
            stepId,
            JSON.stringify(responseData),
          );
          await this.sequenceService.processNextSteps(candidateId, stepId);
          return;
        default:
          this.logger.warn(`Unknown LinkedIn event: ${event}`);
          return;
      }

      await this.candidateSequenceStatusService.updateStatus(status.id, newStatus, {
        webhookEvent: event,
        webhookTimestamp: payload.timestamp,
        webhookData: responseData,
      });

      this.logger.log(`LinkedIn webhook processed: candidate ${candidateId}, step ${stepId}, event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process LinkedIn webhook: ${error.message}`);
      throw error;
    }
  }

  async processTestWebhook(
    candidateId: number,
    stepId: number,
    event: string,
    medium: string,
  ): Promise<void> {
    this.logger.log(`Processing test webhook: candidate ${candidateId}, step ${stepId}, event ${event}, medium ${medium}`);

    // Simulate webhook processing based on medium
    const testPayload = {
      candidateId,
      stepId,
      event,
      timestamp: new Date().toISOString(),
      responseData: { test: true, medium, event },
    };

    switch (medium.toLowerCase()) {
      case 'email':
        await this.processEmailWebhook(testPayload as EmailWebhookPayload);
        break;
      case 'whatsapp':
        await this.processWhatsAppWebhook(testPayload as WhatsAppWebhookPayload);
        break;
      case 'sms':
        await this.processSmsWebhook(testPayload as SmsWebhookPayload);
        break;
      case 'call':
        await this.processCallWebhook(testPayload as CallWebhookPayload);
        break;
      case 'linkedin':
        await this.processLinkedInWebhook(testPayload as LinkedInWebhookPayload);
        break;
      default:
        throw new Error(`Unsupported medium for test webhook: ${medium}`);
    }
  }
}
