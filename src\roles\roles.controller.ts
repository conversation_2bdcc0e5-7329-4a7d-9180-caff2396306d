import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { RolesService } from './roles.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { RoleDto } from './dto/roles.dto';
import { UpdateRoleDto } from './dto/updateRole.dto';
import { AddTrialFromWebsiteDto } from './dto/addTrialFromWebsite.dto';

@Controller('roles')
@ApiTags('Roles Management')
export class RolesController {
  constructor(
    private readonly rolesService: RolesService, // Inject the RolesService to use its methods
  ) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new role' })
  @ApiBody({ type: RoleDto })
  @ApiBearerAuth()
  @ApiBadRequestResponse({ description: 'Role already exists' })
  async createRole(@Body() role: RoleDto) {
    return this.rolesService.createRole(role);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a role' })
  @ApiBody({ type: UpdateRoleDto })
  @ApiBearerAuth()
  async updateRole(@Param('id') id: number, @Body() role: UpdateRoleDto) {
    return this.rolesService.updateRole(id, role);
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Find a role by ID' })
  @ApiBearerAuth()
  async findRoleById(@Param('id') id: number) {
    return this.rolesService.findRoleById(id);
  }

  @Get('find')
  @ApiOperation({ summary: 'Find roles by title' })
  @ApiQuery({ name: 'title', required: false })
  @ApiBearerAuth()
  async findRoleByTitle(@Query('title') title: string) {
    return this.rolesService.findRoleByTitle(title);
  }

  @Get('findByCategory')
  @ApiOperation({ summary: 'Find roles by category' })
  @ApiQuery({ name: 'category', required: false })
  @ApiBearerAuth()
  async findRoleByCategory(@Query('category') category: string) {
    return this.rolesService.findRoleByCategory(category);
  }

  @Get('findByService')
  @ApiOperation({ summary: 'Find roles by service ID' })
  @ApiQuery({ name: 'serviceId', required: false })
  @ApiBearerAuth()
  async findRoleByService(@Query('serviceId') serviceId: number) {
    return this.rolesService.findRoleByService(serviceId);
  }

  @Get('service/stats')
  @ApiOperation({ summary: 'Get role statistics by service ID' })
  @ApiQuery({ name: 'serviceId', required: false })
  @ApiQuery({ name: 'start_date', required: false })
  @ApiQuery({ name: 'end_date', required: false })
  @ApiBearerAuth()
  async getServiceStats(
    @Query('serviceId') serviceId: number,
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
  ) {
    return this.rolesService.getRoleServiceStats(
      start_date,
      end_date,
      serviceId,
    );
  }

  @Get('ByServiceOrCategory')
  @ApiOperation({ summary: 'Find roles by service ID or category' })
  @ApiQuery({ name: 'serviceId', required: false })
  @ApiQuery({ name: 'category', required: true })
  @ApiBearerAuth()
  async findRoleByServiceOrCategory(
    @Query('serviceId') serviceId: number,
    @Query('category') category: string,
  ) {
    return this.rolesService.findRoleByCategoryAndService(category, serviceId);
  }

  @Get('totalTrialRoles')
  @ApiOperation({ summary: 'Get total trial roles' })
  @ApiQuery({ name: 'start_date', required: false })
  @ApiQuery({ name: 'end_date', required: false })
  @ApiQuery({ name: 'isAdvance', required: false })
  @ApiQuery({ name: 'isPrevious', required: false })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ['cvsourcing', 'preQualification', 'direct', 'trial'],
  }) // New type filter
  @ApiQuery({ name: 'bdUserId', required: false })
  @ApiQuery({ name: 'serviceId', required: false })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'acmUserId', required: false })
  @ApiQuery({ name: 'roleId', required: false })
  @ApiQuery({ name: 'searchString', required: false })
  @ApiQuery({ name: 'roleNumber', required: false })
  @ApiQuery({ name: 'clientNumber', required: false })
  @ApiBearerAuth()
  async getTotalTrialRoles(
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('isAdvance') isAdvance: boolean,
    @Query('isPrevious') isPrevious: boolean,
    @Query('type') type: 'cvsourcing' | 'preQualification' | 'direct' | 'trial', // New type filter
    @Query('bdUserId') bdUserId: string,
    @Query('serviceId') serviceId: number,
    @Query('userId') userId: string,
    @Query('acmUserId') acmUserId: string,
    @Query('roleId') roleId: number,
    @Query('searchString') searchString: string,
    @Query('roleNumber') roleNumber: string,
    @Query('clientNumber') clientNumber: string,
  ) {
    return this.rolesService.findAllTrialRoles(
      start_date,
      end_date,
      isAdvance,
      isPrevious,
      type,
      bdUserId,
      serviceId,
      userId,
      acmUserId,
      roleId,
      searchString,
      roleNumber,
      clientNumber,
    );
  }

  @Get('findCvSourcingRoles')
  @ApiOperation({ summary: 'Find CV Sourcing Roles' })
  @ApiQuery({ name: 'isAdvance', required: false, type: Boolean })
  @ApiQuery({ name: 'isPrevious', required: false, type: Boolean })
  @ApiQuery({ name: 'start_date', required: false })
  @ApiQuery({ name: 'end_date', required: false })
  async findCvSourcingRoles(
    @Query('isAdvance') isAdvance: string,
    @Query('isPrevious') isPrevious: string,
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
  ) {
    const parsedIsAdvance = isAdvance === 'true';
    const parsedIsPrevious = isPrevious === 'true';

    return this.rolesService.findCvSourcingRoles(
      parsedIsAdvance,
      parsedIsPrevious,
      start_date,
      end_date,
    );
  }

  @Get('find360_PreQualificationRoles')
  @ApiOperation({ summary: 'Find 360 PreQualification Roles' })
  @ApiQuery({ name: 'isAdvance', required: false })
  @ApiQuery({ name: 'isPrevious', required: false })
  @ApiQuery({ name: 'start_date', required: false })
  @ApiQuery({ name: 'end_date', required: false })
  @ApiQuery({ name: 'serviceId', required: false })
  @ApiBearerAuth()
  async find360_PreQualificationRoles(
    @Query('isAdvance') isAdvance: string,
    @Query('isPrevious') isPrevious: string,
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('serviceId') serviceId: number,
  ) {
    const parsedIsAdvance = isAdvance === 'true';
    const parsedIsPrevious = isPrevious === 'true';
    return this.rolesService.find360_PreQualificationRoles(
      parsedIsAdvance,
      parsedIsPrevious,
      start_date,
      end_date,
      serviceId,
    );
  }

  @Post('addRoleFromWebsite')
  @ApiOperation({ summary: 'Add a role from the website' })
  @ApiBody({ type: AddTrialFromWebsiteDto })
  @ApiBearerAuth()
  async addRoleFromWebsite(@Body() role: AddTrialFromWebsiteDto) {
    return this.rolesService.addTrialFromWebsite(role);
  }
}
