import { Test, TestingModule } from '@nestjs/testing';
import { RoleCandidatesController } from './role_candidates.controller';
import { RoleCandidatesService } from './role_candidates.service';
import { UpdateConnectionStatusDto } from './dto/update-connection-status.dto';

describe('RoleCandidatesController', () => {
  let controller: RoleCandidatesController;
  let service: RoleCandidatesService;

  const mockRoleCandidatesService = {
    markProfileReadyForConnection: jest.fn(),
    getOneReadyToSendConnectionProfile: jest.fn(),
    updateConnectionRequestStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleCandidatesController],
      providers: [
        {
          provide: RoleCandidatesService,
          useValue: mockRoleCandidatesService,
        },
      ],
    }).compile();

    controller = module.get<RoleCandidatesController>(RoleCandidatesController);
    service = module.get<RoleCandidatesService>(RoleCandidatesService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('markProfileReadyForConnection', () => {
    it('should mark profile as ready for connection', async () => {
      const roleCandidateId = 1;
      const mockResult = { id: 1, li_connection_send_status: 'READY_TO_SEND' };

      jest.spyOn(service, 'markProfileReadyForConnection').mockResolvedValue(mockResult as any);

      const result = await controller.markProfileReadyForConnection(roleCandidateId);

      expect(service.markProfileReadyForConnection).toHaveBeenCalledWith(roleCandidateId);
      expect(result).toEqual(mockResult);
    });
  });

  describe('getOneReadyToSendConnectionProfile', () => {
    it('should get one ready to send connection profile', async () => {
      const mockResult = { id: 1, li_connection_send_status: 'SENT' };

      jest.spyOn(service, 'getOneReadyToSendConnectionProfile').mockResolvedValue(mockResult as any);

      const result = await controller.getOneReadyToSendConnectionProfile();

      expect(service.getOneReadyToSendConnectionProfile).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });
  });

  describe('updateConnectionRequestStatus', () => {
    it('should update connection request status', async () => {
      const roleCandidateId = 1;
      const updateDto: UpdateConnectionStatusDto = {
        connectionStatus: 'SENT',
        responseStatus: 'Accepted',
      };
      const mockResult = {
        id: 1,
        li_connection_send_status: 'SENT',
        li_connection_response_status: 'Accepted'
      };

      jest.spyOn(service, 'updateConnectionRequestStatus').mockResolvedValue(mockResult as any);

      const result = await controller.updateConnectionRequestStatus(roleCandidateId, updateDto);

      expect(service.updateConnectionRequestStatus).toHaveBeenCalledWith(
        roleCandidateId,
        updateDto.connectionStatus,
        updateDto.responseStatus,
      );
      expect(result).toEqual(mockResult);
    });
  });
});
